@echo off
title SIMPLE SUPPORT TOOLS FIX - Exactly What's Needed
color 0A

echo.
echo  ==========================================
echo   🎯 SIMPLE SUPPORT TOOLS FIX
echo   Exactly What Support Tools.exe Needs
echo  ==========================================
echo.
echo  This will fix the 5 exact issues:
echo  1. Real-Time Protection is enabled
echo  2. Error checking Firewall status  
echo  3. Unknown Check Apps and Files status
echo  4. Secure Boot is enabled (software prep)
echo  5. Virtualization not enabled (software prep)
echo.

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  🎯 FIXING SUPPORT TOOLS ISSUES...
    echo.
    
    echo  [1/5] Disabling Real-Time Protection...
    powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $true" >nul 2>&1
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender" /v DisableAntiSpyware /t REG_DWORD /d 1 /f >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Real-Time Protection: DISABLED) else (echo  ❌ Real-Time Protection: FAILED)
    
    echo.
    echo  [2/5] Fixing Firewall Status Check...
    reg add "HKLM\SYSTEM\CurrentControlSet\Services\MpsSvc" /v Start /t REG_DWORD /d 2 /f >nul 2>&1
    sc config MpsSvc start= auto >nul 2>&1
    net start MpsSvc >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Firewall Status Check: FIXED) else (echo  ❌ Firewall Status Check: FAILED)
    
    echo.
    echo  [3/5] Fixing SmartScreen RequireAdmin Status...
    reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v SmartScreenEnabled /t REG_SZ /d RequireAdmin /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\System" /v EnableSmartScreen /t REG_DWORD /d 1 /f >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ SmartScreen RequireAdmin: FIXED) else (echo  ❌ SmartScreen RequireAdmin: FAILED)
    
    echo.
    echo  [4/5] Disabling Secure Boot Software...
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\SecureBoot\State" /v UEFISecureBootEnabled /t REG_DWORD /d 0 /f >nul 2>&1
    bcdedit /set {bootmgr} secureboot off >nul 2>&1
    bcdedit /set {current} secureboot off >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Secure Boot Software: PREPARED) else (echo  ❌ Secure Boot Software: FAILED)
    echo  [INFO] BIOS change still required for complete disable
    
    echo.
    echo  [5/5] Enabling Virtualization Software...
    dism /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart >nul 2>&1
    dism /online /enable-feature /featurename:Microsoft-Hyper-V-All /all /norestart >nul 2>&1
    bcdedit /set hypervisorlaunchtype auto >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Virtualization Software: PREPARED) else (echo  ❌ Virtualization Software: FAILED)
    echo  [INFO] BIOS change still required for hardware enable
    
    echo.
    echo  ==========================================
    echo   ✅ SOFTWARE FIXES COMPLETE!
    echo  ==========================================
    echo.
    echo  📋 BIOS CHANGES STILL REQUIRED:
    echo.
    echo  1. DISABLE SECURE BOOT:
    echo     - Enter BIOS (F2, F12, DEL, or ESC during boot)
    echo     - Go to Security or Boot section
    echo     - Find Secure Boot option
    echo     - Set to DISABLED
    echo.
    echo  2. ENABLE VIRTUALIZATION:
    echo     - In BIOS, go to Advanced or CPU Configuration
    echo     - Find Intel VT-x or Virtualization Technology
    echo     - Set to ENABLED
    echo.
    echo  3. SAVE AND EXIT:
    echo     - Press F10 to save changes
    echo     - Confirm Yes to save
    echo.
    echo  💡 After BIOS changes, run Support Tools.exe again!
    echo     All 5 checks should now pass.
    echo.
    
    choice /c YN /m "Would you like to restart and enter BIOS now? (Y/N)"
    if errorlevel 2 goto :manual_bios
    if errorlevel 1 goto :auto_bios
    
    :auto_bios
    echo.
    echo  🚀 RESTARTING TO BIOS IN 10 SECONDS...
    echo.
    echo  ⚠️ REMEMBER: Make these 2 changes in BIOS:
    echo  1. Secure Boot = DISABLED
    echo  2. Virtualization = ENABLED
    echo  3. Save and Exit (F10)
    echo.
    
    timeout /t 10
    shutdown /r /fw /t 0
    goto :end
    
    :manual_bios
    echo.
    echo  📋 MANUAL BIOS ACCESS:
    echo  1. Restart computer manually
    echo  2. Press F2, F12, DEL, or ESC during boot
    echo  3. Make the 2 BIOS changes listed above
    echo  4. Save and exit
    echo  5. Run Support Tools.exe to verify success
    echo.
    goto :end
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator to fix
    echo  the issues detected by Support Tools.exe
    echo.
    echo  HOW TO RUN AS ADMINISTRATOR:
    echo  ===========================
    echo  1. Right-click this file (SIMPLE_SUPPORT_TOOLS_FIX.bat)
    echo  2. Select "Run as administrator"
    echo  3. Click "Yes" when prompted by UAC
    echo.
    
    choice /c YN /m "Would you like to restart with administrator privileges? (Y/N)"
    if errorlevel 2 goto :end
    if errorlevel 1 goto :restart_admin
    
    :restart_admin
    echo.
    echo  🔄 Restarting with administrator privileges...
    
    REM Try to restart with admin privileges
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    goto :end
)

:end
echo.
echo  Press any key to exit...
pause >nul
