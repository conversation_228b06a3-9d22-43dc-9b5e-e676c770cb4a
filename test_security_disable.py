#!/usr/bin/env python3
"""
Test Security Disable
Quick test of the updated security disable functionality
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'modules'))

from security_config import SecurityConfig

def test_security_disable():
    print("🔧 TESTING UPDATED SECURITY DISABLE")
    print("=" * 50)
    
    # Create security config instance
    security = SecurityConfig()
    
    # Check admin privileges
    if not security.is_admin():
        print("❌ Administrator privileges required!")
        print("Please run this script as administrator.")
        input("Press Enter to exit...")
        return
    
    print("✅ Administrator privileges confirmed")
    print()
    
    # Ask for confirmation
    confirm = input("Test aggressive security disable? (type 'YES' to proceed): ")
    if confirm != "YES":
        print("Test cancelled.")
        return
    
    print("\n🚀 Testing aggressive security disable...")
    print("This will show real-time results...")
    print()
    
    # Run the disable
    success = security.disable_all_security()
    
    # Show all results
    print("\n📊 DETAILED RESULTS:")
    print("-" * 30)
    for result in security.get_config_results():
        print(result)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 SECURITY DISABLE TEST: SUCCESS!")
        print("Most security features should now be disabled.")
    else:
        print("⚠️ SECURITY DISABLE TEST: PARTIAL SUCCESS")
        print("Some features may still be active.")
    
    print("\n💡 Run 'python check_security_status.py' to verify actual status.")
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    test_security_disable()
