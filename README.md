# Support Tools Interface - Clean Version

This is a clean, focused interface that ONLY handles the specific requirements from Support Tools.exe.

## What it does:

### ✅ Real-Time Protection
- **Must be DISABLED** for Support Tools.exe to work
- Click "Disable" button to turn off Windows Defender Real-Time Protection

### ✅ Windows Firewall  
- **Must be ENABLED** for Support Tools.exe to work
- Click "Enable" button to turn on Windows Firewall

### ✅ Apps and Files Check
- **Must work properly** for Support Tools.exe
- Click "Fix" button to repair SmartScreen settings

### ⚠️ BIOS Settings (Manual Required)
- **Secure Boot**: Must be DISABLED
- **Virtualization**: Must be ENABLED
- Click "Instructions" button for step-by-step BIOS guide

## How to use:

### 🎮 One-Click Gaming Mode:
1. **Run as Administrator**: Double-click `run_support_tools.bat`
2. **Click "Gaming Mode"**: Automatically configures all Support Tools.exe requirements
3. **Follow BIOS instructions**: Manual steps for Secure Boot/Virtualization
4. **Test**: Run Support Tools.exe to verify all checks pass

### 🛡️ One-Click Normal Mode:
1. **Click "Normal Mode"**: Instantly restores all security settings to safe defaults
2. **Your system is fully protected again**

### 🔧 Manual Control:
1. **Check current status**: All settings show current state
2. **Fix individual items**: Click buttons to fix each requirement separately
3. **BIOS settings**: Follow manual instructions (cannot be automated)

## Requirements:

- Windows 10/11
- Python 3.7+ installed (download from python.org)
- Administrator privileges

## Installation:

### Option 1: Automatic (Recommended)
1. **Double-click `run_support_tools.bat`**
2. **Dependencies install automatically** if missing
3. **Interface launches** when ready

### Option 2: Manual
1. **Double-click `install_dependencies.bat`** to install Python packages
2. **Then run `run_support_tools.bat`** to launch interface

### Option 3: Command Line
```bash
pip install psutil requests
python support_tools_interface.py
```

## What this does NOT do:

- No complex system modifications
- No registry hacking beyond what's needed
- No dangerous operations
- No factory reset risk
- Only touches the 4 specific Support Tools.exe requirements

## Support Tools.exe Requirements:

Based on your Support Tools.exe output, this interface handles:

1. ❌ Real-Time Protection is enabled → **DISABLE**
2. ❌ Firewall Status: Private/Public Disabled → **ENABLE** 
3. ❌ Unknown Check Apps and Files status → **FIX**
4. ❌ Virtualization not enabled in BIOS → **MANUAL**
5. ❌ Secure Boot is enabled → **MANUAL**

After using this interface and following BIOS instructions, Support Tools.exe should show all green checkmarks.
