# Gaming Tool Setup Automation System

A comprehensive system with interface that can complete all gaming tool setup tasks automatically.

## Features

### 🧹 System Cleanup Module
- **Anti-cheat Software Removal**: Automatically detects and removes FaceIT, ESEA, Riot Vanguard, BattlEye, EasyAntiCheat, and VAC
- **Cracking Tools Cleanup**: Removes Cheat Engine, x64dbg, OllyDbg, WinDbg, IDA Pro, and other debugging tools
- **Security Software Uninstall**: Removes Windows Defender, Avast, AVG, Norton, McAfee, Kaspersky, and other antivirus software
- **Process Termination**: Automatically terminates running processes that may interfere
- **Registry Cleanup**: Cleans up leftover registry entries

### 🔒 Windows Security Configuration
- **Windows Defender**: Disables real-time protection, cloud protection, behavior monitoring
- **Windows Firewall**: Disables all firewall profiles (Domain, Public, Private)
- **SmartScreen**: Disables Windows SmartScreen protection
- **User Account Control**: Disables UAC prompts
- **Windows Update**: Disables automatic updates and related services
- **Exploit Protection**: Disables DEP, SEHOP, ASLR, and other exploit protections
- **Fast Boot**: Disables fast boot and hibernation
- **Telemetry**: Disables Windows telemetry and diagnostic services

### ⚡ System Settings Optimizer
- **Display Scaling**: Sets Windows display scaling to 100%
- **Overlay Disabling**: Disables Xbox Game Bar, NVIDIA GeForce, AMD Radeon, Discord, and Steam overlays
- **Power Settings**: Sets power plan to High Performance, disables USB selective suspend
- **Visual Effects**: Optimizes Windows visual effects for performance
- **Background Apps**: Disables unnecessary background applications

### 📦 Dependency Installer
- **WinRAR**: Downloads and installs WinRAR for archive extraction
- **Visual C++ Redistributable**: Installs Microsoft Visual C++ Runtime
- **Notepad++**: Downloads and installs advanced text editor
- **Defender Control**: Downloads and extracts Windows Defender control tool
- **Windows Update Blocker**: Downloads and extracts Windows Update blocking tool
- **WinFIX Script**: Creates custom system optimization script

### 🎮 Loader Interface
- **Secure Authentication**: License key validation system
- **Configuration Management**: Menu key settings, spoofing options, auto-inject settings
- **File Selection**: Browse and select loader files
- **HWID Spoofing**: Hardware ID spoofing functionality
- **Injection Control**: Manual and automatic injection options
- **Status Monitoring**: Real-time status logging and monitoring

### 🎯 Game Integration Module
- **Game Detection**: Automatically detects Call of Duty: Black Ops 6, Warzone, and Modern Warfare
- **Process Monitoring**: Real-time monitoring of game processes
- **Auto-Injection**: Automatic injection when games are detected
- **Installation Scanning**: Scans Steam, Battle.net, and custom game directories
- **Shortcut Creation**: Creates desktop shortcuts for detected games

### 🛠️ Troubleshooting System
- **Menu Close Issues**: Fixes menu that won't close by deleting COD players folder
- **Game Crashes**: Fixes crashes by removing STZ files and clearing cache
- **Injection Failures**: Resolves injection failures by disabling interfering processes
- **Menu Not Appearing**: Fixes menu visibility issues and overlay conflicts
- **Performance Issues**: Optimizes system performance for gaming
- **Antivirus Blocking**: Adds exclusions and temporarily disables protection
- **Auto-Fix**: Automatically runs all fixes in sequence
- **System Scan**: Scans for common issues and provides recommendations

## Installation & Usage

### Prerequisites
- Windows 10/11
- Python 3.8 or higher
- Administrator privileges

### Quick Start
1. **Download** all files to a folder
2. **Right-click** on `launcher.bat` and select **"Run as administrator"**
3. The system will automatically install dependencies and start the interface
4. Follow the on-screen instructions to complete setup

### Manual Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Run as administrator
python main.py
```

## Usage Instructions

### Full Automated Setup
1. Click **"Start Full Setup"** to run all modules automatically
2. Monitor progress through the progress bar and status log
3. Complete BIOS configuration manually when prompted
4. Restart computer when setup is complete

### Individual Module Usage
- **System Cleanup**: Click "1. System Cleanup" to remove interfering software
- **Security Configuration**: Click "3. Security Configuration" to disable Windows security
- **System Optimization**: Click "4. System Optimization" to optimize performance
- **Install Dependencies**: Click "5. Install Dependencies" to download required tools
- **Loader Interface**: Click "6. Loader Interface" to open the injection interface
- **Game Integration**: Click "7. Game Integration" to setup game detection
- **Troubleshooting**: Click "8. Troubleshooting" to fix common issues

### Loader Interface Usage
1. Open the loader interface from the main menu
2. Enter your license key and click "Authenticate"
3. Configure settings (menu key, spoofing, auto-inject)
4. Select your loader file using "Browse"
5. Click "Inject" to start the injection process
6. Launch your game and press the menu key (default: INSERT)

### Troubleshooting Common Issues

#### Menu Won't Close
- Use the troubleshooter's "Unable to close menu" fix
- This deletes the COD players folder and requires a restart

#### Game Crashes
- Use the "Game crashes on injection" fix
- This removes STZ files from C: drive and clears game cache

#### Injection Fails
- Use the "Injection fails" fix
- This disables interfering processes and antivirus temporarily

#### Menu Not Appearing
- Ensure game is in Fullscreen Borderless mode
- Try pressing INSERT key multiple times
- Use the "Menu not appearing" fix to disable overlays

## File Structure
```
gaming-tool-interface/
├── main.py                 # Main application entry point
├── launcher.bat           # Windows launcher script
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── modules/              # Core modules
│   ├── __init__.py
│   ├── system_cleanup.py      # System cleanup functionality
│   ├── security_config.py     # Windows security configuration
│   ├── system_optimizer.py    # System optimization
│   ├── dependency_installer.py # Dependency management
│   ├── loader_interface.py    # Loader interface and injection
│   ├── game_integration.py    # Game detection and integration
│   └── troubleshooter.py      # Troubleshooting system
└── loader_config.json    # Loader configuration (created at runtime)
```

## Security Notice

⚠️ **Important Security Information**:
- This system disables Windows security features for gaming purposes
- Only use on dedicated gaming systems
- Re-enable security features after gaming sessions
- Use at your own risk and responsibility

## System Requirements

- **OS**: Windows 10/11 (64-bit)
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space for tools and cache
- **Network**: Internet connection for downloading dependencies
- **Privileges**: Administrator access required

## Supported Games

- Call of Duty: Black Ops 6
- Call of Duty: Warzone
- Call of Duty: Modern Warfare
- (Additional games can be added to the configuration)

## Configuration

The system creates configuration files automatically:
- `loader_config.json`: Stores loader interface settings
- Game paths and settings are detected automatically

## Troubleshooting

If you encounter issues:
1. Run the built-in troubleshooter
2. Check the status logs for error messages
3. Ensure you're running as Administrator
4. Verify all dependencies are installed
5. Restart your computer if changes don't take effect

## License

This software is provided as-is for educational and gaming purposes. Use responsibly and in accordance with your local laws and game terms of service.

---

**Note**: This system is designed for legitimate gaming enhancement purposes. Always respect game terms of service and anti-cheat policies.
