#!/usr/bin/env python3
"""
System Settings Optimizer Module
Configure display scaling, disable overlays, adjust game settings, and optimize system performance
"""

import os
import sys
import subprocess
import winreg
import ctypes
from ctypes import wintypes
import psutil
import json
from pathlib import Path

class SystemOptimizer:
    def __init__(self):
        self.optimization_results = []
        
    def set_display_scaling(self, scale_percent=100):
        """Set Windows display scaling to specified percentage"""
        try:
            # Calculate DPI value (96 DPI = 100% scaling)
            dpi_value = int(96 * (scale_percent / 100))
            
            # Set registry values for display scaling
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                              r"Control Panel\Desktop", 
                              0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "LogPixels", 0, winreg.REG_DWORD, dpi_value)
                winreg.SetValueEx(key, "Win8DpiScaling", 0, winreg.REG_DWORD, 1)
            
            # Set per-monitor DPI awareness
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, 
                                r"Control Panel\Desktop\PerMonitorSettings") as key:
                winreg.SetValueEx(key, "DpiValue", 0, winreg.REG_DWORD, dpi_value)
            
            self.optimization_results.append(f"✅ Display scaling set to {scale_percent}%")
            return True
            
        except Exception as e:
            self.optimization_results.append(f"❌ Display scaling error: {str(e)}")
            return False
    
    def disable_game_bar(self):
        """Disable Xbox Game Bar and Game Mode"""
        try:
            # Disable Game Bar
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, 
                                r"SOFTWARE\Microsoft\Windows\CurrentVersion\GameDVR") as key:
                winreg.SetValueEx(key, "AppCaptureEnabled", 0, winreg.REG_DWORD, 0)
                winreg.SetValueEx(key, "GameDVR_Enabled", 0, winreg.REG_DWORD, 0)
                winreg.SetValueEx(key, "HistoricalCaptureEnabled", 0, winreg.REG_DWORD, 0)
            
            # Disable Game Mode
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, 
                                r"SOFTWARE\Microsoft\GameBar") as key:
                winreg.SetValueEx(key, "AllowAutoGameMode", 0, winreg.REG_DWORD, 0)
                winreg.SetValueEx(key, "AutoGameModeEnabled", 0, winreg.REG_DWORD, 0)
            
            # Disable Game Bar via Group Policy
            with winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, 
                                r"SOFTWARE\Policies\Microsoft\Windows\GameDVR") as key:
                winreg.SetValueEx(key, "AllowGameDVR", 0, winreg.REG_DWORD, 0)
            
            self.optimization_results.append("✅ Xbox Game Bar disabled")
            return True
            
        except Exception as e:
            self.optimization_results.append(f"❌ Game Bar disable error: {str(e)}")
            return False
    
    def disable_nvidia_overlay(self):
        """Disable NVIDIA GeForce Experience overlay"""
        try:
            # NVIDIA overlay registry settings
            nvidia_paths = [
                r"SOFTWARE\NVIDIA Corporation\NvContainer\Feature\NvShare",
                r"SOFTWARE\NVIDIA Corporation\Global\FTS",
            ]
            
            success_count = 0
            for path in nvidia_paths:
                try:
                    with winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, path) as key:
                        winreg.SetValueEx(key, "FeatureEnabled", 0, winreg.REG_DWORD, 0)
                    success_count += 1
                except:
                    continue
            
            # Try to disable via NVIDIA settings file
            nvidia_settings_paths = [
                Path(os.path.expanduser("~/AppData/Local/NVIDIA Corporation/GeForce Experience/Settings.json")),
                Path("C:/ProgramData/NVIDIA Corporation/GeForce Experience/Settings.json")
            ]
            
            for settings_path in nvidia_settings_paths:
                if settings_path.exists():
                    try:
                        with open(settings_path, 'r') as f:
                            settings = json.load(f)
                        
                        # Disable overlay in settings
                        if 'ShadowPlay' in settings:
                            settings['ShadowPlay']['Enabled'] = False
                        if 'Overlay' in settings:
                            settings['Overlay']['Enabled'] = False
                        
                        with open(settings_path, 'w') as f:
                            json.dump(settings, f, indent=2)
                        
                        success_count += 1
                    except:
                        continue
            
            if success_count > 0:
                self.optimization_results.append("✅ NVIDIA overlay disabled")
                return True
            else:
                self.optimization_results.append("⚠️  NVIDIA overlay not found or already disabled")
                return True
                
        except Exception as e:
            self.optimization_results.append(f"❌ NVIDIA overlay error: {str(e)}")
            return False
    
    def disable_amd_overlay(self):
        """Disable AMD Radeon overlay"""
        try:
            # AMD overlay registry settings
            amd_paths = [
                r"SOFTWARE\AMD\CN\OverlayNotification",
                r"SOFTWARE\AMD\DVR",
            ]
            
            success_count = 0
            for path in amd_paths:
                try:
                    with winreg.CreateKey(winreg.HKEY_CURRENT_USER, path) as key:
                        winreg.SetValueEx(key, "Enabled", 0, winreg.REG_DWORD, 0)
                    success_count += 1
                except:
                    continue
            
            if success_count > 0:
                self.optimization_results.append("✅ AMD overlay disabled")
            else:
                self.optimization_results.append("⚠️  AMD overlay not found or already disabled")
            return True
            
        except Exception as e:
            self.optimization_results.append(f"❌ AMD overlay error: {str(e)}")
            return False
    
    def disable_discord_overlay(self):
        """Disable Discord overlay"""
        try:
            discord_paths = [
                Path(os.path.expanduser("~/AppData/Roaming/discord/settings.json")),
                Path(os.path.expanduser("~/AppData/Roaming/discordcanary/settings.json")),
                Path(os.path.expanduser("~/AppData/Roaming/discordptb/settings.json"))
            ]
            
            success_count = 0
            for settings_path in discord_paths:
                if settings_path.exists():
                    try:
                        with open(settings_path, 'r') as f:
                            settings = json.load(f)
                        
                        # Disable overlay
                        settings['OVERLAY_ENABLED'] = False
                        settings['OVERLAY_ENABLED_2'] = False
                        
                        with open(settings_path, 'w') as f:
                            json.dump(settings, f, indent=2)
                        
                        success_count += 1
                    except:
                        continue
            
            if success_count > 0:
                self.optimization_results.append("✅ Discord overlay disabled")
            else:
                self.optimization_results.append("⚠️  Discord overlay not found or already disabled")
            return True
            
        except Exception as e:
            self.optimization_results.append(f"❌ Discord overlay error: {str(e)}")
            return False
    
    def disable_steam_overlay(self):
        """Disable Steam overlay"""
        try:
            steam_paths = [
                Path("C:/Program Files (x86)/Steam/config/config.vdf"),
                Path("C:/Program Files/Steam/config/config.vdf"),
                Path(os.path.expanduser("~/Steam/config/config.vdf"))
            ]
            
            success_count = 0
            for config_path in steam_paths:
                if config_path.exists():
                    try:
                        with open(config_path, 'r') as f:
                            content = f.read()
                        
                        # Replace overlay settings
                        content = content.replace('"InGameOverlayEnabled"\t\t"1"', '"InGameOverlayEnabled"\t\t"0"')
                        content = content.replace('"InGameOverlayEnabled"		"1"', '"InGameOverlayEnabled"		"0"')
                        
                        with open(config_path, 'w') as f:
                            f.write(content)
                        
                        success_count += 1
                    except:
                        continue
            
            if success_count > 0:
                self.optimization_results.append("✅ Steam overlay disabled")
            else:
                self.optimization_results.append("⚠️  Steam overlay not found or already disabled")
            return True
            
        except Exception as e:
            self.optimization_results.append(f"❌ Steam overlay error: {str(e)}")
            return False
    
    def optimize_power_settings(self):
        """Set power plan to High Performance"""
        try:
            # Set power plan to High Performance
            result = subprocess.run(
                'powercfg /setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c',
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                self.optimization_results.append("✅ Power plan set to High Performance")
            else:
                self.optimization_results.append("⚠️  Could not set High Performance power plan")
            
            # Disable USB selective suspend
            subprocess.run(
                'powercfg /setacvalueindex scheme_current 2a737441-1930-4402-8d77-b2bebba308a3 48e6b7a6-50f5-4782-a5d4-53bb8f07e226 0',
                shell=True
            )
            subprocess.run(
                'powercfg /setdcvalueindex scheme_current 2a737441-1930-4402-8d77-b2bebba308a3 48e6b7a6-50f5-4782-a5d4-53bb8f07e226 0',
                shell=True
            )
            
            self.optimization_results.append("✅ USB selective suspend disabled")
            return True
            
        except Exception as e:
            self.optimization_results.append(f"❌ Power settings error: {str(e)}")
            return False
    
    def optimize_visual_effects(self):
        """Optimize Windows visual effects for performance"""
        try:
            # Set visual effects to "Adjust for best performance"
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                              r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects", 
                              0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "VisualFXSetting", 0, winreg.REG_DWORD, 2)
            
            # Disable specific visual effects
            visual_effects = [
                ("DragFullWindows", "0"),
                ("FontSmoothing", "0"),
                ("ListviewAlphaSelect", "0"),
                ("ListviewShadow", "0"),
                ("MenuAnimation", "0"),
                ("ComboBoxAnimation", "0"),
                ("TooltipAnimation", "0"),
                ("TaskbarAnimations", "0")
            ]
            
            with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                              r"Control Panel\Desktop", 
                              0, winreg.KEY_SET_VALUE) as key:
                for effect, value in visual_effects:
                    try:
                        winreg.SetValueEx(key, effect, 0, winreg.REG_SZ, value)
                    except:
                        continue
            
            self.optimization_results.append("✅ Visual effects optimized for performance")
            return True
            
        except Exception as e:
            self.optimization_results.append(f"❌ Visual effects error: {str(e)}")
            return False
    
    def disable_background_apps(self):
        """Disable unnecessary background apps"""
        try:
            # Disable background apps via registry
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, 
                                r"SOFTWARE\Microsoft\Windows\CurrentVersion\BackgroundAccessApplications") as key:
                winreg.SetValueEx(key, "GlobalUserDisabled", 0, winreg.REG_DWORD, 1)
            
            # Disable specific background apps
            background_apps = [
                "Microsoft.Windows.Photos_8wekyb3d8bbwe",
                "Microsoft.Windows.Calculator_8wekyb3d8bbwe",
                "Microsoft.WindowsStore_8wekyb3d8bbwe",
                "Microsoft.WindowsMaps_8wekyb3d8bbwe",
                "Microsoft.WindowsCamera_8wekyb3d8bbwe",
                "Microsoft.BingWeather_8wekyb3d8bbwe"
            ]
            
            for app in background_apps:
                try:
                    with winreg.CreateKey(winreg.HKEY_CURRENT_USER, 
                                        f"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\BackgroundAccessApplications\\{app}") as key:
                        winreg.SetValueEx(key, "Disabled", 0, winreg.REG_DWORD, 1)
                        winreg.SetValueEx(key, "DisabledByUser", 0, winreg.REG_DWORD, 1)
                except:
                    continue
            
            self.optimization_results.append("✅ Background apps disabled")
            return True
            
        except Exception as e:
            self.optimization_results.append(f"❌ Background apps error: {str(e)}")
            return False
    
    def optimize_all(self):
        """Run all optimization tasks"""
        self.optimization_results = []
        self.optimization_results.append("=== Starting System Optimization ===")
        
        # Execute all optimization functions
        tasks = [
            (lambda: self.set_display_scaling(100), "Display Scaling (100%)"),
            (self.disable_game_bar, "Xbox Game Bar"),
            (self.disable_nvidia_overlay, "NVIDIA Overlay"),
            (self.disable_amd_overlay, "AMD Overlay"),
            (self.disable_discord_overlay, "Discord Overlay"),
            (self.disable_steam_overlay, "Steam Overlay"),
            (self.optimize_power_settings, "Power Settings"),
            (self.optimize_visual_effects, "Visual Effects"),
            (self.disable_background_apps, "Background Apps"),
        ]
        
        success_count = 0
        for task_func, task_name in tasks:
            self.optimization_results.append(f"\n--- Optimizing {task_name} ---")
            try:
                if task_func():
                    success_count += 1
                    self.optimization_results.append(f"✅ {task_name} optimization completed")
                else:
                    self.optimization_results.append(f"❌ {task_name} optimization failed")
            except Exception as e:
                self.optimization_results.append(f"❌ {task_name} error: {str(e)}")
        
        self.optimization_results.append(f"\n=== System Optimization Complete ===")
        self.optimization_results.append(f"Successfully optimized {success_count}/{len(tasks)} settings")
        
        if success_count == len(tasks):
            self.optimization_results.append("🎉 All optimizations completed successfully!")
        else:
            self.optimization_results.append("⚠️  Some optimizations may require manual intervention")
        
        self.optimization_results.append("⚠️  RESTART RECOMMENDED for optimal performance")
        
        return success_count >= len(tasks) * 0.8  # 80% success rate
    
    def get_optimization_results(self):
        """Get the results of the last optimization operation"""
        return self.optimization_results
