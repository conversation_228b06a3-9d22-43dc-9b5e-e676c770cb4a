#!/usr/bin/env python3
"""
Verify Support Tools Fixes
Test if our fixes resolve the Support Tools.exe issues
"""

import subprocess
import sys

class SupportToolsVerifier:
    def __init__(self):
        self.results = []
        
    def log(self, message):
        """Safe logging"""
        try:
            safe_message = str(message).encode('ascii', 'ignore').decode('ascii')
            print(safe_message)
            self.results.append(safe_message)
        except:
            print("[VERIFY] Test completed")
            self.results.append("[VERIFY] Test completed")
    
    def run_command(self, command, timeout=30):
        """Run command safely"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=timeout,
                encoding='utf-8',
                errors='ignore'
            )
            return result.returncode == 0, result.stdout, result.stderr
        except Exception as e:
            return False, "", str(e)
    
    def is_admin(self):
        """Check admin privileges"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def test_secure_boot_status(self):
        """Test Secure Boot status"""
        self.log("TESTING SECURE BOOT STATUS...")
        
        # Test via PowerShell
        success, stdout, stderr = self.run_command('powershell.exe -Command "try { Confirm-SecureBootUEFI } catch { Write-Host \'SecureBoot-Disabled-Or-NotSupported\' }"')
        
        if "True" in stdout:
            self.log("[RESULT] Secure Boot: ENABLED (may cause gaming tool issues)")
            return False
        elif "False" in stdout or "SecureBoot-Disabled-Or-NotSupported" in stdout:
            self.log("[RESULT] Secure Boot: DISABLED (good for gaming tools)")
            return True
        else:
            self.log("[RESULT] Secure Boot: STATUS UNKNOWN")
            return False
    
    def test_virtualization_status(self):
        """Test Virtualization status"""
        self.log("TESTING VIRTUALIZATION STATUS...")
        
        # Test via systeminfo
        success, stdout, stderr = self.run_command('systeminfo')
        
        if "Hyper-V Requirements" in stdout:
            if "Virtualization Enabled In Firmware: Yes" in stdout:
                self.log("[RESULT] Virtualization: ENABLED (good for gaming tools)")
                return True
            else:
                self.log("[RESULT] Virtualization: DISABLED (may cause gaming tool issues)")
                return False
        else:
            self.log("[RESULT] Virtualization: STATUS UNKNOWN")
            return False
    
    def test_firewall_status(self):
        """Test Firewall status"""
        self.log("TESTING FIREWALL STATUS...")
        
        # Test firewall status
        success, stdout, stderr = self.run_command('netsh advfirewall show allprofiles state')
        
        if success and "State" in stdout:
            self.log("[RESULT] Firewall: STATUS CHECK WORKING (good)")
            return True
        else:
            self.log("[RESULT] Firewall: STATUS CHECK FAILED (error)")
            return False
    
    def test_realtime_protection_status(self):
        """Test Real-time Protection status"""
        self.log("TESTING REAL-TIME PROTECTION STATUS...")
        
        # Test Defender status
        success, stdout, stderr = self.run_command('powershell.exe -Command "Get-MpPreference | Select-Object DisableRealtimeMonitoring"')
        
        if success and ("True" in stdout or "False" in stdout):
            self.log("[RESULT] Real-time Protection: STATUS CHECK WORKING (good)")
            return True
        else:
            self.log("[RESULT] Real-time Protection: STATUS CHECK FAILED (error)")
            return False
    
    def run_comprehensive_test(self):
        """Run comprehensive test of all fixes"""
        if not self.is_admin():
            self.log("[ERROR] Administrator privileges required!")
            self.log("Right-click and 'Run as administrator'")
            return False
        
        self.log("SUPPORT TOOLS FIXES VERIFICATION")
        self.log("=" * 50)
        self.log("[INFO] Administrator privileges confirmed")
        self.log("")
        
        tests_passed = 0
        total_tests = 4
        
        # Test each component
        self.log("1/4 TESTING SECURE BOOT...")
        if self.test_secure_boot_status():
            tests_passed += 1
        self.log("")
        
        self.log("2/4 TESTING VIRTUALIZATION...")
        if self.test_virtualization_status():
            tests_passed += 1
        self.log("")
        
        self.log("3/4 TESTING FIREWALL...")
        if self.test_firewall_status():
            tests_passed += 1
        self.log("")
        
        self.log("4/4 TESTING REAL-TIME PROTECTION...")
        if self.test_realtime_protection_status():
            tests_passed += 1
        self.log("")
        
        # Summary
        self.log("=" * 50)
        self.log("VERIFICATION SUMMARY")
        self.log("=" * 50)
        self.log(f"[RESULT] {tests_passed}/{total_tests} tests passed")
        
        if tests_passed >= 3:
            self.log("[SUCCESS] Support Tools issues should be resolved!")
            self.log("[INFO] Run Support Tools.exe to verify")
            return True
        else:
            self.log("[WARNING] Some issues may still exist")
            self.log("[INFO] Run FIX_SUPPORT_TOOLS_ISSUES.bat for repairs")
            return False

def main():
    print("SUPPORT TOOLS FIXES VERIFICATION")
    print("=" * 50)
    print("This will test if our fixes resolve Support Tools.exe issues")
    print("")
    
    verifier = SupportToolsVerifier()
    
    if not verifier.is_admin():
        print("ERROR: Administrator privileges required!")
        print("Right-click this script and 'Run as administrator'")
        input("Press Enter to exit...")
        return
    
    print("Starting verification tests...")
    print("")
    
    success = verifier.run_comprehensive_test()
    
    print("")
    print("=" * 50)
    print("VERIFICATION COMPLETED")
    print("=" * 50)
    
    if success:
        print("SUCCESS: Support Tools issues should be resolved!")
        print("")
        print("NEXT STEPS:")
        print("1. Run Support Tools.exe to verify")
        print("2. All checks should now pass")
        print("3. Gaming tools should work properly")
    else:
        print("WARNING: Some issues may still exist")
        print("")
        print("RECOMMENDED ACTIONS:")
        print("1. Run FIX_SUPPORT_TOOLS_ISSUES.bat")
        print("2. Check BIOS_INSTRUCTIONS.txt for manual steps")
        print("3. Restart system and test again")
    
    print("")
    print("SAFETY REMINDER:")
    print("Emergency restore available via desktop shortcut")
    print("'🚨 EMERGENCY RESTORE GameBoost Pro'")
    
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
