@echo off
title COMPLETE SUPPORT TOOLS FIX - All Issues Resolution
color 0E

echo.
echo  ==========================================
echo   🎯 COMPLETE SUPPORT TOOLS FIX
echo   All Issues Resolution Workflow
echo  ==========================================
echo.
echo  This will fix ALL Support Tools.exe issues:
echo  • Secure Boot encoding errors
echo  • Virtualization encoding errors
echo  • Firewall status errors  
echo  • Real-time Protection errors
echo.
echo  PROCESS:
echo  1. Software fixes (automatic)
echo  2. Auto BIOS entry (automatic)
echo  3. BIOS changes (2 simple changes)
echo  4. Verification (automatic)
echo.

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  ==========================================
    echo   PHASE 1: SOFTWARE FIXES
    echo  ==========================================
    echo.
    echo  Applying all software-level fixes...
    
    echo  [1/6] Fixing Secure Boot registry...
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\SecureBoot\State" /v UEFISecureBootEnabled /t REG_DWORD /d 0 /f >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Secure Boot registry) else (echo  ❌ Secure Boot registry)
    
    echo  [2/6] Fixing boot configuration...
    bcdedit /set {bootmgr} secureboot off >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Boot configuration) else (echo  ❌ Boot configuration)
    
    echo  [3/6] Enabling virtualization features...
    dism /online /enable-feature /featurename:Microsoft-Hyper-V-All /all /norestart >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Hyper-V features) else (echo  ❌ Hyper-V features)
    
    echo  [4/6] Fixing firewall...
    netsh advfirewall reset >nul 2>&1
    sc config MpsSvc start= auto >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Firewall configuration) else (echo  ❌ Firewall configuration)
    
    echo  [5/6] Fixing Windows Defender...
    sc config WinDefend start= auto >nul 2>&1
    powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $false" >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Defender configuration) else (echo  ❌ Defender configuration)
    
    echo  [6/6] Creating BIOS guide...
    echo BIOS CHANGES NEEDED > BIOS_CHANGES_NEEDED.txt
    echo ================== >> BIOS_CHANGES_NEEDED.txt
    echo. >> BIOS_CHANGES_NEEDED.txt
    echo 1. DISABLE SECURE BOOT: >> BIOS_CHANGES_NEEDED.txt
    echo    Security ^> Secure Boot ^> Disabled >> BIOS_CHANGES_NEEDED.txt
    echo. >> BIOS_CHANGES_NEEDED.txt
    echo 2. ENABLE VIRTUALIZATION: >> BIOS_CHANGES_NEEDED.txt
    echo    Advanced ^> CPU Configuration ^> Enabled >> BIOS_CHANGES_NEEDED.txt
    echo. >> BIOS_CHANGES_NEEDED.txt
    echo 3. SAVE AND EXIT: >> BIOS_CHANGES_NEEDED.txt
    echo    Press F10 to save changes >> BIOS_CHANGES_NEEDED.txt
    echo  ✅ BIOS guide created
    
    echo.
    echo  ==========================================
    echo   PHASE 2: BIOS CONFIGURATION REQUIRED
    echo  ==========================================
    echo.
    echo  📋 MANUAL BIOS CHANGES NEEDED:
    echo.
    echo  🔒 CHANGE #1: DISABLE SECURE BOOT
    echo     • Go to Security tab
    echo     • Find "Secure Boot" 
    echo     • Change to "Disabled"
    echo.
    echo  🖥️ CHANGE #2: ENABLE VIRTUALIZATION
    echo     • Go to Advanced tab
    echo     • Find "CPU Configuration"
    echo     • Find "Intel VT-x" or "AMD-V"
    echo     • Change to "Enabled"
    echo.
    echo  💾 CHANGE #3: SAVE AND EXIT
    echo     • Press F10 to save
    echo     • Confirm "Yes"
    echo.
    echo  📄 Detailed guide: VISUAL_BIOS_GUIDE.md
    echo  📋 Simple checklist: SIMPLE_BIOS_CHECKLIST.txt
    echo.
    
    choice /c YN /m "Ready to boot to BIOS automatically? (Y/N)"
    if errorlevel 2 goto :manual_instructions
    if errorlevel 1 goto :auto_bios
    
    :auto_bios
    echo.
    echo  ==========================================
    echo   PHASE 3: AUTO BIOS ENTRY
    echo  ==========================================
    echo.
    echo  🚀 BOOTING TO BIOS AUTOMATICALLY...
    echo.
    echo  ⏰ System will restart in 60 seconds
    echo  🔧 Will boot DIRECTLY to BIOS setup
    echo  📝 Make the 2 changes listed above
    echo  💾 Save and exit when done
    echo.
    echo  ⚠️ REMEMBER IN BIOS:
    echo  1. Disable Secure Boot (Security section)
    echo  2. Enable Virtualization (Advanced section)  
    echo  3. Save and Exit (F10)
    echo.
    
    REM Auto boot to BIOS
    shutdown /r /fw /t 60
    
    if %errorLevel% == 0 (
        echo  ✅ AUTO BIOS BOOT SCHEDULED!
        echo.
        echo  After making BIOS changes:
        echo  • Save and Exit BIOS (F10)
        echo  • Let Windows boot normally
        echo  • Run Support Tools.exe to verify
        echo  • All checks should pass ✅
        echo.
        echo  🎯 SUCCESS: Complete workflow initiated!
    ) else (
        echo  ❌ Auto BIOS boot failed
        goto :manual_instructions
    )
    
    goto :end
    
    :manual_instructions
    echo.
    echo  ==========================================
    echo   MANUAL BIOS ACCESS INSTRUCTIONS
    echo  ==========================================
    echo.
    echo  📋 MANUAL STEPS:
    echo  1. Restart computer manually
    echo  2. Press F2, F12, DEL, or ESC during boot
    echo  3. Make the BIOS changes:
    echo     • Disable Secure Boot
    echo     • Enable Virtualization
    echo  4. Save and Exit (F10)
    echo  5. Run Support Tools.exe to verify
    echo.
    echo  📄 Check these files for detailed help:
    echo  • VISUAL_BIOS_GUIDE.md
    echo  • SIMPLE_BIOS_CHECKLIST.txt
    echo  • BIOS_CHANGES_NEEDED.txt
    echo.
    goto :end
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator.
    echo.
    echo  HOW TO RUN AS ADMINISTRATOR:
    echo  ===========================
    echo  1. Right-click this file
    echo  2. Select "Run as administrator"
    echo  3. Click "Yes" when prompted
    echo.
    
    choice /c YN /m "Restart with administrator privileges? (Y/N)"
    if errorlevel 2 goto :end
    if errorlevel 1 goto :restart_admin
    
    :restart_admin
    echo.
    echo  🔄 Restarting with administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    goto :end
)

:end
echo.
echo  ==========================================
echo   🎯 SUPPORT TOOLS FIX WORKFLOW
echo  ==========================================
echo.
echo  ✅ Software fixes completed
echo  📋 BIOS changes required (2 simple changes)
echo  📄 Detailed guides created
echo  🎮 Gaming tools will work after BIOS changes
echo.
pause
exit /b
