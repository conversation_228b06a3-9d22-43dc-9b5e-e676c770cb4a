#!/usr/bin/env python3
"""
Safety Manager Module
Implements safety measures to prevent system lockups and ensure recovery options
"""

import os
import sys
import subprocess
import winreg
import json
import time
import threading
from pathlib import Path
import shutil

class SafetyManager:
    def __init__(self):
        self.safety_results = []
        self.backup_dir = Path("C:/GamingToolBackup")
        self.backup_dir.mkdir(exist_ok=True)
        
        # Create restore points and backups
        self.registry_backups = {}
        self.service_states = {}
        self.original_settings = {}
        
    def create_system_restore_point(self):
        """Create a Windows System Restore Point before making changes"""
        try:
            self.safety_results.append("🛡️ Creating System Restore Point...")
            
            # Create restore point using PowerShell
            ps_command = '''
            Checkpoint-Computer -Description "Gaming Tool Setup - Before Changes" -RestorePointType "MODIFY_SETTINGS"
            '''
            
            result = subprocess.run(
                f'powershell.exe -Command "{ps_command}"',
                shell=True,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                self.safety_results.append("✅ System Restore Point created successfully")
                return True
            else:
                self.safety_results.append("⚠️ Could not create System Restore Point (continuing anyway)")
                return False
                
        except Exception as e:
            self.safety_results.append(f"⚠️ Restore Point error: {str(e)}")
            return False
    
    def backup_registry_key(self, hkey, path, backup_name):
        """Backup a registry key before modification"""
        try:
            backup_file = self.backup_dir / f"{backup_name}.reg"
            
            # Export registry key
            reg_command = f'reg export "{self.get_hkey_name(hkey)}\\{path}" "{backup_file}"'
            result = subprocess.run(reg_command, shell=True, capture_output=True)
            
            if result.returncode == 0:
                self.registry_backups[backup_name] = str(backup_file)
                self.safety_results.append(f"✅ Backed up registry: {backup_name}")
                return True
            else:
                self.safety_results.append(f"⚠️ Could not backup registry: {backup_name}")
                return False
                
        except Exception as e:
            self.safety_results.append(f"❌ Registry backup error: {str(e)}")
            return False
    
    def get_hkey_name(self, hkey):
        """Convert registry hkey to string name"""
        hkey_names = {
            winreg.HKEY_LOCAL_MACHINE: "HKEY_LOCAL_MACHINE",
            winreg.HKEY_CURRENT_USER: "HKEY_CURRENT_USER",
            winreg.HKEY_CLASSES_ROOT: "HKEY_CLASSES_ROOT",
            winreg.HKEY_USERS: "HKEY_USERS",
            winreg.HKEY_CURRENT_CONFIG: "HKEY_CURRENT_CONFIG"
        }
        return hkey_names.get(hkey, "HKEY_LOCAL_MACHINE")
    
    def backup_service_states(self):
        """Backup current service states"""
        try:
            self.safety_results.append("🛡️ Backing up service states...")
            
            # Services we might modify
            critical_services = [
                "WinDefend", "MpsSvc", "WdNisSvc", "SecurityHealthService",
                "wuauserv", "BITS", "DiagTrack", "dmwappushservice"
            ]
            
            for service in critical_services:
                try:
                    # Get service status
                    result = subprocess.run(
                        f'sc query {service}',
                        shell=True,
                        capture_output=True,
                        text=True
                    )
                    
                    if "RUNNING" in result.stdout:
                        self.service_states[service] = "running"
                    elif "STOPPED" in result.stdout:
                        self.service_states[service] = "stopped"
                    else:
                        self.service_states[service] = "unknown"
                        
                except:
                    self.service_states[service] = "unknown"
            
            # Save to file
            backup_file = self.backup_dir / "service_states.json"
            with open(backup_file, 'w') as f:
                json.dump(self.service_states, f, indent=2)
            
            self.safety_results.append(f"✅ Backed up {len(self.service_states)} service states")
            return True
            
        except Exception as e:
            self.safety_results.append(f"❌ Service backup error: {str(e)}")
            return False
    
    def create_emergency_restore_script(self):
        """Create emergency restore script for manual recovery"""
        try:
            restore_script = f'''@echo off
echo Emergency Gaming Tool Restore Script
echo ====================================
echo.
echo This script will restore your system to its previous state
echo.
pause

echo Restoring Windows Defender...
powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $false"
sc config WinDefend start= auto
sc start WinDefend

echo Restoring Windows Firewall...
netsh advfirewall set allprofiles state on

echo Restoring Windows Update...
sc config wuauserv start= auto
sc start wuauserv

echo Restoring registry backups...
'''
            
            # Add registry restore commands
            for backup_name, backup_file in self.registry_backups.items():
                restore_script += f'reg import "{backup_file}"\n'
            
            restore_script += '''
echo Restoring services...
'''
            
            # Add service restore commands
            for service, state in self.service_states.items():
                if state == "running":
                    restore_script += f'sc start {service}\n'
                elif state == "stopped":
                    restore_script += f'sc stop {service}\n'
            
            restore_script += '''
echo.
echo Restore completed! Please restart your computer.
echo.
pause
'''
            
            # Save restore script
            restore_file = self.backup_dir / "EMERGENCY_RESTORE.bat"
            with open(restore_file, 'w') as f:
                f.write(restore_script)
            
            # Also create on desktop for easy access
            desktop_restore = Path.home() / "Desktop" / "EMERGENCY_RESTORE_GAMING_TOOL.bat"
            shutil.copy2(restore_file, desktop_restore)
            
            self.safety_results.append(f"✅ Emergency restore script created on desktop")
            return True
            
        except Exception as e:
            self.safety_results.append(f"❌ Restore script error: {str(e)}")
            return False
    
    def create_safe_mode_instructions(self):
        """Create instructions for safe mode recovery"""
        try:
            instructions = '''
GAMING TOOL SAFE MODE RECOVERY INSTRUCTIONS
==========================================

If your system becomes unresponsive or you cannot boot normally:

1. BOOT INTO SAFE MODE:
   - Restart your computer
   - Press F8 repeatedly during boot (or Shift+F8 on newer systems)
   - Select "Safe Mode with Networking"

2. RUN EMERGENCY RESTORE:
   - Navigate to your Desktop
   - Run "EMERGENCY_RESTORE_GAMING_TOOL.bat" as Administrator
   - Follow the prompts to restore your system

3. MANUAL RESTORE (if script fails):
   - Open Command Prompt as Administrator
   - Run: sfc /scannow
   - Run: DISM /Online /Cleanup-Image /RestoreHealth
   - Run: powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $false"
   - Run: netsh advfirewall set allprofiles state on

4. SYSTEM RESTORE:
   - Type "rstrui" in Start menu
   - Select the restore point created before gaming tool setup
   - Follow the wizard to restore your system

5. LAST RESORT:
   - Boot from Windows installation media
   - Select "Repair your computer"
   - Choose "System Restore" or "Startup Repair"

IMPORTANT CONTACTS:
- Windows Support: support.microsoft.com
- System Recovery: Use Windows Recovery Environment (WinRE)

Created: {time.strftime("%Y-%m-%d %H:%M:%S")}
'''
            
            # Save instructions
            instructions_file = self.backup_dir / "SAFE_MODE_RECOVERY.txt"
            with open(instructions_file, 'w') as f:
                f.write(instructions)
            
            # Also save on desktop
            desktop_instructions = Path.home() / "Desktop" / "SAFE_MODE_RECOVERY_INSTRUCTIONS.txt"
            with open(desktop_instructions, 'w') as f:
                f.write(instructions)
            
            self.safety_results.append("✅ Safe mode recovery instructions created")
            return True
            
        except Exception as e:
            self.safety_results.append(f"❌ Instructions error: {str(e)}")
            return False
    
    def enable_safe_boot_option(self):
        """Enable safe boot option in boot menu"""
        try:
            self.safety_results.append("🛡️ Configuring safe boot options...")
            
            # Enable F8 boot menu
            subprocess.run('bcdedit /set {default} bootmenupolicy legacy', shell=True)
            
            # Set boot timeout
            subprocess.run('bcdedit /timeout 10', shell=True)
            
            self.safety_results.append("✅ Safe boot options enabled (F8 during startup)")
            return True
            
        except Exception as e:
            self.safety_results.append(f"❌ Safe boot error: {str(e)}")
            return False
    
    def create_system_checkpoint(self):
        """Create a comprehensive system checkpoint"""
        try:
            self.safety_results.append("🛡️ Creating comprehensive system checkpoint...")
            
            checkpoint_info = {
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "registry_backups": self.registry_backups,
                "service_states": self.service_states,
                "backup_directory": str(self.backup_dir),
                "system_info": {
                    "os_version": os.name,
                    "python_version": sys.version,
                    "user": os.getenv("USERNAME")
                }
            }
            
            checkpoint_file = self.backup_dir / "system_checkpoint.json"
            with open(checkpoint_file, 'w') as f:
                json.dump(checkpoint_info, f, indent=2)
            
            self.safety_results.append("✅ System checkpoint created")
            return True
            
        except Exception as e:
            self.safety_results.append(f"❌ Checkpoint error: {str(e)}")
            return False
    
    def setup_all_safety_measures(self):
        """Setup all safety measures before making system changes"""
        self.safety_results = []
        self.safety_results.append("🛡️ Setting up safety measures...")
        
        safety_tasks = [
            (self.create_system_restore_point, "System Restore Point"),
            (self.backup_service_states, "Service States Backup"),
            (self.create_emergency_restore_script, "Emergency Restore Script"),
            (self.create_safe_mode_instructions, "Safe Mode Instructions"),
            (self.enable_safe_boot_option, "Safe Boot Options"),
            (self.create_system_checkpoint, "System Checkpoint")
        ]
        
        success_count = 0
        for task_func, task_name in safety_tasks:
            try:
                if task_func():
                    success_count += 1
                    self.safety_results.append(f"✅ {task_name} completed")
                else:
                    self.safety_results.append(f"⚠️ {task_name} failed (continuing)")
            except Exception as e:
                self.safety_results.append(f"❌ {task_name} error: {str(e)}")
        
        self.safety_results.append(f"\n🛡️ Safety Setup Complete: {success_count}/{len(safety_tasks)} measures active")
        
        if success_count >= 4:  # At least 4 out of 6 safety measures
            self.safety_results.append("✅ System is protected - safe to proceed")
            return True
        else:
            self.safety_results.append("⚠️ Limited protection - proceed with caution")
            return False
    
    def quick_system_check(self):
        """Quick check to ensure system is responsive"""
        try:
            # Test basic system responsiveness
            start_time = time.time()
            
            # Test registry access
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion"):
                pass
            
            # Test file system
            test_file = self.backup_dir / "system_test.tmp"
            with open(test_file, 'w') as f:
                f.write("test")
            test_file.unlink()
            
            # Test process execution
            subprocess.run('echo test', shell=True, capture_output=True, timeout=5)
            
            response_time = time.time() - start_time
            
            if response_time < 2.0:
                self.safety_results.append(f"✅ System responsive ({response_time:.2f}s)")
                return True
            else:
                self.safety_results.append(f"⚠️ System slow ({response_time:.2f}s)")
                return False
                
        except Exception as e:
            self.safety_results.append(f"❌ System check failed: {str(e)}")
            return False
    
    def get_safety_results(self):
        """Get safety setup results"""
        return self.safety_results
