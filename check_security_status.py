#!/usr/bin/env python3
"""
Quick Security Status Check
Verify actual status of Windows security features
"""

import subprocess
import winreg

def check_windows_defender():
    """Check Windows Defender status"""
    print("🔍 CHECKING WINDOWS DEFENDER STATUS")
    print("-" * 40)
    
    try:
        # Check real-time protection
        result = subprocess.run(
            'powershell.exe -Command "Get-MpPreference | Select-Object DisableRealtimeMonitoring"',
            shell=True, capture_output=True, text=True
        )
        if "True" in result.stdout:
            print("✅ Real-time Protection: DISABLED")
        else:
            print("❌ Real-time Protection: STILL ACTIVE")
        
        # Check cloud protection
        result = subprocess.run(
            'powershell.exe -Command "Get-MpPreference | Select-Object MAPSReporting"',
            shell=True, capture_output=True, text=True
        )
        if "Disabled" in result.stdout:
            print("✅ Cloud Protection: DISABLED")
        else:
            print("❌ Cloud Protection: STILL ACTIVE")
        
        # Check behavior monitoring
        result = subprocess.run(
            'powershell.exe -Command "Get-MpPreference | Select-Object DisableBehaviorMonitoring"',
            shell=True, capture_output=True, text=True
        )
        if "True" in result.stdout:
            print("✅ Behavior Monitoring: DISABLED")
        else:
            print("❌ Behavior Monitoring: STILL ACTIVE")
            
    except Exception as e:
        print(f"❌ Error checking Defender: {str(e)}")

def check_windows_firewall():
    """Check Windows Firewall status"""
    print("\n🔥 CHECKING WINDOWS FIREWALL STATUS")
    print("-" * 40)
    
    try:
        profiles = ["Domain", "Public", "Private"]
        for profile in profiles:
            result = subprocess.run(
                f'powershell.exe -Command "Get-NetFirewallProfile -Profile {profile} | Select-Object Enabled"',
                shell=True, capture_output=True, text=True
            )
            if "False" in result.stdout:
                print(f"✅ {profile} Firewall: DISABLED")
            else:
                print(f"❌ {profile} Firewall: STILL ACTIVE")
                
    except Exception as e:
        print(f"❌ Error checking Firewall: {str(e)}")

def check_uac():
    """Check UAC status"""
    print("\n🛡️ CHECKING UAC STATUS")
    print("-" * 25)
    
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                          r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System") as key:
            value = winreg.QueryValueEx(key, "EnableLUA")[0]
            if value == 0:
                print("✅ User Account Control: DISABLED")
            else:
                print("❌ User Account Control: STILL ACTIVE")
    except Exception as e:
        print(f"❌ Error checking UAC: {str(e)}")

def check_smartscreen():
    """Check SmartScreen status"""
    print("\n🔒 CHECKING SMARTSCREEN STATUS")
    print("-" * 30)
    
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                          r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer") as key:
            value = winreg.QueryValueEx(key, "SmartScreenEnabled")[0]
            if value == "Off":
                print("✅ SmartScreen: DISABLED")
            else:
                print("❌ SmartScreen: STILL ACTIVE")
    except Exception as e:
        print(f"❌ Error checking SmartScreen: {str(e)}")

def check_fast_boot():
    """Check Fast Boot status"""
    print("\n⚡ CHECKING FAST BOOT STATUS")
    print("-" * 30)
    
    try:
        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                          r"SYSTEM\CurrentControlSet\Control\Session Manager\Power") as key:
            value = winreg.QueryValueEx(key, "HiberbootEnabled")[0]
            if value == 0:
                print("✅ Fast Boot: DISABLED")
            else:
                print("❌ Fast Boot: STILL ACTIVE")
    except Exception as e:
        print(f"❌ Error checking Fast Boot: {str(e)}")

def check_windows_update():
    """Check Windows Update status"""
    print("\n🔄 CHECKING WINDOWS UPDATE STATUS")
    print("-" * 35)
    
    try:
        result = subprocess.run('sc query wuauserv', shell=True, capture_output=True, text=True)
        if "STOPPED" in result.stdout:
            print("✅ Windows Update Service: STOPPED")
        else:
            print("❌ Windows Update Service: STILL RUNNING")
    except Exception as e:
        print(f"❌ Error checking Windows Update: {str(e)}")

def run_manual_security_disable():
    """Run manual security disable commands"""
    print("\n🔧 ATTEMPTING MANUAL SECURITY DISABLE")
    print("-" * 45)
    print("This requires administrator privileges...")
    
    commands = [
        ("Set-MpPreference -DisableRealtimeMonitoring $true", "Disable Defender Real-time"),
        ("Set-MpPreference -MAPSReporting Disabled", "Disable Defender Cloud"),
        ("Set-MpPreference -DisableBehaviorMonitoring $true", "Disable Behavior Monitoring"),
        ("Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled False", "Disable All Firewalls"),
    ]
    
    for command, description in commands:
        try:
            result = subprocess.run(
                f'powershell.exe -Command "{command}"',
                shell=True, capture_output=True, text=True
            )
            if result.returncode == 0:
                print(f"✅ {description}: SUCCESS")
            else:
                print(f"❌ {description}: FAILED - {result.stderr.strip()}")
        except Exception as e:
            print(f"❌ {description}: ERROR - {str(e)}")

def main():
    print("🔍 SECURITY STATUS VERIFICATION")
    print("=" * 50)
    print("Checking actual status of Windows security features...")
    print()
    
    # Check all security features
    check_windows_defender()
    check_windows_firewall()
    check_uac()
    check_smartscreen()
    check_fast_boot()
    check_windows_update()
    
    print("\n" + "=" * 50)
    print("📊 SECURITY STATUS SUMMARY")
    print("=" * 50)
    
    # Ask if user wants to try manual disable
    print("\nIf any features are still active, we can try manual disable.")
    choice = input("Would you like to attempt manual security disable? (y/n): ").lower().strip()
    
    if choice == 'y':
        run_manual_security_disable()
        print("\nRe-checking status after manual disable...")
        check_windows_defender()
        check_windows_firewall()
    
    print("\n💡 NOTE:")
    print("Some changes may require:")
    print("• Administrator privileges")
    print("• System restart")
    print("• Group Policy modifications")
    print("• Registry edits")

if __name__ == "__main__":
    main()
