#!/usr/bin/env python3
"""
Safety Measures Verification
Comprehensive safety system for all BIOS/Security fixes
"""

import subprocess
import json
import os
import shutil
from datetime import datetime

class SafetyMeasuresManager:
    def __init__(self):
        self.backup_dir = "SAFETY_BACKUPS"
        self.restore_script = "EMERGENCY_RESTORE.bat"
        self.safety_log = "safety_measures.json"
        self.results = []
        
    def log(self, message):
        """Safe logging"""
        try:
            safe_message = str(message).encode('ascii', 'ignore').decode('ascii')
            print(safe_message)
            self.results.append(safe_message)
        except:
            print("[SAFETY] Operation logged")
            self.results.append("[SAFETY] Operation logged")
    
    def create_system_restore_point(self):
        """Create Windows System Restore Point"""
        self.log("CREATING SYSTEM RESTORE POINT...")
        
        try:
            # Create restore point
            cmd = 'powershell.exe -Command "Checkpoint-Computer -Description \'GameBoost Pro Safety Backup\' -RestorePointType MODIFY_SETTINGS"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.log("[SUCCESS] System Restore Point created")
                return True
            else:
                self.log("[WARNING] System Restore Point creation failed")
                return False
        except Exception as e:
            self.log(f"[ERROR] Restore point error: {str(e)[:50]}")
            return False
    
    def backup_registry_keys(self):
        """Backup critical registry keys"""
        self.log("BACKING UP REGISTRY KEYS...")
        
        # Create backup directory
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # Critical registry keys to backup
        registry_keys = [
            ('HKLM\\SYSTEM\\CurrentControlSet\\Control\\SecureBoot', 'secure_boot_backup.reg'),
            ('HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate', 'windows_update_backup.reg'),
            ('HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender', 'defender_backup.reg'),
            ('HKLM\\SYSTEM\\CurrentControlSet\\Services\\MpsSvc', 'firewall_service_backup.reg'),
            ('HKLM\\SYSTEM\\CurrentControlSet\\Services\\WinDefend', 'defender_service_backup.reg'),
        ]
        
        success_count = 0
        for key, filename in registry_keys:
            try:
                backup_path = os.path.join(self.backup_dir, filename)
                cmd = f'reg export "{key}" "{backup_path}" /y'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    self.log(f"[SUCCESS] Backed up: {filename}")
                    success_count += 1
                else:
                    self.log(f"[WARNING] Failed to backup: {filename}")
            except Exception as e:
                self.log(f"[ERROR] Registry backup error: {filename}")
        
        return success_count >= 3  # At least 3 backups successful
    
    def backup_service_configurations(self):
        """Backup service configurations"""
        self.log("BACKING UP SERVICE CONFIGURATIONS...")
        
        services = ['MpsSvc', 'WinDefend', 'wuauserv', 'UsoSvc', 'bits']
        service_backup = {}
        
        for service in services:
            try:
                # Get service configuration
                cmd = f'sc qc {service}'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)
                
                if result.returncode == 0:
                    service_backup[service] = {
                        'config': result.stdout,
                        'timestamp': datetime.now().isoformat()
                    }
                    self.log(f"[SUCCESS] Backed up service: {service}")
                else:
                    self.log(f"[WARNING] Failed to backup service: {service}")
            except Exception as e:
                self.log(f"[ERROR] Service backup error: {service}")
        
        # Save service backup to file
        try:
            backup_file = os.path.join(self.backup_dir, 'service_configurations.json')
            with open(backup_file, 'w') as f:
                json.dump(service_backup, f, indent=2)
            self.log("[SUCCESS] Service configurations saved")
            return True
        except Exception as e:
            self.log("[ERROR] Failed to save service configurations")
            return False
    
    def create_emergency_restore_script(self):
        """Create emergency restore script"""
        self.log("CREATING EMERGENCY RESTORE SCRIPT...")
        
        restore_script_content = f'''@echo off
title EMERGENCY RESTORE - GameBoost Pro Safety
color 0C

echo.
echo  ==========================================
echo   🚨 EMERGENCY RESTORE - GameBoost Pro
echo  ==========================================
echo.
echo  This will restore your system to the state
echo  before GameBoost Pro made any changes.
echo.
echo  ⚠️ WARNING: This will undo ALL changes made
echo  by GameBoost Pro including security disables.
echo.

pause

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Administrator privileges required!
    echo Right-click and "Run as administrator"
    pause
    exit /b 1
)

echo [INFO] Administrator privileges confirmed
echo.

echo 🔄 RESTORING SYSTEM...
echo.

REM Restore registry keys
echo [1/4] Restoring registry keys...
if exist "{self.backup_dir}\\secure_boot_backup.reg" (
    reg import "{self.backup_dir}\\secure_boot_backup.reg"
    echo [SUCCESS] Secure Boot registry restored
)
if exist "{self.backup_dir}\\windows_update_backup.reg" (
    reg import "{self.backup_dir}\\windows_update_backup.reg"
    echo [SUCCESS] Windows Update registry restored
)
if exist "{self.backup_dir}\\defender_backup.reg" (
    reg import "{self.backup_dir}\\defender_backup.reg"
    echo [SUCCESS] Defender registry restored
)

REM Restore services
echo.
echo [2/4] Restoring services...
sc config MpsSvc start= auto
sc start MpsSvc
echo [SUCCESS] Firewall service restored

sc config WinDefend start= auto
sc start WinDefend
echo [SUCCESS] Defender service restored

sc config wuauserv start= auto
sc start wuauserv
echo [SUCCESS] Windows Update service restored

REM Enable security features
echo.
echo [3/4] Re-enabling security features...
powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $false"
echo [SUCCESS] Real-time protection enabled

netsh advfirewall set allprofiles state on
echo [SUCCESS] Firewall enabled

REM System restore point
echo.
echo [4/4] Creating post-restore checkpoint...
powershell.exe -Command "Checkpoint-Computer -Description 'GameBoost Pro Emergency Restore Complete' -RestorePointType MODIFY_SETTINGS"
echo [SUCCESS] Restore checkpoint created

echo.
echo ==========================================
echo  ✅ EMERGENCY RESTORE COMPLETED
echo ==========================================
echo.
echo Your system has been restored to its
echo previous secure state.
echo.
echo RESTART RECOMMENDED for full effect.
echo.

choice /c YN /m "Would you like to restart now? (Y/N)"
if errorlevel 2 goto :end
if errorlevel 1 goto :restart

:restart
echo Restarting in 10 seconds...
timeout /t 10
shutdown /r /t 0
goto :end

:end
pause
exit /b
'''
        
        try:
            with open(self.restore_script, 'w') as f:
                f.write(restore_script_content)
            self.log(f"[SUCCESS] Emergency restore script created: {self.restore_script}")
            return True
        except Exception as e:
            self.log("[ERROR] Failed to create restore script")
            return False
    
    def create_desktop_safety_shortcut(self):
        """Create desktop shortcut for emergency restore"""
        self.log("CREATING DESKTOP SAFETY SHORTCUT...")
        
        try:
            desktop_path = os.path.expanduser("~/Desktop")
            shortcut_path = os.path.join(desktop_path, "🚨 EMERGENCY RESTORE GameBoost Pro.lnk")
            
            # PowerShell script to create shortcut
            ps_script = f'''
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("{shortcut_path}")
$Shortcut.TargetPath = "{os.path.abspath(self.restore_script)}"
$Shortcut.WorkingDirectory = "{os.path.abspath('.')}"
$Shortcut.Description = "Emergency restore for GameBoost Pro changes"
$Shortcut.Save()
'''
            
            subprocess.run(["powershell", "-Command", ps_script], check=True, timeout=15)
            self.log("[SUCCESS] Desktop safety shortcut created")
            return True
        except Exception as e:
            self.log("[ERROR] Failed to create desktop shortcut")
            return False
    
    def save_safety_log(self):
        """Save safety measures log"""
        safety_data = {
            'timestamp': datetime.now().isoformat(),
            'backup_directory': self.backup_dir,
            'restore_script': self.restore_script,
            'safety_measures_applied': [
                'System Restore Point',
                'Registry Key Backups',
                'Service Configuration Backups',
                'Emergency Restore Script',
                'Desktop Safety Shortcut'
            ],
            'results': self.results
        }
        
        try:
            with open(self.safety_log, 'w') as f:
                json.dump(safety_data, f, indent=2)
            self.log(f"[SUCCESS] Safety log saved: {self.safety_log}")
            return True
        except Exception as e:
            self.log("[ERROR] Failed to save safety log")
            return False
    
    def setup_all_safety_measures(self):
        """Setup all safety measures"""
        self.log("🛡️ SETTING UP COMPREHENSIVE SAFETY MEASURES")
        self.log("=" * 60)
        
        safety_count = 0
        total_measures = 5
        
        # 1. System Restore Point
        if self.create_system_restore_point():
            safety_count += 1
        
        # 2. Registry Backups
        if self.backup_registry_keys():
            safety_count += 1
        
        # 3. Service Backups
        if self.backup_service_configurations():
            safety_count += 1
        
        # 4. Emergency Restore Script
        if self.create_emergency_restore_script():
            safety_count += 1
        
        # 5. Desktop Safety Shortcut
        if self.create_desktop_safety_shortcut():
            safety_count += 1
        
        # Save safety log
        self.save_safety_log()
        
        self.log("")
        self.log(f"📊 SAFETY MEASURES: {safety_count}/{total_measures} completed")
        
        if safety_count >= 4:
            self.log("✅ COMPREHENSIVE SAFETY MEASURES ACTIVE!")
            self.log("🛡️ Your system is protected against damage")
            self.log("🚨 Emergency restore available on desktop")
            return True
        else:
            self.log("⚠️ Some safety measures failed")
            self.log("💡 Proceed with extra caution")
            return False

def main():
    print("🛡️ SAFETY MEASURES VERIFICATION")
    print("=" * 50)
    
    safety = SafetyMeasuresManager()
    
    print("Setting up comprehensive safety measures...")
    print("This ensures all changes can be safely reversed.")
    print()
    
    success = safety.setup_all_safety_measures()
    
    print()
    print("=" * 50)
    print("SAFETY VERIFICATION COMPLETE")
    print("=" * 50)
    
    if success:
        print("✅ ALL SAFETY MEASURES ACTIVE!")
        print()
        print("🛡️ PROTECTION INCLUDES:")
        print("• System Restore Point created")
        print("• Registry keys backed up")
        print("• Service configurations saved")
        print("• Emergency restore script on desktop")
        print("• Complete safety log maintained")
        print()
        print("🚨 EMERGENCY RESTORE:")
        print("If anything goes wrong, double-click the")
        print("'🚨 EMERGENCY RESTORE GameBoost Pro' shortcut")
        print("on your desktop to restore everything!")
    else:
        print("⚠️ Some safety measures failed")
        print("Proceed with extra caution")
    
    input("Press Enter to continue...")

if __name__ == "__main__":
    main()
