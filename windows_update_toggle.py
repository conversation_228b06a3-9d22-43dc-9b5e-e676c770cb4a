#!/usr/bin/env python3
"""
Windows Update Toggle System
Dedicated toggle for enabling/disabling Windows Update
Perfect for when you need updates (Xbox app, etc.) but want to disable for gaming
"""

import subprocess
import json
import os
from datetime import datetime

class WindowsUpdateToggle:
    def __init__(self):
        self.results = []
        self.state_file = "windows_update_state.json"
    
    def log(self, message):
        """Add message to results"""
        print(message)
        self.results.append(message)
    
    def run_command(self, command, description):
        """Run command and return success status"""
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                self.log(f"✅ {description}: SUCCESS")
                return True
            else:
                self.log(f"❌ {description}: FAILED")
                return False
        except subprocess.TimeoutExpired:
            self.log(f"⏰ {description}: TIMEOUT")
            return False
        except Exception as e:
            self.log(f"❌ {description}: ERROR - {str(e)}")
            return False
    
    def is_admin(self):
        """Check if running with administrator privileges"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def get_current_update_state(self):
        """Get current Windows Update state"""
        try:
            # Check Windows Update service status
            result = subprocess.run('sc query wuauserv', shell=True, capture_output=True, text=True)
            wuauserv_running = "RUNNING" in result.stdout
            
            # Check Update Orchestrator service
            result = subprocess.run('sc query UsoSvc', shell=True, capture_output=True, text=True)
            usosvc_running = "RUNNING" in result.stdout
            
            # Check registry settings
            result = subprocess.run('reg query "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU" /v NoAutoUpdate', 
                                  shell=True, capture_output=True, text=True)
            auto_update_disabled = result.returncode == 0 and "0x1" in result.stdout
            
            return {
                "wuauserv_running": wuauserv_running,
                "usosvc_running": usosvc_running,
                "auto_update_disabled": auto_update_disabled,
                "update_enabled": wuauserv_running and usosvc_running and not auto_update_disabled
            }
        except:
            return {
                "wuauserv_running": False,
                "usosvc_running": False, 
                "auto_update_disabled": True,
                "update_enabled": False
            }
    
    def save_current_state(self):
        """Save current Windows Update state"""
        try:
            state = self.get_current_update_state()
            state["timestamp"] = datetime.now().isoformat()
            
            with open(self.state_file, 'w') as f:
                json.dump(state, f, indent=2)
            
            self.log(f"💾 Windows Update state saved to {self.state_file}")
            return True
        except Exception as e:
            self.log(f"❌ Failed to save state: {str(e)}")
            return False
    
    def enable_windows_update(self):
        """Enable Windows Update completely"""
        if not self.is_admin():
            self.log("❌ Administrator privileges required!")
            return False
        
        self.log("🔄 ENABLING WINDOWS UPDATE...")
        self.log("=" * 50)
        
        success_count = 0
        total_operations = 0
        
        # Save current state first
        self.save_current_state()
        
        # Enable Windows Update registry settings
        self.log("\n📝 ENABLING UPDATE REGISTRY SETTINGS...")
        registry_commands = [
            ('reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU" /v NoAutoUpdate /f', "Remove NoAutoUpdate"),
            ('reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate" /v DisableWindowsUpdateAccess /f', "Remove Update Access Block"),
            ('reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate" /v SetDisableUXWUAccess /f', "Remove UX Access Block"),
            ('reg delete "HKLM\\SOFTWARE\\Microsoft\\WindowsUpdate\\UX\\Settings" /v UxOption /f', "Remove UX Settings Block"),
        ]
        
        for cmd, desc in registry_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Enable Windows Update services
        self.log("\n🔧 ENABLING UPDATE SERVICES...")
        service_commands = [
            ('sc config wuauserv start= auto', "Windows Update Service Auto"),
            ('sc start wuauserv', "Windows Update Service Start"),
            ('sc config UsoSvc start= auto', "Update Orchestrator Auto"),
            ('sc start UsoSvc', "Update Orchestrator Start"),
            ('sc config bits start= auto', "BITS Service Auto"),
            ('sc start bits', "BITS Service Start"),
            ('sc config dosvc start= auto', "Delivery Optimization Auto"),
            ('sc start dosvc', "Delivery Optimization Start"),
        ]
        
        for cmd, desc in service_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Enable automatic updates via PowerShell
        self.log("\n⚡ ENABLING AUTOMATIC UPDATES...")
        powershell_commands = [
            ('powershell.exe -Command "Set-ItemProperty -Path \'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\WindowsUpdate\\Auto Update\' -Name AUOptions -Value 4 -Force"', "Auto Update Options"),
            ('powershell.exe -Command "Get-Service wuauserv | Set-Service -StartupType Automatic"', "PS Windows Update Auto"),
            ('powershell.exe -Command "Get-Service UsoSvc | Set-Service -StartupType Automatic"', "PS Update Orchestrator Auto"),
        ]
        
        for cmd, desc in powershell_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Force check for updates
        self.log("\n🔍 FORCING UPDATE CHECK...")
        update_check_commands = [
            ('powershell.exe -Command "Start-Service wuauserv"', "Ensure Update Service Running"),
            ('powershell.exe -Command "(New-Object -ComObject Microsoft.Update.AutoUpdate).DetectNow()"', "Force Update Detection"),
            ('usoclient StartScan', "Start Update Scan"),
        ]
        
        for cmd, desc in update_check_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        success_rate = success_count / total_operations if total_operations > 0 else 0
        
        self.log(f"\n📊 WINDOWS UPDATE ENABLE RESULTS: {success_count}/{total_operations} ({success_rate*100:.1f}%)")
        
        if success_rate >= 0.7:
            self.log("✅ WINDOWS UPDATE ENABLED SUCCESSFULLY!")
            self.log("🔄 You can now check for and install updates")
            self.log("📱 Xbox app and Microsoft Store should now update")
            return True
        else:
            self.log("⚠️ WINDOWS UPDATE PARTIALLY ENABLED")
            return False
    
    def disable_windows_update(self):
        """Disable Windows Update completely"""
        if not self.is_admin():
            self.log("❌ Administrator privileges required!")
            return False
        
        self.log("🚫 DISABLING WINDOWS UPDATE...")
        self.log("=" * 50)
        
        success_count = 0
        total_operations = 0
        
        # Save current state first
        self.save_current_state()
        
        # Disable Windows Update registry settings
        self.log("\n📝 DISABLING UPDATE REGISTRY SETTINGS...")
        registry_commands = [
            ('reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU" /v NoAutoUpdate /t REG_DWORD /d 1 /f', "Disable Auto Update"),
            ('reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate" /v DisableWindowsUpdateAccess /t REG_DWORD /d 1 /f', "Block Update Access"),
            ('reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate" /v SetDisableUXWUAccess /t REG_DWORD /d 1 /f', "Block UX Access"),
            ('reg add "HKLM\\SOFTWARE\\Microsoft\\WindowsUpdate\\UX\\Settings" /v UxOption /t REG_DWORD /d 1 /f', "Block UX Settings"),
        ]
        
        for cmd, desc in registry_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Stop and disable Windows Update services
        self.log("\n🔧 DISABLING UPDATE SERVICES...")
        service_commands = [
            ('sc stop wuauserv', "Stop Windows Update Service"),
            ('sc config wuauserv start= disabled', "Disable Windows Update Service"),
            ('sc stop UsoSvc', "Stop Update Orchestrator"),
            ('sc config UsoSvc start= disabled', "Disable Update Orchestrator"),
            ('sc stop bits', "Stop BITS Service"),
            ('sc config bits start= disabled', "Disable BITS Service"),
            ('sc stop dosvc', "Stop Delivery Optimization"),
            ('sc config dosvc start= disabled', "Disable Delivery Optimization"),
        ]
        
        for cmd, desc in service_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Additional PowerShell disable
        self.log("\n⚡ ADDITIONAL POWERSHELL DISABLE...")
        powershell_commands = [
            ('powershell.exe -Command "Set-ItemProperty -Path \'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\WindowsUpdate\\Auto Update\' -Name AUOptions -Value 1 -Force"', "Disable Auto Options"),
            ('powershell.exe -Command "Get-Service wuauserv | Set-Service -StartupType Disabled"', "PS Disable Windows Update"),
            ('powershell.exe -Command "Get-Service UsoSvc | Set-Service -StartupType Disabled"', "PS Disable Update Orchestrator"),
        ]
        
        for cmd, desc in powershell_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        success_rate = success_count / total_operations if total_operations > 0 else 0
        
        self.log(f"\n📊 WINDOWS UPDATE DISABLE RESULTS: {success_count}/{total_operations} ({success_rate*100:.1f}%)")
        
        if success_rate >= 0.7:
            self.log("✅ WINDOWS UPDATE DISABLED SUCCESSFULLY!")
            self.log("🎮 System ready for uninterrupted gaming")
            return True
        else:
            self.log("⚠️ WINDOWS UPDATE PARTIALLY DISABLED")
            return False
    
    def get_results(self):
        """Get operation results"""
        return self.results
    
    def clear_results(self):
        """Clear operation results"""
        self.results = []

# Example usage and testing
if __name__ == "__main__":
    toggle = WindowsUpdateToggle()
    
    print("🔄 WINDOWS UPDATE TOGGLE TEST")
    print("=" * 40)
    
    # Check current state
    current_state = toggle.get_current_update_state()
    print(f"Current State: {current_state}")
    print()
    
    if current_state["update_enabled"]:
        print("Windows Update is currently ENABLED")
        choice = input("Disable Windows Update? (y/n): ").lower().strip()
        if choice == 'y':
            toggle.disable_windows_update()
    else:
        print("Windows Update is currently DISABLED")
        choice = input("Enable Windows Update? (y/n): ").lower().strip()
        if choice == 'y':
            toggle.enable_windows_update()
    
    print("\nOperation completed!")
    input("Press Enter to exit...")
