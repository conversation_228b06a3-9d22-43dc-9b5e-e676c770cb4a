#!/usr/bin/env python3
"""
Test Admin Interface
Simple test to verify the interface works with admin privileges
"""

import sys
import ctypes
import tkinter as tk
from tkinter import messagebox

def check_admin():
    """Check if running as administrator"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def test_interface():
    """Test basic interface functionality"""
    root = tk.Tk()
    root.title("Gaming Tool Interface - Admin Test")
    root.geometry("600x400")
    
    # Admin status
    admin_status = "✅ ADMINISTRATOR" if check_admin() else "❌ NOT ADMIN"
    
    # Create simple interface
    tk.Label(root, text="🎮 Gaming Tool Interface Test", font=("Arial", 16, "bold")).pack(pady=20)
    tk.Label(root, text=f"Admin Status: {admin_status}", font=("Arial", 12)).pack(pady=10)
    
    if check_admin():
        tk.Label(root, text="✅ Ready to modify system settings", fg="green").pack(pady=5)
        tk.Button(root, text="Launch Full Interface", command=launch_full_interface, 
                 bg="blue", fg="white", font=("Arial", 12)).pack(pady=20)
    else:
        tk.Label(root, text="❌ Admin privileges required", fg="red").pack(pady=5)
        tk.Button(root, text="Restart as Admin", command=restart_as_admin,
                 bg="orange", fg="white", font=("Arial", 12)).pack(pady=20)
    
    tk.Button(root, text="Close", command=root.quit, 
             font=("Arial", 10)).pack(pady=10)
    
    root.mainloop()

def launch_full_interface():
    """Launch the full gaming tool interface"""
    try:
        import subprocess
        subprocess.Popen([sys.executable, "gaming_tool_interface.py"])
        messagebox.showinfo("Success", "Full interface launched!")
    except Exception as e:
        messagebox.showerror("Error", f"Could not launch interface: {str(e)}")

def restart_as_admin():
    """Restart with admin privileges"""
    try:
        import subprocess
        subprocess.run(['powershell', '-Command', f'Start-Process python -ArgumentList "{__file__}" -Verb RunAs'])
    except Exception as e:
        messagebox.showerror("Error", f"Could not restart as admin: {str(e)}")

def main():
    print("🧪 TESTING ADMIN INTERFACE")
    print("=" * 40)
    
    if check_admin():
        print("✅ Running with administrator privileges")
    else:
        print("❌ NOT running with administrator privileges")
    
    print("Launching test interface...")
    test_interface()

if __name__ == "__main__":
    main()
