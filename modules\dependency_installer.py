#!/usr/bin/env python3
"""
Dependency Installer Module
Automated download and installation of required tools
"""

import os
import sys
import subprocess
import requests
import tempfile
import zipfile
import shutil
from pathlib import Path
import threading
import time

class DependencyInstaller:
    def __init__(self):
        self.installation_results = []
        self.download_progress = {}
        
        # Define required dependencies with download URLs
        self.dependencies = {
            "winrar": {
                "name": "WinRAR",
                "url": "https://www.rarlab.com/rar/winrar-x64-623.exe",
                "filename": "winrar-x64-623.exe",
                "install_args": "/S",
                "description": "Archive extraction tool"
            },
            "vcredist": {
                "name": "Visual C++ Redistributable",
                "url": "https://aka.ms/vs/17/release/vc_redist.x64.exe",
                "filename": "vc_redist.x64.exe",
                "install_args": "/quiet /norestart",
                "description": "Microsoft Visual C++ Runtime"
            },
            "notepadpp": {
                "name": "Notepad++",
                "url": "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.5.8/npp.8.5.8.Installer.x64.exe",
                "filename": "npp.8.5.8.Installer.x64.exe",
                "install_args": "/S",
                "description": "Advanced text editor"
            },
            "defender_control": {
                "name": "Defender Control",
                "url": "https://www.sordum.org/files/download/defender-control/DefenderControl.zip",
                "filename": "DefenderControl.zip",
                "install_args": None,
                "description": "Windows Defender control tool"
            },
            "windows_update_blocker": {
                "name": "Windows Update Blocker",
                "url": "https://www.sordum.org/files/download/windows-update-blocker/Wub.zip",
                "filename": "Wub.zip",
                "install_args": None,
                "description": "Windows Update blocking tool"
            }
        }
        
        # Create downloads directory
        self.downloads_dir = Path(tempfile.gettempdir()) / "gaming_tool_downloads"
        self.downloads_dir.mkdir(exist_ok=True)
        
        # Create tools directory
        self.tools_dir = Path("C:/GamingTools")
        self.tools_dir.mkdir(exist_ok=True)
    
    def download_file(self, url, filename, description=""):
        """Download a file with progress tracking"""
        try:
            self.installation_results.append(f"📥 Downloading {description}...")
            
            file_path = self.downloads_dir / filename
            
            # Start download
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # Update progress
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            self.download_progress[filename] = progress
            
            self.installation_results.append(f"✅ Downloaded {description} ({downloaded_size} bytes)")
            return file_path
            
        except requests.RequestException as e:
            self.installation_results.append(f"❌ Download failed for {description}: {str(e)}")
            return None
        except Exception as e:
            self.installation_results.append(f"❌ Error downloading {description}: {str(e)}")
            return None
    
    def install_executable(self, file_path, install_args, description=""):
        """Install an executable file"""
        try:
            if not file_path.exists():
                self.installation_results.append(f"❌ File not found: {file_path}")
                return False
            
            self.installation_results.append(f"🔧 Installing {description}...")
            
            # Build command
            cmd = [str(file_path)]
            if install_args:
                cmd.extend(install_args.split())
            
            # Run installer
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                self.installation_results.append(f"✅ {description} installed successfully")
                return True
            else:
                self.installation_results.append(f"❌ {description} installation failed (exit code: {result.returncode})")
                if result.stderr:
                    self.installation_results.append(f"Error: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.installation_results.append(f"❌ {description} installation timed out")
            return False
        except Exception as e:
            self.installation_results.append(f"❌ Error installing {description}: {str(e)}")
            return False
    
    def extract_archive(self, file_path, destination, description=""):
        """Extract a ZIP archive"""
        try:
            if not file_path.exists():
                self.installation_results.append(f"❌ Archive not found: {file_path}")
                return False
            
            self.installation_results.append(f"📦 Extracting {description}...")
            
            with zipfile.ZipFile(file_path, 'r') as zip_ref:
                zip_ref.extractall(destination)
            
            self.installation_results.append(f"✅ {description} extracted to {destination}")
            return True
            
        except zipfile.BadZipFile:
            self.installation_results.append(f"❌ Invalid ZIP file: {file_path}")
            return False
        except Exception as e:
            self.installation_results.append(f"❌ Error extracting {description}: {str(e)}")
            return False
    
    def install_winrar(self):
        """Install WinRAR"""
        dep = self.dependencies["winrar"]
        file_path = self.download_file(dep["url"], dep["filename"], dep["name"])
        
        if file_path:
            return self.install_executable(file_path, dep["install_args"], dep["name"])
        return False
    
    def install_vcredist(self):
        """Install Visual C++ Redistributable"""
        dep = self.dependencies["vcredist"]
        file_path = self.download_file(dep["url"], dep["filename"], dep["name"])
        
        if file_path:
            return self.install_executable(file_path, dep["install_args"], dep["name"])
        return False
    
    def install_notepadpp(self):
        """Install Notepad++"""
        dep = self.dependencies["notepadpp"]
        file_path = self.download_file(dep["url"], dep["filename"], dep["name"])
        
        if file_path:
            return self.install_executable(file_path, dep["install_args"], dep["name"])
        return False
    
    def install_defender_control(self):
        """Install Defender Control"""
        dep = self.dependencies["defender_control"]
        file_path = self.download_file(dep["url"], dep["filename"], dep["name"])
        
        if file_path:
            destination = self.tools_dir / "DefenderControl"
            destination.mkdir(exist_ok=True)
            return self.extract_archive(file_path, destination, dep["name"])
        return False
    
    def install_windows_update_blocker(self):
        """Install Windows Update Blocker"""
        dep = self.dependencies["windows_update_blocker"]
        file_path = self.download_file(dep["url"], dep["filename"], dep["name"])
        
        if file_path:
            destination = self.tools_dir / "WindowsUpdateBlocker"
            destination.mkdir(exist_ok=True)
            return self.extract_archive(file_path, destination, dep["name"])
        return False
    
    def create_winfix_script(self):
        """Create WinFIX script for system optimization"""
        try:
            winfix_content = '''@echo off
echo WinFIX - System Optimization Script
echo ====================================

echo Cleaning temporary files...
del /q /f /s %TEMP%\\*
del /q /f /s C:\\Windows\\Temp\\*

echo Flushing DNS cache...
ipconfig /flushdns

echo Resetting network stack...
netsh winsock reset
netsh int ip reset

echo Optimizing system services...
sc config "SysMain" start= disabled
sc config "WSearch" start= disabled
sc config "Themes" start= disabled

echo Clearing event logs...
for /f "tokens=*" %%G in ('wevtutil.exe el') DO (call :do_clear "%%G")
goto :end

:do_clear
wevtutil.exe cl %1
goto :eof

:end
echo WinFIX optimization complete!
echo Please restart your computer for changes to take effect.
pause
'''
            
            winfix_path = self.tools_dir / "WinFIX.bat"
            with open(winfix_path, 'w') as f:
                f.write(winfix_content)
            
            self.installation_results.append(f"✅ WinFIX script created at {winfix_path}")
            return True
            
        except Exception as e:
            self.installation_results.append(f"❌ Error creating WinFIX script: {str(e)}")
            return False
    
    def check_existing_installations(self):
        """Check for existing installations"""
        self.installation_results.append("=== Checking Existing Installations ===")
        
        # Check for existing programs
        checks = [
            ("WinRAR", ["C:/Program Files/WinRAR/WinRAR.exe", "C:/Program Files (x86)/WinRAR/WinRAR.exe"]),
            ("Notepad++", ["C:/Program Files/Notepad++/notepad++.exe", "C:/Program Files (x86)/Notepad++/notepad++.exe"]),
            ("Visual C++ Redistributable", ["C:/Windows/System32/vcruntime140.dll"]),
        ]
        
        for name, paths in checks:
            found = False
            for path in paths:
                if Path(path).exists():
                    self.installation_results.append(f"✅ {name} already installed at {path}")
                    found = True
                    break
            
            if not found:
                self.installation_results.append(f"⚠️  {name} not found - will be installed")
    
    def install_all(self):
        """Install all required dependencies"""
        self.installation_results = []
        self.installation_results.append("=== Starting Dependency Installation ===")
        
        # Check existing installations first
        self.check_existing_installations()
        
        # Install dependencies
        installation_tasks = [
            (self.install_winrar, "WinRAR"),
            (self.install_vcredist, "Visual C++ Redistributable"),
            (self.install_notepadpp, "Notepad++"),
            (self.install_defender_control, "Defender Control"),
            (self.install_windows_update_blocker, "Windows Update Blocker"),
            (self.create_winfix_script, "WinFIX Script"),
        ]
        
        success_count = 0
        for task_func, task_name in installation_tasks:
            self.installation_results.append(f"\n--- Installing {task_name} ---")
            try:
                if task_func():
                    success_count += 1
                    self.installation_results.append(f"✅ {task_name} installation completed")
                else:
                    self.installation_results.append(f"❌ {task_name} installation failed")
            except Exception as e:
                self.installation_results.append(f"❌ {task_name} error: {str(e)}")
        
        self.installation_results.append(f"\n=== Dependency Installation Complete ===")
        self.installation_results.append(f"Successfully installed {success_count}/{len(installation_tasks)} dependencies")
        
        if success_count == len(installation_tasks):
            self.installation_results.append("🎉 All dependencies installed successfully!")
            self.installation_results.append(f"📁 Tools directory: {self.tools_dir}")
        else:
            self.installation_results.append("⚠️  Some installations may require manual intervention")
        
        # Cleanup downloads
        try:
            shutil.rmtree(self.downloads_dir)
            self.installation_results.append("🧹 Temporary downloads cleaned up")
        except:
            pass
        
        return success_count >= len(installation_tasks) * 0.8  # 80% success rate
    
    def get_installation_results(self):
        """Get the results of the last installation operation"""
        return self.installation_results
    
    def get_download_progress(self):
        """Get current download progress"""
        return self.download_progress
