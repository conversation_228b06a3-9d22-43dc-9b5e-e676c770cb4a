@echo off
title DISABLE SECURITY NOW - GAMING TOOL SETUP
color 0C

echo.
echo  ==========================================
echo   DISABLE SECURITY NOW - GAMING TOOL SETUP
echo  ==========================================
echo.
echo  This will disable Windows security for gaming tools:
echo  - Windows Defender Real-time Protection
echo  - Windows Firewall (All profiles)
echo  - SmartScreen Protection
echo  - User Account Control (UAC)
echo  - Fast Boot
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    REM Change to script directory
    cd /d "%~dp0"
    
    echo  [✓] Running PowerShell security disable script...
    echo.
    
    REM Run PowerShell script with execution policy bypass
    powershell.exe -ExecutionPolicy Bypass -File "DISABLE_SECURITY_NOW.ps1"
    
    echo.
    echo  Security disable process completed!
    echo.
    pause
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator to disable security.
    echo  Right-click this file and select "Run as administrator"
    echo.
    pause
    
    REM Try to restart with admin privileges
    echo  Attempting to restart with administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
)

exit /b
