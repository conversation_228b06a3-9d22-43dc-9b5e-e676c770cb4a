@echo off
title DOWNLOAD MICROSOFT STORE
color 0A

echo.
echo  ==========================================
echo   DOWNLOAD MICROSOFT STORE
echo  ==========================================
echo.

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  [1/4] Downloading Microsoft Store installer...
    powershell.exe -Command "Invoke-WebRequest -Uri 'https://aka.ms/getwinget' -OutFile '$env:TEMP\Microsoft.DesktopAppInstaller.msixbundle' -UseBasicParsing"
    if %errorLevel% == 0 (echo  ✅ Download: SUCCESS) else (echo  ❌ Download: FAILED)
    
    echo.
    echo  [2/4] Installing Microsoft Store...
    powershell.exe -Command "Add-AppxPackage -Path '$env:TEMP\Microsoft.DesktopAppInstaller.msixbundle'"
    if %errorLevel% == 0 (echo  ✅ Install: SUCCESS) else (echo  ❌ Install: FAILED)
    
    echo.
    echo  [3/4] Installing winget...
    powershell.exe -Command "winget install Microsoft.WindowsStore --accept-source-agreements --accept-package-agreements --silent"
    if %errorLevel% == 0 (echo  ✅ Winget install: SUCCESS) else (echo  ❌ Winget install: FAILED)
    
    echo.
    echo  [4/4] Testing Microsoft Store...
    timeout /t 3 >nul
    start ms-windows-store:
    if %errorLevel% == 0 (echo  ✅ Store launch: SUCCESS) else (echo  ❌ Store launch: FAILED)
    
    echo.
    echo  ==========================================
    echo   ✅ MICROSOFT STORE DOWNLOAD COMPLETE!
    echo  ==========================================
    echo.
    echo  Try opening Microsoft Store from Start Menu!
    echo.
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  Right-click this file and select "Run as administrator"
    echo.
)

echo.
echo  Press any key to exit...
pause >nul
