#!/usr/bin/env python3
"""
System Cleanup Module
Handles detection and removal of anti-cheat software, cracking tools, and security software
"""

import os
import sys
import subprocess
import psutil
import winreg
import shutil
from pathlib import Path
import tkinter as tk
from tkinter import messagebox
import threading

class SystemCleanup:
    def __init__(self):
        # Define software to remove
        self.anti_cheat_software = [
            "FaceIT",
            "FACEIT",
            "ESEA",
            "Riot Vanguard",
            "vgc.exe",
            "vgtray.exe",
            "BattlEye",
            "EasyAntiCheat",
            "EAC",
            "Valve Anti-Cheat",
            "VAC"
        ]
        
        self.cracking_software = [
            "Cheat Engine",
            "cheatengine",
            "x64dbg",
            "x32dbg",
            "OllyDbg",
            "WinDbg",
            "IDA Pro",
            "IDA",
            "Hex-Rays",
            "Process Hacker",
            "Process Monitor",
            "Wireshark"
        ]
        
        self.security_software = [
            "Windows Defender",
            "Avast",
            "AVG",
            "Norton",
            "McAfee",
            "Ka<PERSON>sky",
            "Bitdefender",
            "Malwarebytes",
            "ESET",
            "Trend Micro",
            "Sophos",
            "Webroot"
        ]
        
        self.cleanup_results = []
    
    def scan_running_processes(self):
        """Scan for running processes that need to be terminated"""
        found_processes = []
        all_software = self.anti_cheat_software + self.cracking_software + self.security_software
        
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                proc_name = proc.info['name'].lower()
                proc_exe = proc.info['exe']
                
                for software in all_software:
                    if software.lower() in proc_name or (proc_exe and software.lower() in proc_exe.lower()):
                        found_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'exe': proc_exe,
                            'software_type': self.get_software_type(software)
                        })
                        break
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        return found_processes
    
    def get_software_type(self, software):
        """Determine the type of software"""
        if software in self.anti_cheat_software:
            return "Anti-Cheat"
        elif software in self.cracking_software:
            return "Cracking Tool"
        elif software in self.security_software:
            return "Security Software"
        return "Unknown"
    
    def terminate_processes(self, processes):
        """Terminate specified processes"""
        terminated = []
        failed = []
        
        for proc_info in processes:
            try:
                proc = psutil.Process(proc_info['pid'])
                proc.terminate()
                proc.wait(timeout=10)
                terminated.append(proc_info)
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired) as e:
                failed.append((proc_info, str(e)))
        
        return terminated, failed
    
    def scan_installed_programs(self):
        """Scan for installed programs that need to be removed"""
        found_programs = []
        all_software = self.anti_cheat_software + self.cracking_software + self.security_software
        
        # Check Windows Registry for installed programs
        registry_paths = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        ]
        
        for reg_path in registry_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path) as key:
                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    uninstall_string = winreg.QueryValueEx(subkey, "UninstallString")[0]
                                    
                                    for software in all_software:
                                        if software.lower() in display_name.lower():
                                            found_programs.append({
                                                'name': display_name,
                                                'uninstall_string': uninstall_string,
                                                'software_type': self.get_software_type(software)
                                            })
                                            break
                                except FileNotFoundError:
                                    continue
                        except OSError:
                            continue
            except OSError:
                continue
        
        return found_programs
    
    def uninstall_programs(self, programs):
        """Uninstall specified programs"""
        uninstalled = []
        failed = []
        
        for program in programs:
            try:
                # Execute uninstall command
                uninstall_cmd = program['uninstall_string']
                if "msiexec" in uninstall_cmd.lower():
                    # MSI installer - add silent flags
                    uninstall_cmd += " /quiet /norestart"
                
                result = subprocess.run(uninstall_cmd, shell=True, capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    uninstalled.append(program)
                else:
                    failed.append((program, f"Exit code: {result.returncode}"))
                    
            except subprocess.TimeoutExpired:
                failed.append((program, "Timeout during uninstallation"))
            except Exception as e:
                failed.append((program, str(e)))
        
        return uninstalled, failed
    
    def scan_file_system(self):
        """Scan file system for software installations"""
        found_files = []
        all_software = self.anti_cheat_software + self.cracking_software + self.security_software
        
        # Common installation directories
        search_paths = [
            Path("C:/Program Files"),
            Path("C:/Program Files (x86)"),
            Path(os.path.expanduser("~/AppData/Local")),
            Path(os.path.expanduser("~/AppData/Roaming")),
            Path("C:/ProgramData")
        ]
        
        for search_path in search_paths:
            if search_path.exists():
                try:
                    for item in search_path.iterdir():
                        if item.is_dir():
                            for software in all_software:
                                if software.lower() in item.name.lower():
                                    found_files.append({
                                        'path': str(item),
                                        'name': item.name,
                                        'software_type': self.get_software_type(software)
                                    })
                                    break
                except PermissionError:
                    continue
        
        return found_files
    
    def remove_directories(self, directories):
        """Remove specified directories"""
        removed = []
        failed = []
        
        for dir_info in directories:
            try:
                dir_path = Path(dir_info['path'])
                if dir_path.exists():
                    shutil.rmtree(dir_path)
                    removed.append(dir_info)
            except Exception as e:
                failed.append((dir_info, str(e)))
        
        return removed, failed
    
    def clean_registry(self):
        """Clean registry entries for removed software"""
        cleaned_entries = []
        
        # Registry paths to clean
        registry_paths = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\RunOnce"
        ]
        
        all_software = self.anti_cheat_software + self.cracking_software + self.security_software
        
        for reg_path in registry_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path, 0, winreg.KEY_ALL_ACCESS) as key:
                    i = 0
                    while True:
                        try:
                            value_name, value_data, _ = winreg.EnumValue(key, i)
                            
                            for software in all_software:
                                if software.lower() in value_name.lower() or software.lower() in str(value_data).lower():
                                    winreg.DeleteValue(key, value_name)
                                    cleaned_entries.append(f"{reg_path}\\{value_name}")
                                    break
                            else:
                                i += 1
                        except OSError:
                            break
            except OSError:
                continue
        
        return cleaned_entries
    
    def run_cleanup(self):
        """Run the complete cleanup process"""
        self.cleanup_results = []
        
        try:
            # Step 1: Scan and terminate running processes
            self.cleanup_results.append("=== Scanning Running Processes ===")
            running_processes = self.scan_running_processes()
            
            if running_processes:
                self.cleanup_results.append(f"Found {len(running_processes)} processes to terminate:")
                for proc in running_processes:
                    self.cleanup_results.append(f"  - {proc['name']} (PID: {proc['pid']}) - {proc['software_type']}")
                
                terminated, failed = self.terminate_processes(running_processes)
                self.cleanup_results.append(f"Terminated {len(terminated)} processes")
                
                if failed:
                    self.cleanup_results.append("Failed to terminate:")
                    for proc, error in failed:
                        self.cleanup_results.append(f"  - {proc['name']}: {error}")
            else:
                self.cleanup_results.append("No problematic processes found running")
            
            # Step 2: Scan and uninstall programs
            self.cleanup_results.append("\n=== Scanning Installed Programs ===")
            installed_programs = self.scan_installed_programs()
            
            if installed_programs:
                self.cleanup_results.append(f"Found {len(installed_programs)} programs to uninstall:")
                for program in installed_programs:
                    self.cleanup_results.append(f"  - {program['name']} - {program['software_type']}")
                
                uninstalled, failed = self.uninstall_programs(installed_programs)
                self.cleanup_results.append(f"Uninstalled {len(uninstalled)} programs")
                
                if failed:
                    self.cleanup_results.append("Failed to uninstall:")
                    for program, error in failed:
                        self.cleanup_results.append(f"  - {program['name']}: {error}")
            else:
                self.cleanup_results.append("No problematic programs found installed")
            
            # Step 3: Scan and remove file system entries
            self.cleanup_results.append("\n=== Scanning File System ===")
            file_entries = self.scan_file_system()
            
            if file_entries:
                self.cleanup_results.append(f"Found {len(file_entries)} directories to remove:")
                for entry in file_entries:
                    self.cleanup_results.append(f"  - {entry['path']} - {entry['software_type']}")
                
                removed, failed = self.remove_directories(file_entries)
                self.cleanup_results.append(f"Removed {len(removed)} directories")
                
                if failed:
                    self.cleanup_results.append("Failed to remove:")
                    for entry, error in failed:
                        self.cleanup_results.append(f"  - {entry['path']}: {error}")
            else:
                self.cleanup_results.append("No problematic directories found")
            
            # Step 4: Clean registry
            self.cleanup_results.append("\n=== Cleaning Registry ===")
            cleaned_entries = self.clean_registry()
            
            if cleaned_entries:
                self.cleanup_results.append(f"Cleaned {len(cleaned_entries)} registry entries:")
                for entry in cleaned_entries:
                    self.cleanup_results.append(f"  - {entry}")
            else:
                self.cleanup_results.append("No problematic registry entries found")
            
            self.cleanup_results.append("\n=== Cleanup Complete ===")
            self.cleanup_results.append("System cleanup completed successfully!")
            
        except Exception as e:
            self.cleanup_results.append(f"Error during cleanup: {str(e)}")
        
        return self.cleanup_results
    
    def get_cleanup_results(self):
        """Get the results of the last cleanup operation"""
        return self.cleanup_results
