@echo off
title Support Tools - Install Python Dependencies
echo.
echo ========================================
echo  Support Tools - Installing Dependencies
echo ========================================
echo.

REM Check if Python is installed
echo [1/4] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ERROR: Python is not installed!
    echo.
    echo Please install Python 3.7+ from: https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python is installed!
python --version

echo.
echo [2/4] Upgrading pip...
python -m pip install --upgrade pip

echo.
echo [3/4] Installing required packages...
python -m pip install psutil requests

echo.
echo [4/4] Verifying installation...
python -c "import tkinter; import psutil; import requests; print('All dependencies installed successfully!')"

if errorlevel 1 (
    echo.
    echo WARNING: Some dependencies may not have installed correctly
    echo The interface should still work with basic functionality
    echo.
) else (
    echo.
    echo ========================================
    echo  INSTALLATION COMPLETE!
    echo ========================================
    echo.
    echo All Python dependencies are now installed.
    echo You can now run the Support Tools interface.
    echo.
)

echo Press any key to continue...
pause >nul
