#!/usr/bin/env python3
"""
Ultimate Defender Kill
Final automated attempt to disable Windows Defender
"""

import subprocess
import winreg
import ctypes
import time
import os

def is_admin():
    """Check admin privileges"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_cmd(command, description=""):
    """Run command and return success"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ {description}: SUCCESS")
            return True
        else:
            print(f"❌ {description}: FAILED")
            return False
    except Exception as e:
        print(f"❌ {description}: ERROR - {str(e)}")
        return False

def disable_tamper_protection_registry():
    """Try to disable Tamper Protection via registry"""
    print("🔒 ATTEMPTING TAMPER PROTECTION REGISTRY DISABLE...")
    
    tamper_paths = [
        (r"SOFTWARE\Microsoft\Windows Defender\Features", "TamperProtection"),
        (r"SOFTWARE\Policies\Microsoft\Windows Defender\Features", "TamperProtection"),
        (r"SOFTWARE\Microsoft\Windows Defender\Real-Time Protection", "DisableRealtimeMonitoring"),
    ]
    
    success_count = 0
    for path, value_name in tamper_paths:
        try:
            with winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, path) as key:
                if "TamperProtection" in value_name:
                    winreg.SetValueEx(key, value_name, 0, winreg.REG_DWORD, 0)
                else:
                    winreg.SetValueEx(key, value_name, 0, winreg.REG_DWORD, 1)
            print(f"✅ Registry: {path}\\{value_name}")
            success_count += 1
        except Exception as e:
            print(f"❌ Registry: {path}\\{value_name} - {str(e)}")
    
    return success_count > 0

def kill_defender_processes():
    """Forcefully kill all Defender processes"""
    print("💀 KILLING DEFENDER PROCESSES...")
    
    processes = [
        "MsMpEng.exe", "NisSrv.exe", "SecurityHealthSystray.exe", 
        "SecurityHealthService.exe", "MpCmdRun.exe", "MpSigStub.exe"
    ]
    
    for process in processes:
        try:
            subprocess.run(f'taskkill /f /im {process}', shell=True, capture_output=True)
            print(f"✅ Killed: {process}")
        except:
            print(f"⚠️ Not running: {process}")

def disable_defender_services():
    """Disable all Defender services"""
    print("🛑 DISABLING DEFENDER SERVICES...")
    
    services = ["WinDefend", "WdNisSvc", "SecurityHealthService", "Sense", "WdFilter", "WdBoot"]
    
    for service in services:
        try:
            # Stop service
            subprocess.run(f'sc stop {service}', shell=True, capture_output=True)
            # Disable service
            subprocess.run(f'sc config {service} start= disabled', shell=True, capture_output=True)
            # Registry disable
            subprocess.run(
                f'reg add "HKLM\\SYSTEM\\CurrentControlSet\\Services\\{service}" /v Start /t REG_DWORD /d 4 /f',
                shell=True, capture_output=True
            )
            print(f"✅ Service disabled: {service}")
        except Exception as e:
            print(f"❌ Service failed: {service}")

def nuclear_registry_disable():
    """Nuclear registry disable approach"""
    print("☢️ NUCLEAR REGISTRY DISABLE...")
    
    registry_settings = [
        # Main Defender disable
        (r"SOFTWARE\Policies\Microsoft\Windows Defender", "DisableAntiSpyware", 1),
        (r"SOFTWARE\Policies\Microsoft\Windows Defender", "DisableRealtimeMonitoring", 1),
        
        # Real-time protection
        (r"SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection", "DisableRealtimeMonitoring", 1),
        (r"SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection", "DisableBehaviorMonitoring", 1),
        (r"SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection", "DisableOnAccessProtection", 1),
        (r"SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection", "DisableScanOnRealtimeEnable", 1),
        
        # Cloud protection
        (r"SOFTWARE\Policies\Microsoft\Windows Defender\Spynet", "DisableBlockAtFirstSeen", 1),
        (r"SOFTWARE\Policies\Microsoft\Windows Defender\Spynet", "SpynetReporting", 0),
        (r"SOFTWARE\Policies\Microsoft\Windows Defender\Spynet", "SubmitSamplesConsent", 2),
        
        # Additional disables
        (r"SOFTWARE\Policies\Microsoft\Windows Defender\Scan", "DisableArchiveScanning", 1),
        (r"SOFTWARE\Policies\Microsoft\Windows Defender\Scan", "DisableEmailScanning", 1),
        (r"SOFTWARE\Policies\Microsoft\Windows Defender\Scan", "DisableRemovableDriveScanning", 1),
    ]
    
    success_count = 0
    for path, name, value in registry_settings:
        try:
            with winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, path) as key:
                winreg.SetValueEx(key, name, 0, winreg.REG_DWORD, value)
            print(f"✅ Registry: {name}")
            success_count += 1
        except Exception as e:
            print(f"❌ Registry: {name} - {str(e)}")
    
    return success_count

def powershell_nuclear_disable():
    """PowerShell nuclear disable"""
    print("⚡ POWERSHELL NUCLEAR DISABLE...")
    
    ps_commands = [
        "Set-MpPreference -DisableRealtimeMonitoring $true -Force",
        "Set-MpPreference -MAPSReporting Disabled -Force", 
        "Set-MpPreference -DisableBehaviorMonitoring $true -Force",
        "Set-MpPreference -DisableBlockAtFirstSeen $true -Force",
        "Set-MpPreference -DisableIOAVProtection $true -Force",
        "Set-MpPreference -DisableScriptScanning $true -Force",
        "Set-MpPreference -SubmitSamplesConsent NeverSend -Force",
        "Remove-MpPreference -ExclusionPath 'C:\\' -Force",
        "Add-MpPreference -ExclusionPath 'C:\\' -Force",
    ]
    
    success_count = 0
    for cmd in ps_commands:
        methods = [
            f'powershell.exe -Command "try {{ {cmd} }} catch {{ }}"',
            f'powershell.exe -ExecutionPolicy Bypass -Command "try {{ {cmd} }} catch {{ }}"',
            f'powershell.exe -NoProfile -ExecutionPolicy Bypass -Command "try {{ {cmd} }} catch {{ }}"'
        ]
        
        for method in methods:
            try:
                result = subprocess.run(method, shell=True, capture_output=True, timeout=30)
                if result.returncode == 0:
                    print(f"✅ PS: {cmd.split()[0]}")
                    success_count += 1
                    break
            except:
                continue
    
    return success_count

def verify_defender_status():
    """Check if Defender is actually disabled"""
    print("🔍 VERIFYING DEFENDER STATUS...")
    
    try:
        result = subprocess.run(
            'powershell.exe -Command "Get-MpPreference | Select-Object DisableRealtimeMonitoring"',
            shell=True, capture_output=True, text=True, timeout=30
        )
        
        if "True" in result.stdout:
            print("✅ VERIFICATION: Defender Real-time Protection DISABLED")
            return True
        else:
            print("❌ VERIFICATION: Defender Real-time Protection STILL ACTIVE")
            return False
    except:
        print("⚠️ VERIFICATION: Cannot check status")
        return False

def main():
    print("☢️ ULTIMATE DEFENDER KILL")
    print("=" * 50)
    print("Final automated attempt to disable Windows Defender")
    print()
    
    if not is_admin():
        print("❌ Administrator privileges required!")
        input("Press Enter to exit...")
        return
    
    print("✅ Administrator privileges confirmed")
    print("🎯 Target: Complete Windows Defender disable")
    print("⚠️ WARNING: This uses nuclear methods!")
    print()
    
    confirm = input("Type 'NUKE' to proceed with ultimate disable: ")
    if confirm != "NUKE":
        print("Operation cancelled.")
        return
    
    print("\n🚀 Starting ultimate Defender disable...")
    print()
    
    # Step 1: Disable Tamper Protection
    disable_tamper_protection_registry()
    
    # Step 2: Kill processes
    kill_defender_processes()
    
    # Step 3: Disable services
    disable_defender_services()
    
    # Step 4: Nuclear registry
    registry_success = nuclear_registry_disable()
    
    # Step 5: PowerShell nuclear
    ps_success = powershell_nuclear_disable()
    
    # Step 6: Verify
    success = verify_defender_status()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ULTIMATE DEFENDER KILL: SUCCESS!")
        print("Windows Defender should now be completely disabled!")
    else:
        print("⚠️ ULTIMATE DEFENDER KILL: PARTIAL SUCCESS")
        print("Defender may still be protected by Tamper Protection")
        print()
        print("💡 MANUAL SOLUTION:")
        print("1. Open Windows Security")
        print("2. Go to Virus & threat protection")
        print("3. Click 'Manage settings' under Real-time protection")
        print("4. Turn OFF 'Tamper Protection'")
        print("5. Run this script again")
    
    print("\n⚠️ RESTART RECOMMENDED for full effect")
    
    restart = input("\nWould you like to restart now? (y/n): ").lower().strip()
    if restart == 'y':
        print("Restarting in 10 seconds...")
        time.sleep(10)
        os.system("shutdown /r /t 0")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
