#!/usr/bin/env python3
"""
Commercial License System
License validation, trial management, and activation
"""

import json
import hashlib
import platform
import uuid
import os
from datetime import datetime, timedelta
import requests

class LicenseManager:
    def __init__(self):
        self.license_file = "license.dat"
        self.trial_file = "trial.dat"
        self.server_url = "https://api.gameboost-pro.com"  # Production server
        self.hardware_id = self.generate_hardware_id()
        
    def generate_hardware_id(self):
        """Generate unique hardware fingerprint"""
        try:
            # Get system information
            machine = platform.machine()
            processor = platform.processor()
            system = platform.system()
            
            # Get MAC address
            mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                           for elements in range(0,2*6,2)][::-1])
            
            # Create hardware fingerprint
            hw_string = f"{machine}-{processor}-{system}-{mac}"
            hw_hash = hashlib.sha256(hw_string.encode()).hexdigest()[:16]
            
            return hw_hash
        except:
            # Fallback to UUID if hardware detection fails
            return str(uuid.uuid4())[:16]
    
    def is_licensed(self):
        """Check if software is properly licensed"""
        try:
            if os.path.exists(self.license_file):
                with open(self.license_file, 'r') as f:
                    license_data = json.load(f)
                
                # Validate license
                if self.validate_license(license_data):
                    return True
            
            return False
        except:
            return False
    
    def validate_license(self, license_data):
        """Validate license data"""
        try:
            required_fields = ['key', 'hardware_id', 'activation_date', 'signature']
            
            # Check required fields
            for field in required_fields:
                if field not in license_data:
                    return False
            
            # Check hardware ID match
            if license_data['hardware_id'] != self.hardware_id:
                return False
            
            # Validate signature (simple check - in production use proper crypto)
            expected_sig = self.generate_signature(
                license_data['key'], 
                license_data['hardware_id'], 
                license_data['activation_date']
            )
            
            if license_data['signature'] != expected_sig:
                return False
            
            return True
        except:
            return False
    
    def generate_signature(self, key, hardware_id, activation_date):
        """Generate license signature"""
        data = f"{key}-{hardware_id}-{activation_date}-GAMEBOOST_SECRET"
        return hashlib.sha256(data.encode()).hexdigest()
    
    def activate_license(self, license_key):
        """Activate license with server validation"""
        try:
            # In production, validate with server
            # For demo, use simple validation
            if self.validate_license_key_format(license_key):
                
                # Create license data
                activation_date = datetime.now().isoformat()
                signature = self.generate_signature(license_key, self.hardware_id, activation_date)
                
                license_data = {
                    'key': license_key,
                    'hardware_id': self.hardware_id,
                    'activation_date': activation_date,
                    'signature': signature,
                    'product': 'GameBoost Pro',
                    'version': '1.0.0'
                }
                
                # Save license
                with open(self.license_file, 'w') as f:
                    json.dump(license_data, f, indent=2)
                
                return True, "License activated successfully!"
            else:
                return False, "Invalid license key format"
                
        except Exception as e:
            return False, f"Activation failed: {str(e)}"
    
    def validate_license_key_format(self, key):
        """Validate license key format"""
        # Simple format validation: XXXX-XXXX-XXXX-XXXX
        if len(key) != 19:  # 16 chars + 3 dashes
            return False
        
        parts = key.split('-')
        if len(parts) != 4:
            return False
        
        for part in parts:
            if len(part) != 4 or not part.isalnum():
                return False
        
        return True
    
    def get_trial_info(self):
        """Get trial information"""
        try:
            if os.path.exists(self.trial_file):
                with open(self.trial_file, 'r') as f:
                    trial_data = json.load(f)
                
                start_date = datetime.fromisoformat(trial_data['start_date'])
                trial_days = trial_data.get('trial_days', 7)
                end_date = start_date + timedelta(days=trial_days)
                
                days_left = (end_date - datetime.now()).days
                
                return {
                    'active': days_left > 0,
                    'days_left': max(0, days_left),
                    'start_date': start_date,
                    'end_date': end_date
                }
            else:
                # First run - create trial
                return self.start_trial()
                
        except:
            return self.start_trial()
    
    def start_trial(self):
        """Start trial period"""
        try:
            trial_data = {
                'start_date': datetime.now().isoformat(),
                'trial_days': 7,
                'hardware_id': self.hardware_id
            }
            
            with open(self.trial_file, 'w') as f:
                json.dump(trial_data, f, indent=2)
            
            return {
                'active': True,
                'days_left': 7,
                'start_date': datetime.now(),
                'end_date': datetime.now() + timedelta(days=7)
            }
        except:
            return {
                'active': False,
                'days_left': 0,
                'start_date': datetime.now(),
                'end_date': datetime.now()
            }
    
    def is_trial_valid(self):
        """Check if trial is still valid"""
        if self.is_licensed():
            return True
        
        trial_info = self.get_trial_info()
        return trial_info['active']
    
    def get_license_status(self):
        """Get comprehensive license status"""
        if self.is_licensed():
            try:
                with open(self.license_file, 'r') as f:
                    license_data = json.load(f)
                
                return {
                    'type': 'licensed',
                    'status': 'active',
                    'key': license_data['key'][:4] + '-****-****-' + license_data['key'][-4:],
                    'activation_date': license_data['activation_date'],
                    'hardware_id': self.hardware_id
                }
            except:
                return {'type': 'error', 'status': 'license_corrupted'}
        else:
            trial_info = self.get_trial_info()
            return {
                'type': 'trial',
                'status': 'active' if trial_info['active'] else 'expired',
                'days_left': trial_info['days_left'],
                'hardware_id': self.hardware_id
            }
    
    def deactivate_license(self):
        """Deactivate current license"""
        try:
            if os.path.exists(self.license_file):
                os.remove(self.license_file)
            return True
        except:
            return False
    
    def generate_demo_license_key(self):
        """Generate a demo license key for testing"""
        # This is for demo purposes only
        import random
        import string
        
        def random_segment():
            return ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        
        return f"{random_segment()}-{random_segment()}-{random_segment()}-{random_segment()}"

# Example usage and testing
if __name__ == "__main__":
    license_manager = LicenseManager()
    
    print("🔑 License System Test")
    print("=" * 30)
    
    # Check current status
    status = license_manager.get_license_status()
    print(f"Current Status: {status}")
    
    # Generate demo key
    demo_key = license_manager.generate_demo_license_key()
    print(f"Demo License Key: {demo_key}")
    
    # Test activation
    success, message = license_manager.activate_license(demo_key)
    print(f"Activation: {success} - {message}")
    
    # Check status after activation
    status = license_manager.get_license_status()
    print(f"New Status: {status}")
