@echo off
title GameBoost Pro v1.0.0 - Professional Gaming Optimization Suite
color 0E

echo.
echo  ==========================================
echo   🎮 GAMEBOOST PRO v1.0.0
echo   Professional Gaming Optimization Suite
echo  ==========================================
echo.
echo   Developed by TechFlow Solutions
echo   © 2024 All Rights Reserved
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    REM Check if Python is installed
    python --version >nul 2>&1
    if %errorLevel% == 0 (
        echo  [✓] Python installation detected
        echo.
        
        REM Check if required modules exist
        if exist "main.py" (
            echo  [✓] GameBoost Pro files detected
            echo.
            
            echo  🚀 Starting GameBoost Pro...
            echo.
            
            REM Launch the application
            python main.py
            
        ) else (
            echo  [!] ERROR: GameBoost Pro files not found
            echo.
            echo  Please ensure you have extracted all files to the same directory.
            echo  Required files:
            echo    • main.py
            echo    • security_toggle.py
            echo    • branding.py
            echo    • license_system.py
            echo    • modules/ folder
            echo.
            pause
        )
        
    ) else (
        echo  [!] ERROR: Python not installed
        echo.
        echo  GameBoost Pro requires Python 3.8 or higher.
        echo.
        echo  AUTOMATIC PYTHON INSTALLATION:
        echo  ==============================
        echo.
        
        choice /c YN /m "Would you like to download and install Python automatically? (Y/N)"
        if errorlevel 2 goto :no_python
        if errorlevel 1 goto :install_python
        
        :install_python
        echo.
        echo  [🔄] Downloading Python installer...
        
        REM Download Python installer
        powershell -Command "Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.0/python-3.11.0-amd64.exe' -OutFile 'python_installer.exe'"
        
        if exist "python_installer.exe" (
            echo  [✓] Python installer downloaded
            echo.
            echo  [🔄] Installing Python...
            echo      This may take a few minutes...
            
            REM Install Python silently
            python_installer.exe /quiet InstallAllUsers=1 PrependPath=1
            
            echo  [✓] Python installation completed
            echo.
            echo  [🔄] Cleaning up...
            del python_installer.exe
            
            echo  [✓] Please restart this script to launch GameBoost Pro
            pause
            
        ) else (
            echo  [!] Failed to download Python installer
            echo.
            echo  MANUAL INSTALLATION:
            echo  1. Visit: https://www.python.org/downloads/
            echo  2. Download Python 3.8 or higher
            echo  3. Install with "Add to PATH" option checked
            echo  4. Restart this script
            echo.
            pause
        )
        
        goto :end
        
        :no_python
        echo.
        echo  MANUAL PYTHON INSTALLATION:
        echo  ==========================
        echo  1. Visit: https://www.python.org/downloads/
        echo  2. Download Python 3.8 or higher  
        echo  3. Install with "Add to PATH" option checked
        echo  4. Restart this script
        echo.
        pause
        goto :end
    )
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  GameBoost Pro requires administrator privileges to modify
    echo  Windows security settings safely and effectively.
    echo.
    echo  HOW TO RUN AS ADMINISTRATOR:
    echo  ===========================
    echo  1. Right-click this file (GAMEBOOST_PRO.bat)
    echo  2. Select "Run as administrator"
    echo  3. Click "Yes" when prompted by UAC
    echo.
    
    choice /c YN /m "Would you like to restart with administrator privileges? (Y/N)"
    if errorlevel 2 goto :end
    if errorlevel 1 goto :restart_admin
    
    :restart_admin
    echo.
    echo  [🔄] Restarting with administrator privileges...
    
    REM Try to restart with admin privileges
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    goto :end
)

:end
echo.
echo  ==========================================
echo   Thank you for using GameBoost Pro!
echo   Visit: https://gameboost-pro.com
echo  ==========================================
echo.
pause
exit /b
