#!/usr/bin/env python3
"""
Enhanced Security Toggle
Improved version with better error handling and service recovery
"""

import subprocess
import time
import json
import os

class EnhancedSecurityToggle:
    def __init__(self):
        self.results = []
        self.state_file = "security_state.json"
        
    def log(self, message):
        """Safe logging with encoding handling"""
        try:
            safe_message = str(message).encode('ascii', 'ignore').decode('ascii')
            print(safe_message)
            self.results.append(safe_message)
        except:
            print("[LOG] Operation completed")
            self.results.append("[LOG] Operation completed")
    
    def run_command_with_retry(self, command, description, retries=3):
        """Run command with retry logic"""
        for attempt in range(retries):
            try:
                result = subprocess.run(
                    command, 
                    shell=True, 
                    capture_output=True, 
                    text=True, 
                    timeout=30,
                    encoding='utf-8',
                    errors='ignore'
                )
                
                if result.returncode == 0:
                    self.log(f"✅ {description}: SUCCESS")
                    return True
                else:
                    if attempt < retries - 1:
                        self.log(f"⚠️ {description}: RETRY {attempt + 1}")
                        time.sleep(2)  # Wait before retry
                    else:
                        self.log(f"❌ {description}: FAILED")
                        return False
                        
            except Exception as e:
                if attempt < retries - 1:
                    self.log(f"⚠️ {description}: RETRY {attempt + 1} (Error: {str(e)[:30]})")
                    time.sleep(2)
                else:
                    self.log(f"❌ {description}: FAILED (Error: {str(e)[:30]})")
                    return False
        
        return False
    
    def fix_service_dependencies(self):
        """Fix common service dependency issues"""
        self.log("🔧 FIXING SERVICE DEPENDENCIES...")
        
        # Fix common dependency services first
        dependency_services = [
            ('sc config RpcSs start= auto', 'RPC Service Auto'),
            ('sc start RpcSs', 'RPC Service Start'),
            ('sc config DcomLaunch start= auto', 'DCOM Launch Auto'),
            ('sc start DcomLaunch', 'DCOM Launch Start'),
            ('sc config EventLog start= auto', 'Event Log Auto'),
            ('sc start EventLog', 'Event Log Start'),
        ]
        
        for cmd, desc in dependency_services:
            self.run_command_with_retry(cmd, desc, retries=2)
    
    def force_service_start(self, service_name, display_name):
        """Force start a service with multiple methods"""
        self.log(f"🔄 FORCE STARTING {display_name}...")
        
        # Method 1: Standard start
        if self.run_command_with_retry(f'sc start {service_name}', f'{display_name} Standard Start'):
            return True
        
        # Method 2: Net start
        if self.run_command_with_retry(f'net start {service_name}', f'{display_name} Net Start'):
            return True
        
        # Method 3: PowerShell start
        ps_cmd = f'powershell.exe -Command "Start-Service -Name {service_name} -ErrorAction SilentlyContinue"'
        if self.run_command_with_retry(ps_cmd, f'{display_name} PowerShell Start'):
            return True
        
        # Method 4: Force start with dependencies
        dep_cmd = f'sc start {service_name} depend= force'
        if self.run_command_with_retry(dep_cmd, f'{display_name} Force Start'):
            return True
        
        self.log(f"❌ {display_name}: ALL START METHODS FAILED")
        return False
    
    def enhanced_enable_windows_update(self):
        """Enhanced Windows Update enable with better error handling"""
        self.log("🔄 ENHANCED WINDOWS UPDATE ENABLE...")
        self.log("=" * 50)
        
        success_count = 0
        total_operations = 0
        
        # Step 1: Fix service dependencies first
        self.fix_service_dependencies()
        
        # Step 2: Registry fixes with elevated permissions
        self.log("📝 FIXING UPDATE REGISTRY (ENHANCED)...")
        
        registry_commands = [
            ('reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate" /v NoAutoUpdate /f', 'Remove NoAutoUpdate'),
            ('reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate" /v DisableWindowsUpdateAccess /f', 'Remove Update Access Block'),
            ('reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU" /v NoAutoUpdate /f', 'Remove AU NoAutoUpdate'),
            ('reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\WindowsUpdate\\Auto Update" /v AUOptions /t REG_DWORD /d 4 /f', 'Enable Auto Updates'),
            ('reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU" /v AUOptions /t REG_DWORD /d 4 /f', 'Policy Auto Updates'),
        ]
        
        for cmd, desc in registry_commands:
            if self.run_command_with_retry(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Step 3: Enhanced service configuration
        self.log("🔧 ENHANCED SERVICE CONFIGURATION...")
        
        # Configure services with dependencies
        service_configs = [
            ('sc config wuauserv start= auto depend= RpcSs', 'Windows Update Auto with Dependencies'),
            ('sc config UsoSvc start= auto depend= RpcSs', 'Update Orchestrator Auto with Dependencies'),
            ('sc config BITS start= auto depend= RpcSs/EventLog', 'BITS Auto with Dependencies'),
            ('sc config DoSvc start= auto depend= RpcSs', 'Delivery Optimization Auto with Dependencies'),
        ]
        
        for cmd, desc in service_configs:
            if self.run_command_with_retry(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Step 4: Force start services
        self.log("🚀 FORCE STARTING UPDATE SERVICES...")
        
        services_to_start = [
            ('wuauserv', 'Windows Update Service'),
            ('UsoSvc', 'Update Orchestrator Service'),
            ('BITS', 'Background Transfer Service'),
            ('DoSvc', 'Delivery Optimization Service'),
        ]
        
        for service_name, display_name in services_to_start:
            if self.force_service_start(service_name, display_name):
                success_count += 1
            total_operations += 1
        
        # Step 5: PowerShell enhancements
        self.log("⚡ POWERSHELL UPDATE ENHANCEMENTS...")
        
        ps_commands = [
            ('powershell.exe -Command "Set-ItemProperty -Path \'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\WindowsUpdate\\Auto Update\' -Name AUOptions -Value 4 -Force"', 'PS Auto Update Options'),
            ('powershell.exe -Command "Get-Service wuauserv | Set-Service -StartupType Automatic -PassThru | Start-Service -ErrorAction SilentlyContinue"', 'PS Windows Update Auto'),
            ('powershell.exe -Command "Get-Service UsoSvc | Set-Service -StartupType Automatic -PassThru | Start-Service -ErrorAction SilentlyContinue"', 'PS Update Orchestrator Auto'),
            ('powershell.exe -Command "Get-Service BITS | Set-Service -StartupType Automatic -PassThru | Start-Service -ErrorAction SilentlyContinue"', 'PS BITS Auto'),
        ]
        
        for cmd, desc in ps_commands:
            if self.run_command_with_retry(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Step 6: Force update check
        self.log("🔍 FORCING UPDATE CHECK...")
        
        update_commands = [
            ('powershell.exe -Command "Start-Service wuauserv -ErrorAction SilentlyContinue"', 'Ensure Update Service Running'),
            ('powershell.exe -Command "(New-Object -ComObject Microsoft.Update.AutoUpdate).DetectNow()"', 'Force Update Detection'),
            ('powershell.exe -Command "UsoClient StartScan"', 'Start Update Scan'),
        ]
        
        for cmd, desc in update_commands:
            if self.run_command_with_retry(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Results
        percentage = (success_count / total_operations) * 100 if total_operations > 0 else 0
        self.log(f"📊 ENHANCED WINDOWS UPDATE RESULTS: {success_count}/{total_operations} ({percentage:.1f}%)")
        
        if percentage >= 70:
            self.log("✅ WINDOWS UPDATE SUCCESSFULLY ENABLED")
            return True
        else:
            self.log("⚠️ WINDOWS UPDATE PARTIALLY ENABLED")
            return False
    
    def enhanced_enable_firewall(self):
        """Enhanced firewall enable with better error handling"""
        self.log("🔥 ENHANCED FIREWALL ENABLE...")
        
        success_count = 0
        total_operations = 0
        
        # Step 1: Fix firewall service dependencies
        self.log("🔧 FIXING FIREWALL DEPENDENCIES...")
        
        firewall_deps = [
            ('sc config BFE start= auto', 'Base Filtering Engine Auto'),
            ('sc start BFE', 'Base Filtering Engine Start'),
            ('sc config PolicyAgent start= auto', 'Policy Agent Auto'),
            ('sc start PolicyAgent', 'Policy Agent Start'),
        ]
        
        for cmd, desc in firewall_deps:
            if self.run_command_with_retry(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Step 2: Configure firewall service
        firewall_configs = [
            ('sc config MpsSvc start= auto depend= BFE/RpcSs', 'Firewall Service Auto with Dependencies'),
            ('sc start MpsSvc', 'Firewall Service Start'),
        ]
        
        for cmd, desc in firewall_configs:
            if self.run_command_with_retry(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Step 3: Enable firewall profiles
        firewall_commands = [
            ('netsh advfirewall set allprofiles state on', 'All Firewall Profiles'),
            ('netsh advfirewall set domainprofile state on', 'Domain Profile'),
            ('netsh advfirewall set privateprofile state on', 'Private Profile'),
            ('netsh advfirewall set publicprofile state on', 'Public Profile'),
        ]
        
        for cmd, desc in firewall_commands:
            if self.run_command_with_retry(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Step 4: PowerShell firewall enable
        ps_firewall_commands = [
            ('powershell.exe -Command "Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled True"', 'PS Firewall Profiles'),
            ('powershell.exe -Command "Get-Service MpsSvc | Set-Service -StartupType Automatic -PassThru | Start-Service"', 'PS Firewall Service'),
        ]
        
        for cmd, desc in ps_firewall_commands:
            if self.run_command_with_retry(cmd, desc):
                success_count += 1
            total_operations += 1
        
        percentage = (success_count / total_operations) * 100 if total_operations > 0 else 0
        self.log(f"📊 ENHANCED FIREWALL RESULTS: {success_count}/{total_operations} ({percentage:.1f}%)")
        
        return percentage >= 70
    
    def get_results(self):
        """Get all logged results"""
        return self.results
    
    def clear_results(self):
        """Clear all results"""
        self.results = []

def main():
    """Test the enhanced security toggle"""
    enhanced = EnhancedSecurityToggle()
    
    print("🔧 TESTING ENHANCED SECURITY TOGGLE")
    print("=" * 50)
    
    # Test Windows Update enable
    enhanced.enhanced_enable_windows_update()
    print()
    
    # Test Firewall enable
    enhanced.enhanced_enable_firewall()
    
    print()
    print("✅ Enhanced security toggle test completed!")

if __name__ == "__main__":
    main()
