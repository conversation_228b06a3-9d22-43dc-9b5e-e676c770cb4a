#!/usr/bin/env python3
"""
Aggressive Gaming Mode
Continues trying different methods until ALL security features are disabled
"""

from security_toggle import SecurityToggle
import time
import sys

def verify_all_disabled():
    """Verify that all security features are actually disabled"""
    toggle = SecurityToggle()
    state = toggle.get_current_security_state()
    
    results = {
        "defender_realtime": not state["defender_realtime"],
        "firewall_enabled": not state["firewall_enabled"], 
        "uac_enabled": not state["uac_enabled"],
        "smartscreen_enabled": not state["smartscreen_enabled"],
        "fast_boot_enabled": not state["fast_boot_enabled"],
        "windows_update_enabled": not state["windows_update_enabled"]
    }
    
    return results

def main():
    print("⚔️ AGGRESSIVE GAMING MODE")
    print("=" * 50)
    print("Continues until ALL security features are disabled!")
    print()
    
    toggle = SecurityToggle()
    
    if not toggle.is_admin():
        print("❌ Administrator privileges required!")
        print("Right-click and 'Run as administrator'")
        input("Press Enter to exit...")
        return
    
    print("✅ Administrator privileges confirmed")
    print("🎯 Target: 100% security disable success")
    print()
    
    max_attempts = 3
    attempt = 1
    
    while attempt <= max_attempts:
        print(f"🚀 ATTEMPT {attempt}/{max_attempts}")
        print("-" * 30)
        
        try:
            # Run gaming mode
            result = toggle.enable_gaming_mode()
            
            # Verify results
            print("\n🔍 VERIFYING RESULTS...")
            verification = verify_all_disabled()
            
            disabled_count = sum(verification.values())
            total_features = len(verification)
            
            print(f"\n📊 VERIFICATION RESULTS: {disabled_count}/{total_features}")
            for feature, disabled in verification.items():
                status = "✅ DISABLED" if disabled else "❌ STILL ACTIVE"
                print(f"   {feature.replace('_', ' ').title()}: {status}")
            
            if disabled_count == total_features:
                print("\n🎉 SUCCESS! ALL SECURITY FEATURES DISABLED!")
                print("🎮 System is 100% ready for gaming tools!")
                break
            else:
                print(f"\n⚠️ PARTIAL SUCCESS: {disabled_count}/{total_features} disabled")
                if attempt < max_attempts:
                    print(f"🔄 Trying again with more aggressive methods...")
                    time.sleep(2)
                else:
                    print("⚠️ Maximum attempts reached")
                    print("💡 System should still work for most gaming tools")
        
        except Exception as e:
            print(f"❌ Error during attempt {attempt}: {str(e)}")
        
        attempt += 1
        print()
    
    print("\n" + "=" * 50)
    print("🎯 FINAL STATUS:")
    
    # Final verification
    final_verification = verify_all_disabled()
    final_disabled = sum(final_verification.values())
    final_total = len(final_verification)
    
    if final_disabled == final_total:
        print("✅ COMPLETE SUCCESS - All security disabled!")
    elif final_disabled >= final_total * 0.8:
        print("✅ HIGH SUCCESS - Most security disabled!")
    else:
        print("⚠️ PARTIAL SUCCESS - Some security disabled")
    
    print(f"📊 Final Score: {final_disabled}/{final_total} ({final_disabled/final_total*100:.1f}%)")
    
    print("\n💡 RECOMMENDATIONS:")
    if final_disabled == final_total:
        print("🎮 Ready for all gaming tools!")
        print("🔄 Use Safe Mode to restore security when done")
    else:
        print("🎮 Should work for most gaming tools")
        print("🔧 Manual disable may be needed for stubborn features")
        print("🔄 Restart and try again for better results")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
