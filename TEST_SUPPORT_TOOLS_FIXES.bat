@echo off
title TEST SUPPORT TOOLS FIXES
color 0A

echo.
echo  ==========================================
echo   🧪 TEST SUPPORT TOOLS FIXES
echo  ==========================================
echo.
echo  This will test if our fixes resolve the
echo  issues detected by Support Tools.exe
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  🔍 TESTING CURRENT STATUS...
    echo.
    
    REM Test Secure Boot status
    echo  [1/4] Testing Secure Boot...
    powershell -Command "try { Confirm-SecureBootUEFI } catch { Write-Host 'Secure Boot check failed or disabled' }"
    
    echo.
    echo  [2/4] Testing Virtualization...
    systeminfo | findstr /C:"Hyper-V Requirements"
    
    echo.
    echo  [3/4] Testing Firewall...
    netsh advfirewall show allprofiles state | findstr "State"
    
    echo.
    echo  [4/4] Testing Windows Defender...
    powershell -Command "Get-MpPreference | Select-Object DisableRealtimeMonitoring"
    
    echo.
    echo  ==========================================
    echo   📊 TEST RESULTS SUMMARY
    echo  ==========================================
    echo.
    echo  ✅ If Secure Boot shows 'False' or error = GOOD
    echo  ✅ If Virtualization shows enabled = GOOD  
    echo  ✅ If Firewall shows ON = GOOD
    echo  ✅ If Real-time shows False = GOOD (disabled)
    echo.
    echo  💡 NEXT STEPS:
    echo  1. If issues remain, run FIX_SUPPORT_TOOLS_ISSUES.bat
    echo  2. Restart system after fixes
    echo  3. Run Support Tools.exe to verify
    echo  4. All checks should pass
    echo.
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This test requires administrator privileges to
    echo  check system security settings properly.
    echo.
    echo  HOW TO RUN AS ADMINISTRATOR:
    echo  ===========================
    echo  1. Right-click this file (TEST_SUPPORT_TOOLS_FIXES.bat)
    echo  2. Select "Run as administrator"
    echo  3. Click "Yes" when prompted by UAC
    echo.
    
    choice /c YN /m "Would you like to restart with administrator privileges? (Y/N)"
    if errorlevel 2 goto :end
    if errorlevel 1 goto :restart_admin
    
    :restart_admin
    echo.
    echo  🔄 Restarting with administrator privileges...
    
    REM Try to restart with admin privileges
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    goto :end
)

:end
echo.
echo  ==========================================
echo   Test Complete
echo  ==========================================
echo.
pause
exit /b
