#!/usr/bin/env python3
"""
Simple Gaming Tool Launcher
Launches the interface with proper admin checking
"""

import sys
import os
import ctypes
import subprocess
from pathlib import Path

def check_admin():
    """Check if running as administrator"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def main():
    print("🎮 GAMING TOOL LAUNCHER")
    print("=" * 30)
    
    # Check admin status
    if check_admin():
        print("✅ Administrator privileges: CONFIRMED")
    else:
        print("❌ Administrator privileges: MISSING")
        print("\nTo enable all features, please:")
        print("1. Right-click this file")
        print("2. Select 'Run as administrator'")
        print("\nContinuing with limited functionality...")
    
    print()
    
    # Check if interface file exists
    interface_file = Path("gaming_tool_interface.py")
    if not interface_file.exists():
        print("❌ Interface file not found!")
        print("Looking for gaming_tool_interface.py...")
        input("Press Enter to exit...")
        return
    
    print("✅ Interface file found")
    print("🚀 Launching gaming tool interface...")
    print()
    
    try:
        # Launch the interface
        if check_admin():
            print("🛡️ Running with FULL ADMIN PRIVILEGES")
            print("   • Can modify security settings")
            print("   • Can disable Windows Defender")
            print("   • Can change system configurations")
        else:
            print("⚠️ Running with LIMITED PRIVILEGES")
            print("   • Some features may not work")
            print("   • Security changes will fail")
            print("   • Manual admin required for full setup")
        
        print()
        print("Starting interface...")
        
        # Import and run the interface
        sys.path.append(str(Path(__file__).parent))
        
        # Try to import the interface
        try:
            from gaming_tool_interface import GamingToolInterface
            app = GamingToolInterface()
            app.run()
        except ImportError as e:
            print(f"❌ Import error: {str(e)}")
            print("Trying alternative launch method...")
            
            # Alternative: run as subprocess
            result = subprocess.run([sys.executable, "gaming_tool_interface.py"], 
                                  capture_output=False)
            if result.returncode != 0:
                print("❌ Interface failed to launch")
        
    except Exception as e:
        print(f"❌ Error launching interface: {str(e)}")
        print("\nTroubleshooting:")
        print("1. Make sure Python is installed")
        print("2. Install required packages: pip install customtkinter")
        print("3. Run as administrator for full functionality")
        
    print("\nInterface closed.")
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
