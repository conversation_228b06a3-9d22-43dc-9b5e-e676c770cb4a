@echo off
title DOWNLOAD DEFENDER KILLER TOOL
color 0E

echo.
echo  ==========================================
echo   🔽 DOWNLOAD DEFENDER KILLER TOOL
echo  ==========================================
echo.
echo  This will download the advanced Windows Defender
echo  disable tool from MEGA for use as a fallback
echo  when standard methods fail.
echo.

REM Check if tool already exists
if exist "defender_killer.exe" (
    echo  ✅ Defender killer tool already exists!
    echo  📁 File: defender_killer.exe
    echo.
    choice /c YN /m "Download again? (Y/N)"
    if errorlevel 2 goto :skip_download
)

echo  🎯 Attempting to download Windows Defender killer tool...
echo.

REM Try Python downloader first
echo  [1/2] Trying Python downloader...
python download_defender_killer.py
if exist "defender_killer.exe" (
    echo  ✅ Download successful via Python!
    goto :success
)

REM Manual download instructions
echo  [2/2] Manual download required...
echo.
echo  💡 MANUAL DOWNLOAD INSTRUCTIONS:
echo  ==========================================
echo  1. Open your web browser
echo  2. Go to this URL:
echo     https://mega.nz/file/QHNRgYxB#61QJA0UE-03CDNxLg19y1vxYuNjagZNIEaaQnu9guN0
echo  3. Click 'Download' button
echo  4. Save the file as 'defender_killer.exe' in this directory
echo  5. Run the Gaming Mode again
echo.

REM Try to open browser
echo  🌐 Opening browser automatically...
start "" "https://mega.nz/file/QHNRgYxB#61QJA0UE-03CDNxLg19y1vxYuNjagZNIEaaQnu9guN0"
echo  ✅ Browser opened with download link
echo.

choice /c YN /m "Did you download the file? (Y/N)"
if errorlevel 2 goto :no_download

if exist "defender_killer.exe" (
    goto :success
) else (
    echo  ❌ File not found. Please ensure you saved it as 'defender_killer.exe'
    goto :no_download
)

:success
echo.
echo  ==========================================
echo   🎉 DEFENDER KILLER TOOL READY!
echo  ==========================================
echo.
echo  ✅ The Gaming Mode will now automatically use
echo     this tool as a fallback when standard
echo     Windows Defender disable methods fail.
echo.
echo  📁 Tool location: defender_killer.exe
echo  🎮 Ready for Gaming Mode!
echo.
goto :end

:skip_download
echo  Keeping existing defender_killer.exe file.
goto :success

:no_download
echo.
echo  ⚠️ Download not completed.
echo  The Gaming Mode will work with standard methods,
echo  but may not be able to disable stubborn Defender.
echo.

:end
pause
exit /b
