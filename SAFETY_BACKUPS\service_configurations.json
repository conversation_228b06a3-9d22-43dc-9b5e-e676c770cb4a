{"MpsSvc": {"config": "[SC] QueryServiceConfig SUCCESS\n\nSERVICE_NAME: MpsSvc\n        TYPE               : 20  WIN32_SHARE_PROCESS \n        START_TYPE         : 4   DISABLED\n        ERROR_CONTROL      : 1   NORMAL\n        BINARY_PATH_NAME   : C:\\WINDOWS\\system32\\svchost.exe -k LocalServiceNoNetworkFirewall -p\n        LOAD_ORDER_GROUP   : NetworkProvider\n        TAG                : 0\n        DISPLAY_NAME       : Windows Defender Firewall\n        DEPENDENCIES       : mpsdrv\n                           : bfe\n                           : nsi\n        SERVICE_START_NAME : NT Authority\\LocalService\n", "timestamp": "2025-06-17T12:59:40.215315"}, "WinDefend": {"config": "[SC] QueryServiceConfig SUCCESS\n\nSERVICE_NAME: WinDefend\n        TYPE               : 10  WIN32_OWN_PROCESS \n        START_TYPE         : 2   AUTO_START\n        ERROR_CONTROL      : 1   NORMAL\n        BINARY_PATH_NAME   : \"C:\\ProgramData\\Microsoft\\Windows Defender\\Platform\\4.18.25050.5-0\\MsMpEng.exe\"\n        LOAD_ORDER_GROUP   : \n        TAG                : 0\n        DISPLAY_NAME       : Microsoft Defender Antivirus Service\n        DEPENDENCIES       : RpcSs\n        SERVICE_START_NAME : LocalSystem\n", "timestamp": "2025-06-17T12:59:40.235416"}, "wuauserv": {"config": "[SC] QueryServiceConfig SUCCESS\n\nSERVICE_NAME: wuauserv\n        TYPE               : 20  WIN32_SHARE_PROCESS \n        START_TYPE         : 2   AUTO_START\n        ERROR_CONTROL      : 1   NORMAL\n        BINARY_PATH_NAME   : C:\\WINDOWS\\system32\\svchost.exe -k netsvcs -p\n        LOAD_ORDER_GROUP   : \n        TAG                : 0\n        DISPLAY_NAME       : Windows Update\n        DEPENDENCIES       : rpcss\n        SERVICE_START_NAME : LocalSystem\n", "timestamp": "2025-06-17T12:59:40.253429"}, "UsoSvc": {"config": "[SC] QueryServiceConfig SUCCESS\n\nSERVICE_NAME: UsoSvc\n        TYPE               : 20  WIN32_SHARE_PROCESS \n        START_TYPE         : 2   AUTO_START\n        ERROR_CONTROL      : 1   NORMAL\n        BINARY_PATH_NAME   : C:\\WINDOWS\\system32\\svchost.exe -k netsvcs -p\n        LOAD_ORDER_GROUP   : \n        TAG                : 0\n        DISPLAY_NAME       : Update Orchestrator Service\n        DEPENDENCIES       : rpcss\n        SERVICE_START_NAME : LocalSystem\n", "timestamp": "2025-06-17T12:59:40.271233"}, "bits": {"config": "[SC] QueryServiceConfig SUCCESS\n\nSERVICE_NAME: bits\n        TYPE               : 20  WIN32_SHARE_PROCESS \n        START_TYPE         : 2   AUTO_START\n        ERROR_CONTROL      : 1   NORMAL\n        BINARY_PATH_NAME   : C:\\WINDOWS\\System32\\svchost.exe -k netsvcs -p\n        LOAD_ORDER_GROUP   : \n        TAG                : 0\n        DISPLAY_NAME       : Background Intelligent Transfer Service\n        DEPENDENCIES       : RpcSs\n        SERVICE_START_NAME : LocalSystem\n", "timestamp": "2025-06-17T12:59:40.288919"}}