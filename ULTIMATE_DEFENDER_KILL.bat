@echo off
title ULTIMATE DEFENDER KILL - NUCLEAR OPTION
color 0C

echo.
echo  ==========================================
echo   ☢️ ULTIMATE DEFENDER KILL - NUCLEAR OPTION
echo  ==========================================
echo.
echo  Windows Defender is still active despite all attempts.
echo  This is the NUCLEAR OPTION - most aggressive approach.
echo.
echo  What this will do:
echo  ☢️ Nuclear registry disable
echo  💀 Kill all Defender processes
echo  🛑 Disable all Defender services  
echo  ⚡ PowerShell nuclear disable
echo  🔒 Attempt Tamper Protection bypass
echo.
echo  ⚠️ WARNING: This is the most aggressive method!
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  [!] FINAL WARNING: Nuclear Defender disable!
    echo  [!] This uses the most aggressive methods available!
    echo.
    pause
    
    REM Change to script directory
    cd /d "%~dp0"
    
    echo  [☢️] Starting Ultimate Defender Kill...
    echo.
    
    REM Run the ultimate script
    python ULTIMATE_DEFENDER_KILL.py
    
    echo.
    echo  Ultimate Defender Kill completed!
    echo.
    pause
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator.
    echo  Right-click this file and select "Run as administrator"
    echo.
    pause
    
    REM Try to restart with admin privileges
    echo  Attempting to restart with administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
)

exit /b
