@echo off
title Gaming Tool Setup - Administrator Mode
color 0B

echo.
echo  ========================================
echo   GAMING TOOL SETUP - ADMINISTRATOR MODE
echo  ========================================
echo.
echo  This will fix all issues and ensure 100%% verification pass
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    echo  [✓] Starting comprehensive setup...
    echo.
    
    REM Run the fix script
    python fix_all_issues.py
    
    echo.
    echo  Setup process completed!
    echo.
    pause
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script will now restart with administrator privileges...
    echo.
    pause
    
    REM Restart with admin privileges
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
)

exit /b
