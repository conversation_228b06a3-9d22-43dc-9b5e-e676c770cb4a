#!/usr/bin/env python3
"""
Professional Branding System
Commercial branding, logos, and UI elements
"""

import customtkinter as ctk
from tkinter import messagebox
import webbrowser

class BrandingManager:
    def __init__(self):
        self.product_name = "GameBoost Pro"
        self.version = "1.0.0"
        self.company = "TechFlow Solutions"
        self.website = "https://gameboost-pro.com"
        self.support_email = "<EMAIL>"
        self.license_key = None
        self.is_licensed = False
        self.trial_days_left = 7
        
    def get_product_title(self):
        """Get formatted product title"""
        return f"{self.product_name} v{self.version}"
    
    def get_window_title(self):
        """Get window title with license status"""
        if self.is_licensed:
            return f"{self.product_name} v{self.version} - Licensed"
        else:
            return f"{self.product_name} v{self.version} - Trial ({self.trial_days_left} days left)"
    
    def create_header_frame(self, parent):
        """Create professional header with branding"""
        header_frame = ctk.CTkFrame(parent)
        header_frame.pack(fill="x", padx=20, pady=(20, 10))
        
        # Logo and title section
        title_frame = ctk.CTkFrame(header_frame)
        title_frame.pack(fill="x", padx=15, pady=15)
        
        # Product logo/icon (using emoji for now, can be replaced with actual logo)
        logo_label = ctk.CTkLabel(
            title_frame, 
            text="🎮", 
            font=ctk.CTkFont(size=48)
        )
        logo_label.pack(side="left", padx=(10, 20))
        
        # Title and tagline
        text_frame = ctk.CTkFrame(title_frame)
        text_frame.pack(side="left", fill="both", expand=True)
        
        title_label = ctk.CTkLabel(
            text_frame,
            text=self.get_product_title(),
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(anchor="w", padx=10, pady=(10, 0))
        
        tagline_label = ctk.CTkLabel(
            text_frame,
            text="Professional Gaming Optimization Suite",
            font=ctk.CTkFont(size=14),
            text_color="gray"
        )
        tagline_label.pack(anchor="w", padx=10, pady=(0, 10))
        
        # License status
        status_frame = ctk.CTkFrame(title_frame)
        status_frame.pack(side="right", padx=(20, 10))
        
        if self.is_licensed:
            status_text = "✅ LICENSED"
            status_color = "green"
        else:
            status_text = f"⏰ TRIAL ({self.trial_days_left} days)"
            status_color = "orange"
        
        status_label = ctk.CTkLabel(
            status_frame,
            text=status_text,
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color=status_color
        )
        status_label.pack(padx=15, pady=10)
        
        return header_frame
    
    def create_footer_frame(self, parent):
        """Create professional footer with links"""
        footer_frame = ctk.CTkFrame(parent)
        footer_frame.pack(fill="x", padx=20, pady=(10, 20))
        
        # Company info
        company_label = ctk.CTkLabel(
            footer_frame,
            text=f"© 2024 {self.company} - All Rights Reserved",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        company_label.pack(side="left", padx=15, pady=8)
        
        # Action buttons
        button_frame = ctk.CTkFrame(footer_frame)
        button_frame.pack(side="right", padx=15, pady=5)
        
        # Website button
        website_btn = ctk.CTkButton(
            button_frame,
            text="🌐 Website",
            command=self.open_website,
            width=80,
            height=25,
            font=ctk.CTkFont(size=10)
        )
        website_btn.pack(side="left", padx=2)
        
        # Support button
        support_btn = ctk.CTkButton(
            button_frame,
            text="📧 Support",
            command=self.open_support,
            width=80,
            height=25,
            font=ctk.CTkFont(size=10)
        )
        support_btn.pack(side="left", padx=2)
        
        # About button
        about_btn = ctk.CTkButton(
            button_frame,
            text="ℹ️ About",
            command=self.show_about,
            width=70,
            height=25,
            font=ctk.CTkFont(size=10)
        )
        about_btn.pack(side="left", padx=2)
        
        return footer_frame
    
    def open_website(self):
        """Open company website"""
        webbrowser.open(self.website)
    
    def open_support(self):
        """Open support email"""
        webbrowser.open(f"mailto:{self.support_email}")
    
    def show_about(self):
        """Show about dialog"""
        about_text = f"""
{self.product_name} v{self.version}

Professional Gaming Optimization Suite

🎮 FEATURES:
• Aggressive Security Disable System
• Real-time Progress Monitoring  
• MEGA Defender Killer Integration
• Safe Mode Toggle System
• Professional Gaming Tool Interface
• Comprehensive System Optimization

🏢 COMPANY:
{self.company}

🌐 WEBSITE:
{self.website}

📧 SUPPORT:
{self.support_email}

⚖️ LICENSE:
{"Licensed Version" if self.is_licensed else f"Trial Version ({self.trial_days_left} days remaining)"}

© 2024 {self.company}
All Rights Reserved
        """
        
        messagebox.showinfo("About GameBoost Pro", about_text)
    
    def show_license_dialog(self):
        """Show license activation dialog"""
        license_window = ctk.CTkToplevel()
        license_window.title("License Activation")
        license_window.geometry("500x400")
        license_window.resizable(False, False)
        
        # Center the window
        license_window.transient()
        license_window.grab_set()
        
        # Header
        header_label = ctk.CTkLabel(
            license_window,
            text="🔑 License Activation",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        header_label.pack(pady=20)
        
        # License key input
        key_frame = ctk.CTkFrame(license_window)
        key_frame.pack(fill="x", padx=30, pady=10)
        
        key_label = ctk.CTkLabel(key_frame, text="Enter License Key:")
        key_label.pack(pady=(10, 5))
        
        key_entry = ctk.CTkEntry(
            key_frame,
            placeholder_text="XXXX-XXXX-XXXX-XXXX",
            width=300,
            font=ctk.CTkFont(size=14)
        )
        key_entry.pack(pady=(0, 10))
        
        # Buttons
        button_frame = ctk.CTkFrame(license_window)
        button_frame.pack(fill="x", padx=30, pady=20)
        
        activate_btn = ctk.CTkButton(
            button_frame,
            text="✅ Activate License",
            command=lambda: self.activate_license(key_entry.get(), license_window),
            height=35
        )
        activate_btn.pack(side="left", padx=(0, 10))
        
        trial_btn = ctk.CTkButton(
            button_frame,
            text="⏰ Continue Trial",
            command=license_window.destroy,
            height=35
        )
        trial_btn.pack(side="left")
        
        # Purchase info
        info_frame = ctk.CTkFrame(license_window)
        info_frame.pack(fill="both", expand=True, padx=30, pady=(0, 20))
        
        info_text = """
💰 PURCHASE LICENSE:
Visit our website to purchase a license key

🎯 FEATURES:
• Unlimited usage
• Priority support  
• Automatic updates
• Commercial use rights

🔗 Get your license at:
gameboost-pro.com/purchase
        """
        
        info_label = ctk.CTkLabel(
            info_frame,
            text=info_text,
            font=ctk.CTkFont(size=12),
            justify="left"
        )
        info_label.pack(padx=20, pady=20)
    
    def activate_license(self, key, window):
        """Activate license with provided key"""
        # Simple validation (in production, this would validate against server)
        if len(key) >= 16 and "-" in key:
            self.license_key = key
            self.is_licensed = True
            messagebox.showinfo("Success", "License activated successfully!")
            window.destroy()
        else:
            messagebox.showerror("Invalid Key", "Please enter a valid license key.")
    
    def check_trial_status(self):
        """Check if trial is still valid"""
        if not self.is_licensed and self.trial_days_left <= 0:
            messagebox.showwarning(
                "Trial Expired",
                "Your trial has expired. Please purchase a license to continue using GameBoost Pro."
            )
            return False
        return True
