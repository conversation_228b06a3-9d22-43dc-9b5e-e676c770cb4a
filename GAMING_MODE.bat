@echo off
title GAMING MODE - DISABLE ALL SECURITY
color 0C

echo.
echo  ==========================================
echo   🎮 GAMING MODE - DISABLE ALL SECURITY
echo  ==========================================
echo.
echo  This will disable ALL Windows security for gaming:
echo  ❌ Windows Defender (Real-time Protection)
echo  ❌ Windows Firewall (All profiles)
echo  ❌ SmartScreen Protection
echo  ❌ User Account Control (UAC)
echo  ❌ Fast Boot / Secure Boot
echo  ❌ Windows Update
echo.
echo  💾 Your current security settings will be saved
echo  🔄 You can restore them anytime with SAFE_MODE.bat
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  [!] WARNING: This will disable Windows security!
    echo  [!] Only use this for gaming sessions!
    echo.
    pause
    
    REM Change to script directory
    cd /d "%~dp0"
    
    echo  [🎮] Enabling Gaming Mode...
    echo.
    
    REM Run the toggle script
    python -c "from security_toggle import SecurityToggle; t = SecurityToggle(); t.enable_gaming_mode()"
    
    echo.
    echo  ==========================================
    echo   🎮 GAMING MODE ENABLED!
    echo  ==========================================
    echo.
    echo  [✓] All security features disabled
    echo  [💾] Previous settings saved to security_state.json
    echo  [🔄] Run SAFE_MODE.bat to restore security
    echo  [⚠️] Restart recommended for full effect
    echo.
    
    choice /c YN /m "Would you like to restart now? (Y/N)"
    if errorlevel 2 goto :no_restart
    if errorlevel 1 goto :restart
    
    :restart
    echo  [🔄] Restarting in 10 seconds...
    timeout /t 10
    shutdown /r /t 0
    goto :end
    
    :no_restart
    echo  [⚠️] Please restart manually for full effect
    goto :end
    
    :end
    pause
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator.
    echo  Right-click this file and select "Run as administrator"
    echo.
    pause
    
    REM Try to restart with admin privileges
    echo  Attempting to restart with administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
)

exit /b
