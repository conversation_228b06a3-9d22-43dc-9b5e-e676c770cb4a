# Support Tools Interface Requirements
# Basic Python dependencies for the gaming tool interface

# Core GUI and system interaction
tkinter  # Usually comes with Python, but listed for completeness
subprocess  # Built-in module
datetime  # Built-in module

# Additional useful packages for system management
psutil>=5.8.0  # System and process utilities
requests>=2.25.0  # HTTP requests (if needed for downloads)
winreg  # Windows registry access (built-in on Windows)

# Optional: Enhanced GUI components
# customtkinter>=5.0.0  # Modern tkinter themes (optional)
