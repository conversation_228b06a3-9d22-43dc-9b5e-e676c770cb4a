@echo off
title RUN ALL FIXES NOW - Support Tools Issues
color 0E

echo.
echo  ==========================================
echo   🔧 RUN ALL FIXES NOW
echo   Support Tools Issues Resolution
echo  ==========================================
echo.
echo  This will run all fixes to resolve the issues
echo  detected by Support Tools.exe including:
echo.
echo  • Secure Boot encoding errors
echo  • Virtualization encoding errors  
echo  • Firewall status errors
echo  • Real-time Protection errors
echo.
echo  ⚠️ IMPORTANT: BIOS changes may still be required
echo  📄 BIOS instructions will be provided
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  🛡️ STEP 1: Setting up safety measures...
    python SAFETY_MEASURES_VERIFICATION.py
    echo.
    
    echo  🔧 STEP 2: Running comprehensive fixes...
    python FIX_ALL_SUPPORT_TOOLS_ISSUES.py
    echo.
    
    echo  🔍 STEP 3: Verifying fixes...
    python VERIFY_SUPPORT_TOOLS_FIXES.py
    echo.
    
    echo  ==========================================
    echo   📋 NEXT STEPS REQUIRED
    echo  ==========================================
    echo.
    echo  ✅ Software fixes completed
    echo  📄 BIOS_INSTRUCTIONS.txt created
    echo  🛡️ Safety measures active
    echo.
    echo  🔧 MANUAL BIOS CONFIGURATION REQUIRED:
    echo  =====================================
    echo.
    echo  1. RESTART your computer
    echo  2. Enter BIOS (F2, F12, DEL, or ESC during boot)
    echo  3. DISABLE Secure Boot (Security section)
    echo  4. ENABLE Virtualization (Advanced/CPU section)
    echo  5. SAVE and exit BIOS
    echo  6. Run Support Tools.exe to verify
    echo.
    echo  📄 Detailed instructions in BIOS_INSTRUCTIONS.txt
    echo.
    
    choice /c YN /m "Would you like to restart now to access BIOS? (Y/N)"
    if errorlevel 2 goto :no_restart
    if errorlevel 1 goto :restart
    
    :restart
    echo.
    echo  🔄 Restarting in 15 seconds...
    echo  ⚠️ Enter BIOS during boot to make manual changes
    timeout /t 15
    shutdown /r /t 0
    goto :end
    
    :no_restart
    echo.
    echo  ⚠️ Please restart manually and enter BIOS
    echo  📄 Check BIOS_INSTRUCTIONS.txt for detailed steps
    goto :end
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator to fix
    echo  system-level issues detected by Support Tools.exe
    echo.
    echo  HOW TO RUN AS ADMINISTRATOR:
    echo  ===========================
    echo  1. Right-click this file (RUN_ALL_FIXES_NOW.bat)
    echo  2. Select "Run as administrator"
    echo  3. Click "Yes" when prompted by UAC
    echo.
    
    choice /c YN /m "Would you like to restart with administrator privileges? (Y/N)"
    if errorlevel 2 goto :end
    if errorlevel 1 goto :restart_admin
    
    :restart_admin
    echo.
    echo  🔄 Restarting with administrator privileges...
    
    REM Try to restart with admin privileges
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    goto :end
)

:end
echo.
echo  ==========================================
echo   🛡️ SAFETY REMINDER
echo  ==========================================
echo.
echo  Emergency restore available via:
echo  • Desktop shortcut: 🚨 EMERGENCY RESTORE GameBoost Pro
echo  • Manual script: EMERGENCY_RESTORE.bat
echo  • Windows System Restore
echo.
echo  All changes are 100%% reversible!
echo.
pause
exit /b
