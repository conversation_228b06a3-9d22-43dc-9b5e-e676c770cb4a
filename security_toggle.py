#!/usr/bin/env python3
"""
Security Toggle System
Toggle between Gaming Mode (security disabled) and Safe Mode (security enabled)
"""

import subprocess
import winreg
import ctypes
import json
import os
import requests
import zipfile
import tempfile
from pathlib import Path

class SecurityToggle:
    def __init__(self):
        self.results = []
        self.state_file = "security_state.json"
        
    def log(self, message):
        """Log message"""
        print(message)
        self.results.append(message)
        
    def is_admin(self):
        """Check admin privileges"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def run_command(self, command, description=""):
        """Run command silently"""
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )
            if result.returncode == 0:
                self.log(f"✅ {description}: SUCCESS")
                return True
            else:
                self.log(f"❌ {description}: FAILED")
                return False
        except Exception as e:
            self.log(f"❌ {description}: ERROR - {str(e)}")
            return False
    
    def get_current_security_state(self):
        """Check current security state"""
        self.log("🔍 CHECKING CURRENT SECURITY STATE...")
        
        state = {
            "defender_realtime": False,
            "firewall_enabled": False,
            "uac_enabled": False,
            "smartscreen_enabled": False,
            "fast_boot_enabled": False,
            "windows_update_enabled": False
        }
        
        # Check Windows Defender
        try:
            result = subprocess.run(
                'powershell.exe -Command "Get-MpPreference | Select-Object DisableRealtimeMonitoring"',
                shell=True, capture_output=True, text=True, timeout=30
            )
            if "False" in result.stdout:
                state["defender_realtime"] = True
        except:
            pass
        
        # Check Windows Firewall
        try:
            result = subprocess.run(
                'powershell.exe -Command "Get-NetFirewallProfile -Profile Domain | Select-Object Enabled"',
                shell=True, capture_output=True, text=True, timeout=30
            )
            if "True" in result.stdout:
                state["firewall_enabled"] = True
        except:
            pass
        
        # Check UAC
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                              r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System") as key:
                value = winreg.QueryValueEx(key, "EnableLUA")[0]
                if value == 1:
                    state["uac_enabled"] = True
        except:
            pass
        
        # Check SmartScreen
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                              r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer") as key:
                value = winreg.QueryValueEx(key, "SmartScreenEnabled")[0]
                if value != "Off":
                    state["smartscreen_enabled"] = True
        except:
            pass
        
        # Check Fast Boot
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                              r"SYSTEM\CurrentControlSet\Control\Session Manager\Power") as key:
                value = winreg.QueryValueEx(key, "HiberbootEnabled")[0]
                if value == 1:
                    state["fast_boot_enabled"] = True
        except:
            pass
        
        # Check Windows Update
        try:
            result = subprocess.run('sc query wuauserv', shell=True, capture_output=True, text=True)
            if "RUNNING" in result.stdout:
                state["windows_update_enabled"] = True
        except:
            pass
        
        return state
    
    def save_security_state(self, state):
        """Save current security state to file"""
        try:
            with open(self.state_file, 'w') as f:
                json.dump(state, f, indent=2)
            self.log(f"✅ Security state saved to {self.state_file}")
        except Exception as e:
            self.log(f"❌ Failed to save state: {str(e)}")
    
    def load_security_state(self):
        """Load previous security state from file"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r') as f:
                    state = json.load(f)
                self.log(f"✅ Security state loaded from {self.state_file}")
                return state
            else:
                self.log("⚠️ No previous state file found - using defaults")
                return None
        except Exception as e:
            self.log(f"❌ Failed to load state: {str(e)}")
            return None

    def download_and_run_defender_killer(self):
        """Download and run the MEGA Defender disable tool as fallback"""
        try:
            self.log("🔽 Downloading advanced Defender disable tool...")

            # MEGA direct download URL
            mega_url = "https://mega.nz/file/QHNRgYxB#61QJA0UE-03CDNxLg19y1vxYuNjagZNIEaaQnu9guN0"

            # Create temp directory
            temp_dir = tempfile.mkdtemp()
            tool_path = Path(temp_dir) / "defender_killer.exe"

            self.log("📥 Attempting to download Defender disable tool...")

            # Try to download using requests (simplified approach)
            # Note: MEGA links require special handling, this is a placeholder
            # In production, you'd use mega-cmd or similar

            # For now, check if tool already exists locally
            local_tool_paths = [
                "defender_killer.exe",
                "tools/defender_killer.exe",
                "DefenderKiller.exe",
                "WindowsDefenderKiller.exe"
            ]

            tool_found = False
            for local_path in local_tool_paths:
                if os.path.exists(local_path):
                    self.log(f"✅ Found local Defender killer: {local_path}")
                    tool_path = Path(local_path)
                    tool_found = True
                    break

            if not tool_found:
                self.log("⚠️ Advanced Defender disable tool not found locally")
                self.log("💡 Please download the tool manually from:")
                self.log("   https://mega.nz/file/QHNRgYxB#61QJA0UE-03CDNxLg19y1vxYuNjagZNIEaaQnu9guN0")
                self.log("   And place it in the same directory as this script")
                return False

            # Run the tool
            self.log(f"🚀 Running advanced Defender disable tool: {tool_path}")

            # Run with admin privileges
            result = subprocess.run(
                f'powershell.exe -Command "Start-Process \\"{tool_path}\\" -Verb RunAs -Wait"',
                shell=True,
                capture_output=True,
                text=True,
                timeout=120
            )

            if result.returncode == 0:
                self.log("✅ Advanced Defender disable tool completed successfully")
                return True
            else:
                self.log("❌ Advanced Defender disable tool failed")
                return False

        except Exception as e:
            self.log(f"❌ Error running advanced Defender disable tool: {str(e)}")
            return False
    
    def enable_gaming_mode(self):
        """Enable Gaming Mode (disable all security)"""
        self.log("🎮 ENABLING GAMING MODE...")
        self.log("=" * 50)
        
        if not self.is_admin():
            self.log("❌ Administrator privileges required!")
            return False
        
        # Save current state before disabling
        current_state = self.get_current_security_state()
        self.save_security_state(current_state)
        
        success_count = 0
        total_operations = 0
        
        # Disable Windows Defender
        self.log("\n🛡️ DISABLING WINDOWS DEFENDER...")
        defender_commands = [
            ('powershell.exe -ExecutionPolicy Bypass -Command "Set-MpPreference -DisableRealtimeMonitoring $true"', "Defender Real-time"),
            ('powershell.exe -ExecutionPolicy Bypass -Command "Set-MpPreference -MAPSReporting Disabled"', "Defender Cloud"),
            ('powershell.exe -ExecutionPolicy Bypass -Command "Set-MpPreference -DisableBehaviorMonitoring $true"', "Defender Behavior"),
            ('reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender" /v DisableAntiSpyware /t REG_DWORD /d 1 /f', "Defender Registry"),
        ]

        defender_success_count = 0
        for cmd, desc in defender_commands:
            if self.run_command(cmd, desc):
                success_count += 1
                defender_success_count += 1
            total_operations += 1

        # If standard Defender disable methods failed, try the MEGA tool
        if defender_success_count < 2:
            self.log("⚠️ Standard Defender disable methods insufficient")
            self.log("🔄 Attempting advanced Defender disable tool...")
            if self.download_and_run_defender_killer():
                self.log("✅ Advanced Defender disable: SUCCESS")
                success_count += 2  # Boost success count for advanced tool
            else:
                self.log("❌ Advanced Defender disable: FAILED")
        
        # Disable Windows Firewall
        self.log("\n🔥 DISABLING WINDOWS FIREWALL...")
        firewall_commands = [
            ('powershell.exe -ExecutionPolicy Bypass -Command "Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled False"', "All Firewall Profiles"),
            ('netsh advfirewall set allprofiles state off', "Advanced Firewall"),
            ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy\\DomainProfile" /v EnableFirewall /t REG_DWORD /d 0 /f', "Domain Firewall Registry"),
            ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy\\PublicProfile" /v EnableFirewall /t REG_DWORD /d 0 /f', "Public Firewall Registry"),
            ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy\\StandardProfile" /v EnableFirewall /t REG_DWORD /d 0 /f', "Private Firewall Registry"),
        ]

        for cmd, desc in firewall_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1

        # Try service disable (may fail but that's OK)
        self.log("🔧 Attempting firewall service disable (optional)...")
        service_commands = [
            ('sc stop MpsSvc', "Firewall Service Stop"),
            ('sc config MpsSvc start= disabled', "Firewall Service Disable"),
        ]

        for cmd, desc in service_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            # Don't count these as required operations since they often fail
            # total_operations += 1
        
        # Disable UAC
        self.log("\n🛡️ DISABLING UAC...")
        uac_commands = [
            ('reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v EnableLUA /t REG_DWORD /d 0 /f', "UAC Main"),
            ('reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v ConsentPromptBehaviorAdmin /t REG_DWORD /d 0 /f', "UAC Admin Prompt"),
        ]
        
        for cmd, desc in uac_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Disable SmartScreen
        self.log("\n🔒 DISABLING SMARTSCREEN...")
        smartscreen_commands = [
            ('reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer" /v SmartScreenEnabled /t REG_SZ /d "Off" /f', "SmartScreen Main"),
            ('reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\System" /v EnableSmartScreen /t REG_DWORD /d 0 /f', "SmartScreen Policy"),
        ]
        
        for cmd, desc in smartscreen_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Disable Fast Boot
        self.log("\n⚡ DISABLING FAST BOOT...")
        fastboot_commands = [
            ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Power" /v HiberbootEnabled /t REG_DWORD /d 0 /f', "Fast Boot Registry"),
            ('powercfg /hibernate off', "Hibernation"),
        ]
        
        for cmd, desc in fastboot_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Disable Windows Update
        self.log("\n🔄 DISABLING WINDOWS UPDATE...")
        update_commands = [
            ('sc stop wuauserv', "Windows Update Stop"),
            ('sc config wuauserv start= disabled', "Windows Update Disable"),
            ('sc stop UsoSvc', "Update Orchestrator Stop"),
            ('sc config UsoSvc start= disabled', "Update Orchestrator Disable"),
            ('reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate\\AU" /v NoAutoUpdate /t REG_DWORD /d 1 /f', "Auto Update Registry"),
            ('reg add "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\WindowsUpdate" /v DisableWindowsUpdateAccess /t REG_DWORD /d 1 /f', "Update Access Registry"),
        ]

        for cmd, desc in update_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        success_rate = success_count / total_operations if total_operations > 0 else 0
        
        self.log(f"\n📊 GAMING MODE RESULTS: {success_count}/{total_operations} ({success_rate*100:.1f}%)")
        
        if success_rate >= 0.8:
            self.log("🎮 GAMING MODE ENABLED SUCCESSFULLY!")
            self.log("⚠️ RESTART RECOMMENDED for full effect")
            return True
        else:
            self.log("⚠️ GAMING MODE PARTIALLY ENABLED")
            return False
    
    def enable_safe_mode(self):
        """Enable Safe Mode (restore all security)"""
        self.log("🛡️ ENABLING SAFE MODE...")
        self.log("=" * 50)
        
        if not self.is_admin():
            self.log("❌ Administrator privileges required!")
            return False
        
        # Load previous state if available
        previous_state = self.load_security_state()
        
        success_count = 0
        total_operations = 0
        
        # Enable Windows Defender
        self.log("\n🛡️ ENABLING WINDOWS DEFENDER...")
        defender_commands = [
            ('powershell.exe -ExecutionPolicy Bypass -Command "Set-MpPreference -DisableRealtimeMonitoring $false"', "Defender Real-time"),
            ('powershell.exe -ExecutionPolicy Bypass -Command "Set-MpPreference -MAPSReporting Advanced"', "Defender Cloud"),
            ('powershell.exe -ExecutionPolicy Bypass -Command "Set-MpPreference -DisableBehaviorMonitoring $false"', "Defender Behavior"),
            ('reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows Defender" /v DisableAntiSpyware /f', "Defender Registry"),
        ]
        
        for cmd, desc in defender_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Enable Windows Firewall
        self.log("\n🔥 ENABLING WINDOWS FIREWALL...")
        firewall_commands = [
            ('powershell.exe -ExecutionPolicy Bypass -Command "Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled True"', "All Firewall Profiles"),
            ('netsh advfirewall set allprofiles state on', "Advanced Firewall"),
            ('sc config MpsSvc start= auto', "Firewall Service Enable"),
            ('sc start MpsSvc', "Firewall Service Start"),
        ]
        
        for cmd, desc in firewall_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Enable UAC
        self.log("\n🛡️ ENABLING UAC...")
        uac_commands = [
            ('reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v EnableLUA /t REG_DWORD /d 1 /f', "UAC Main"),
            ('reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v ConsentPromptBehaviorAdmin /t REG_DWORD /d 5 /f', "UAC Admin Prompt"),
        ]
        
        for cmd, desc in uac_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Enable SmartScreen
        self.log("\n🔒 ENABLING SMARTSCREEN...")
        smartscreen_commands = [
            ('reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer" /v SmartScreenEnabled /t REG_SZ /d "RequireAdmin" /f', "SmartScreen Main"),
            ('reg delete "HKLM\\SOFTWARE\\Policies\\Microsoft\\Windows\\System" /v EnableSmartScreen /f', "SmartScreen Policy"),
        ]
        
        for cmd, desc in smartscreen_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Enable Fast Boot (if it was enabled before)
        if previous_state and previous_state.get("fast_boot_enabled", True):
            self.log("\n⚡ ENABLING FAST BOOT...")
            fastboot_commands = [
                ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Power" /v HiberbootEnabled /t REG_DWORD /d 1 /f', "Fast Boot Registry"),
                ('powercfg /hibernate on', "Hibernation"),
            ]
            
            for cmd, desc in fastboot_commands:
                if self.run_command(cmd, desc):
                    success_count += 1
                total_operations += 1
        
        # Enable Windows Update
        self.log("\n🔄 ENABLING WINDOWS UPDATE...")
        update_commands = [
            ('sc config wuauserv start= auto', "Windows Update Enable"),
            ('sc start wuauserv', "Windows Update Start"),
        ]
        
        for cmd, desc in update_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        success_rate = success_count / total_operations if total_operations > 0 else 0
        
        self.log(f"\n📊 SAFE MODE RESULTS: {success_count}/{total_operations} ({success_rate*100:.1f}%)")
        
        if success_rate >= 0.8:
            self.log("🛡️ SAFE MODE ENABLED SUCCESSFULLY!")
            self.log("⚠️ RESTART RECOMMENDED for full effect")
            return True
        else:
            self.log("⚠️ SAFE MODE PARTIALLY ENABLED")
            return False
    
    def get_results(self):
        """Get operation results"""
        return self.results

def main():
    print("🔄 SECURITY TOGGLE SYSTEM")
    print("=" * 40)
    print("Toggle between Gaming Mode and Safe Mode")
    print()
    
    toggle = SecurityToggle()
    
    if not toggle.is_admin():
        print("❌ Administrator privileges required!")
        input("Press Enter to exit...")
        return
    
    # Check current state
    current_state = toggle.get_current_security_state()
    
    # Determine current mode
    security_active = any(current_state.values())
    
    if security_active:
        print("📊 CURRENT MODE: 🛡️ SAFE MODE (Security Enabled)")
        print("\nAvailable action:")
        print("1. Switch to 🎮 GAMING MODE (Disable Security)")
        choice = input("\nEnter choice (1) or 'q' to quit: ").strip()
        
        if choice == "1":
            toggle.enable_gaming_mode()
        elif choice.lower() == "q":
            return
    else:
        print("📊 CURRENT MODE: 🎮 GAMING MODE (Security Disabled)")
        print("\nAvailable action:")
        print("1. Switch to 🛡️ SAFE MODE (Enable Security)")
        choice = input("\nEnter choice (1) or 'q' to quit: ").strip()
        
        if choice == "1":
            toggle.enable_safe_mode()
        elif choice.lower() == "q":
            return
    
    print("\n" + "=" * 50)
    print("Operation completed!")
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
