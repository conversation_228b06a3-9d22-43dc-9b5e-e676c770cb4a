@echo off
echo ========================================
echo FIXING XBOX COMPONENTS - ADMIN REQUIRED
echo ========================================
echo.

echo [1/5] Restoring Microsoft Store...
powershell -Command "Get-AppxPackage -AllUsers Microsoft.WindowsStore | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register \"$($_.InstallLocation)\AppXManifest.xml\"}"

echo.
echo [2/5] Restoring Xbox Game Bar...
powershell -Command "Get-AppxPackage -AllUsers Microsoft.XboxGamingOverlay | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register \"$($_.InstallLocation)\AppXManifest.xml\"}"

echo.
echo [3/5] Restoring Xbox Identity Provider...
powershell -Command "Get-AppxPackage -AllUsers Microsoft.XboxIdentityProvider | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register \"$($_.InstallLocation)\AppXManifest.xml\"}"

echo.
echo [4/5] Restoring Xbox Live components...
powershell -Command "Get-AppxPackage -AllUsers Microsoft.Xbox.TCUI | Foreach {Add-AppxPackage -DisableDevelopmentMode -Register \"$($_.InstallLocation)\AppXManifest.xml\"}"

echo.
echo [5/5] Restarting Xbox app...
taskkill /f /im "Xbox.exe" >nul 2>&1
taskkill /f /im "XboxApp.exe" >nul 2>&1
timeout /t 3 >nul
start "" "shell:AppsFolder\Microsoft.GamingApp_8wekyb3d8bbwe!Microsoft.Xbox.App"

echo.
echo ========================================
echo REPAIR COMPLETE!
echo ========================================
echo.
echo The Xbox app should now work properly.
echo Try the update again!
echo.
pause
