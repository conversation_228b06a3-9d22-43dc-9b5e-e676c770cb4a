#!/usr/bin/env python3
"""
Simple Security Mode Checker
"""

from security_toggle import SecurityToggle

def main():
    print("🔍 SECURITY MODE CHECKER")
    print("=" * 30)
    
    toggle = SecurityToggle()
    state = toggle.get_current_security_state()
    
    # Determine current mode
    security_active = any(state.values())
    
    if security_active:
        print("📊 CURRENT MODE: 🛡️ SAFE MODE (Security Enabled)")
    else:
        print("📊 CURRENT MODE: 🎮 GAMING MODE (Security Disabled)")
    
    print("\n📋 DETAILED STATUS:")
    print("-" * 25)
    
    status_map = {
        "defender_realtime": "Windows Defender Real-time",
        "firewall_enabled": "Windows Firewall",
        "uac_enabled": "User Account Control",
        "smartscreen_enabled": "SmartScreen Protection",
        "fast_boot_enabled": "Fast Boot",
        "windows_update_enabled": "Windows Update"
    }
    
    for key, description in status_map.items():
        status = "✅ ENABLED" if state.get(key, False) else "❌ DISABLED"
        print(f"  {description}: {status}")
    
    print("\n🔄 TOGGLE OPTIONS:")
    print("• Run GAMING_MODE.bat to disable security")
    print("• Run SAFE_MODE.bat to enable security")
    print("• Use the main interface toggle buttons")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
