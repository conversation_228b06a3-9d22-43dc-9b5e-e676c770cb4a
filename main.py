#!/usr/bin/env python3
"""
Gaming Tool Setup Automation System
Main application entry point
"""

import customtkinter as ctk
from tkinter import messagebox
import threading
import sys
import os
import ctypes
import time

# Import our modules
from modules.system_cleanup import SystemCleanup
from modules.security_config import SecurityConfig
from modules.system_optimizer import SystemOptimizer
from modules.dependency_installer import DependencyInstaller
from modules.loader_interface import LoaderInterface
from modules.game_integration import GameIntegration
from modules.troubleshooter import Troubleshooter
from modules.safety_manager import SafetyManager

class GamingToolSetup:
    def __init__(self):
        # Set appearance mode and color theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # Create main window
        self.root = ctk.CTk()
        self.root.title("Gaming Tool Setup Automation System")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # Initialize modules
        self.safety_manager = SafetyManager()
        self.system_cleanup = SystemCleanup()
        self.security_config = SecurityConfig()
        self.system_optimizer = SystemOptimizer()
        self.dependency_installer = DependencyInstaller()
        self.loader_interface = LoaderInterface()
        self.game_integration = GameIntegration()
        self.troubleshooter = Troubleshooter()
        
        # Setup UI
        self.setup_ui()
        
        # Status tracking
        self.current_step = 0
        self.total_steps = 8
        
    def setup_ui(self):
        """Setup the main user interface"""
        # Main container
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        self.title_label = ctk.CTkLabel(
            self.main_frame, 
            text="Gaming Tool Setup Automation System",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.title_label.pack(pady=(20, 30))
        
        # Progress bar
        self.progress_frame = ctk.CTkFrame(self.main_frame)
        self.progress_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.progress_label = ctk.CTkLabel(self.progress_frame, text="Ready to start setup")
        self.progress_label.pack(pady=10)
        
        self.progress_bar = ctk.CTkProgressBar(self.progress_frame)
        self.progress_bar.pack(fill="x", padx=20, pady=(0, 10))
        self.progress_bar.set(0)
        
        # Setup steps frame
        self.steps_frame = ctk.CTkScrollableFrame(self.main_frame, height=400)
        self.steps_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        self.create_step_buttons()
        
        # Control buttons
        self.control_frame = ctk.CTkFrame(self.main_frame)
        self.control_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.start_button = ctk.CTkButton(
            self.control_frame,
            text="Start Full Setup",
            command=self.start_full_setup,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=40
        )
        self.start_button.pack(side="left", padx=(20, 10), pady=10)
        
        self.stop_button = ctk.CTkButton(
            self.control_frame,
            text="Stop",
            command=self.stop_setup,
            state="disabled",
            height=40
        )
        self.stop_button.pack(side="left", padx=10, pady=10)
        
        self.safety_button = ctk.CTkButton(
            self.control_frame,
            text="Safety Check",
            command=self.run_safety_check,
            height=40
        )
        self.safety_button.pack(side="right", padx=(10, 5), pady=10)

        self.troubleshoot_button = ctk.CTkButton(
            self.control_frame,
            text="Troubleshoot",
            command=self.open_troubleshooter,
            height=40
        )
        self.troubleshoot_button.pack(side="right", padx=(5, 20), pady=10)
        
        # Status text area
        self.status_frame = ctk.CTkFrame(self.main_frame)
        self.status_frame.pack(fill="x", padx=20)
        
        self.status_text = ctk.CTkTextbox(self.status_frame, height=100)
        self.status_text.pack(fill="x", padx=10, pady=10)
        
    def create_step_buttons(self):
        """Create individual step buttons"""
        steps = [
            ("1. System Cleanup", "Remove anti-cheat and cracking software", self.run_system_cleanup),
            ("2. BIOS Configuration", "Guide for BIOS settings", self.open_bios_guide),
            ("3. Security Configuration", "Disable Windows security features", self.run_security_config),
            ("4. System Optimization", "Optimize display and game settings", self.run_system_optimizer),
            ("5. Install Dependencies", "Download and install required tools", self.run_dependency_installer),
            ("6. Loader Interface", "Setup and configure loader", self.open_loader_interface),
            ("7. Game Integration", "Inject and setup game integration", self.run_game_integration),
            ("8. Troubleshooting", "Fix common issues", self.open_troubleshooter)
        ]
        
        self.step_buttons = []
        for title, description, command in steps:
            step_frame = ctk.CTkFrame(self.steps_frame)
            step_frame.pack(fill="x", padx=10, pady=5)
            
            button = ctk.CTkButton(
                step_frame,
                text=title,
                command=command,
                width=200,
                height=40
            )
            button.pack(side="left", padx=10, pady=10)
            
            desc_label = ctk.CTkLabel(step_frame, text=description)
            desc_label.pack(side="left", padx=(10, 0), pady=10)
            
            status_label = ctk.CTkLabel(step_frame, text="⏳ Pending", text_color="orange")
            status_label.pack(side="right", padx=10, pady=10)
            
            self.step_buttons.append((button, status_label))
    
    def log_message(self, message):
        """Add message to status log"""
        self.status_text.insert("end", f"{message}\n")
        self.status_text.see("end")
        self.root.update()
    
    def update_progress(self, step, message):
        """Update progress bar and current step"""
        self.current_step = step
        progress = step / self.total_steps
        self.progress_bar.set(progress)
        self.progress_label.configure(text=message)
        
        # Update step status
        if step > 0 and step <= len(self.step_buttons):
            self.step_buttons[step-1][1].configure(text="✅ Complete", text_color="green")
        
        self.root.update()
    
    def start_full_setup(self):
        """Start the full automated setup process"""
        # Safety confirmation dialog
        safety_msg = """⚠️ IMPORTANT SAFETY NOTICE ⚠️

This will make significant changes to your system including:
• Disabling Windows security features
• Modifying system settings
• Installing/removing software

SAFETY MEASURES ACTIVE:
✅ System Restore Point will be created
✅ Emergency restore script on desktop
✅ Safe mode recovery instructions
✅ Registry backups created
✅ Service states backed up

Do you want to proceed?"""

        result = messagebox.askyesno("Safety Confirmation", safety_msg)
        if not result:
            self.log_message("❌ Setup cancelled by user")
            return

        self.start_button.configure(state="disabled")
        self.stop_button.configure(state="normal")

        # Run setup in separate thread to prevent UI freezing
        setup_thread = threading.Thread(target=self.run_full_setup)
        setup_thread.daemon = True
        setup_thread.start()
    
    def run_full_setup(self):
        """Execute all setup steps"""
        try:
            self.log_message("Starting full setup process...")

            # Step 0: Setup Safety Measures
            self.update_progress(0, "Setting up safety measures...")
            self.log_message("🛡️ Setting up safety measures...")
            safety_success = self.safety_manager.setup_all_safety_measures()

            for result in self.safety_manager.get_safety_results():
                self.log_message(result)

            if not safety_success:
                self.log_message("⚠️ Some safety measures failed - proceeding with caution")

            # Step 1: System Cleanup
            self.update_progress(1, "Running system cleanup...")
            self.system_cleanup.run_cleanup()
            self.log_message("✅ System cleanup completed")
            
            # Step 2: BIOS Guide (manual step)
            self.update_progress(2, "BIOS configuration guide opened")
            self.log_message("⚠️ Please complete BIOS configuration manually")
            
            # Step 3: Security Configuration
            self.update_progress(3, "Configuring Windows security...")
            self.security_config.disable_all_security()
            self.log_message("✅ Security configuration completed")
            
            # Step 4: System Optimization
            self.update_progress(4, "Optimizing system settings...")
            self.system_optimizer.optimize_all()
            self.log_message("✅ System optimization completed")
            
            # Step 5: Install Dependencies
            self.update_progress(5, "Installing dependencies...")
            self.dependency_installer.install_all()
            self.log_message("✅ Dependencies installed")
            
            # Step 6: Loader Interface
            self.update_progress(6, "Setting up loader interface...")
            self.log_message("✅ Loader interface ready")
            
            # Step 7: Game Integration
            self.update_progress(7, "Preparing game integration...")
            self.log_message("✅ Game integration ready")
            
            # Step 8: Complete
            self.update_progress(8, "Setup completed successfully!")
            self.log_message("🎉 Full setup process completed successfully!")
            
        except Exception as e:
            self.log_message(f"❌ Error during setup: {str(e)}")
        finally:
            self.start_button.configure(state="normal")
            self.stop_button.configure(state="disabled")
    
    # Individual step methods
    def run_system_cleanup(self):
        self.log_message("Running system cleanup...")
        threading.Thread(target=self.system_cleanup.run_cleanup, daemon=True).start()
    
    def open_bios_guide(self):
        self.log_message("Opening BIOS configuration guide...")
        # This will open a separate window with BIOS instructions
        pass
    
    def run_security_config(self):
        self.log_message("Configuring security settings...")
        threading.Thread(target=self.security_config.disable_all_security, daemon=True).start()
    
    def run_system_optimizer(self):
        self.log_message("Optimizing system settings...")
        threading.Thread(target=self.system_optimizer.optimize_all, daemon=True).start()
    
    def run_dependency_installer(self):
        self.log_message("Installing dependencies...")
        threading.Thread(target=self.dependency_installer.install_all, daemon=True).start()
    
    def open_loader_interface(self):
        self.log_message("Opening loader interface...")
        self.loader_interface.show_interface()
    
    def run_game_integration(self):
        self.log_message("Starting game integration...")
        threading.Thread(target=self.game_integration.start_integration, daemon=True).start()
    
    def open_troubleshooter(self):
        self.log_message("Opening troubleshooter...")
        self.troubleshooter.show_interface()
    
    def stop_setup(self):
        """Stop the current setup process"""
        self.log_message("⏹️ Setup process stopped by user")
        self.start_button.configure(state="normal")
        self.stop_button.configure(state="disabled")

    def run_safety_check(self):
        """Run comprehensive safety check"""
        self.log_message("🛡️ Running safety check...")

        # Run safety check in separate thread
        safety_thread = threading.Thread(target=self.perform_safety_check)
        safety_thread.daemon = True
        safety_thread.start()

    def perform_safety_check(self):
        """Perform comprehensive safety check"""
        try:
            # Quick system responsiveness check
            self.log_message("Checking system responsiveness...")
            responsive = self.safety_manager.quick_system_check()

            if responsive:
                self.log_message("✅ System is responsive and stable")
            else:
                self.log_message("⚠️ System may be experiencing issues")

            # Setup safety measures
            self.log_message("Setting up safety measures...")
            safety_success = self.safety_manager.setup_all_safety_measures()

            # Display results
            for result in self.safety_manager.get_safety_results():
                self.log_message(result)

            if safety_success:
                self.log_message("🛡️ All safety measures are active - system protected")
                messagebox.showinfo("Safety Check Complete",
                    "✅ Safety measures are active!\n\n" +
                    "• System Restore Point created\n" +
                    "• Emergency restore script on desktop\n" +
                    "• Registry and service backups created\n" +
                    "• Safe mode instructions available\n\n" +
                    "Your system is protected!")
            else:
                self.log_message("⚠️ Some safety measures failed")
                messagebox.showwarning("Safety Check Warning",
                    "⚠️ Some safety measures could not be activated.\n\n" +
                    "Proceed with extra caution or run as Administrator.")

        except Exception as e:
            self.log_message(f"❌ Safety check error: {str(e)}")
            messagebox.showerror("Safety Check Error",
                f"Error during safety check: {str(e)}")

    def monitor_system_health(self):
        """Monitor system health during operations"""
        while hasattr(self, 'monitoring_active') and self.monitoring_active:
            try:
                # Quick responsiveness check
                responsive = self.safety_manager.quick_system_check()

                if not responsive:
                    self.log_message("⚠️ System responsiveness warning!")
                    # Could add automatic pause/recovery here

                time.sleep(10)  # Check every 10 seconds

            except Exception as e:
                self.log_message(f"Health monitor error: {str(e)}")
                break
    
    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    # Check if running as administrator
    if not os.name == 'nt' or not ctypes.windll.shell32.IsUserAnAdmin():
        messagebox.showerror("Error", "This application must be run as Administrator!")
        sys.exit(1)
    
    app = GamingToolSetup()
    app.run()
