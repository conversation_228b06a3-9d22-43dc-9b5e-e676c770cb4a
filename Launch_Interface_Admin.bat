@echo off
title Gaming Tool Interface - Administrator Mode
color 0B

echo.
echo  ========================================
echo   GAMING TOOL INTERFACE - ADMIN MODE
echo  ========================================
echo.
echo  Launching professional gaming tool interface...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    echo  [✓] Launching professional interface...
    echo.
    
    REM Launch the professional interface
    python gaming_tool_interface.py
    
    if %errorLevel% neq 0 (
        echo.
        echo  [!] Error launching professional interface
        echo  [i] Trying fallback interface...
        python main.py
    )
    
    echo.
    echo  Interface closed.
    pause
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This interface needs admin rights to modify system settings
    echo  The script will now restart with administrator privileges...
    echo.
    pause
    
    REM Restart with admin privileges
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
)

exit /b
