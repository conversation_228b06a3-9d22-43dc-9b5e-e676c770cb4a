@echo off
title NUCLEAR DEFENDER DISABLE - TAMPER PROTECTION BYPASS
color 0C

echo.
echo  ================================================
echo   NUCLEAR DEFENDER DISABLE - TAMPER PROTECTION
echo  ================================================
echo.
echo  WARNING: This is the MOST AGGRESSIVE approach!
echo  This will attempt to bypass Tamper Protection
echo  and completely disable Windows Defender.
echo.
echo  What this does:
echo  - Disable Tamper Protection (registry + policy)
echo  - Stop all Defender services
echo  - Kill all Defender processes  
echo  - Nuclear registry disable
echo  - Multiple PowerShell disable attempts
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  [!] FINAL WARNING: This will NUKE Windows Defender!
    echo  [!] Only proceed if other methods failed!
    echo.
    pause
    
    REM Change to script directory
    cd /d "%~dp0"
    
    echo  [✓] Running nuclear Defender disable...
    echo.
    
    REM Run PowerShell script with maximum privileges
    powershell.exe -ExecutionPolicy Bypass -NoProfile -File "DISABLE_DEFENDER_TAMPER.ps1"
    
    echo.
    echo  Nuclear Defender disable completed!
    echo.
    pause
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator.
    echo  Right-click this file and select "Run as administrator"
    echo.
    pause
    
    REM Try to restart with admin privileges
    echo  Attempting to restart with administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
)

exit /b
