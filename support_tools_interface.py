#!/usr/bin/env python3
"""
Simple Support Tools Interface
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys

class SimpleSupportTools:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Support Tools Interface")
        self.root.geometry("600x400")
        self.root.configure(bg='#2a2a2a')
        
        self.create_interface()
        
    def create_interface(self):
        # Title
        title = tk.Label(
            self.root, 
            text="Support Tools Interface", 
            font=("Arial", 16, "bold"),
            fg='white', 
            bg='#2a2a2a'
        )
        title.pack(pady=20)
        
        # Gaming Mode button
        gaming_btn = tk.Button(
            self.root,
            text="Gaming Mode\n(Disable Real-Time Protection + Enable Firewall)",
            font=("Arial", 12, "bold"),
            bg='#00aa00',
            fg='white',
            width=50,
            height=3,
            command=self.gaming_mode
        )
        gaming_btn.pack(pady=10)
        
        # Normal Mode button
        normal_btn = tk.But<PERSON>(
            self.root,
            text="Normal Mode\n(Enable All Security)",
            font=("Arial", 12, "bold"),
            bg='#ff6600',
            fg='white',
            width=50,
            height=3,
            command=self.normal_mode
        )
        normal_btn.pack(pady=10)
        
        # Status label
        self.status_label = tk.Label(
            self.root,
            text="Ready",
            font=("Arial", 10),
            fg='white',
            bg='#2a2a2a'
        )
        self.status_label.pack(pady=20)
        
    def run_command(self, command, description):
        try:
            self.status_label.config(text=f"Running: {description}")
            self.root.update()
            
            result = subprocess.run(
                ["powershell", "-Command", command],
                capture_output=True,
                text=True,
                check=True
            )
            
            self.status_label.config(text=f"Success: {description}")
            return True
            
        except Exception as e:
            self.status_label.config(text=f"Failed: {description}")
            return False
            
    def gaming_mode(self):
        self.status_label.config(text="Configuring Gaming Mode...")
        self.root.update()
        
        success_count = 0
        
        # Disable Real-Time Protection
        if self.run_command("Set-MpPreference -DisableRealtimeMonitoring $true", "Disabling Real-Time Protection"):
            success_count += 1
            
        # Enable Firewall
        if self.run_command("netsh advfirewall set allprofiles state on", "Enabling Firewall"):
            success_count += 1
            
        if success_count == 2:
            messagebox.showinfo("Success", "Gaming Mode enabled!\n\nReal-Time Protection: DISABLED\nFirewall: ENABLED\n\nYou still need to configure BIOS settings manually.")
            self.status_label.config(text="Gaming Mode enabled successfully")
        else:
            messagebox.showerror("Partial Success", f"Gaming Mode partially enabled ({success_count}/2 operations successful)")
            
    def normal_mode(self):
        self.status_label.config(text="Configuring Normal Mode...")
        self.root.update()
        
        success_count = 0
        
        # Enable Real-Time Protection
        if self.run_command("Set-MpPreference -DisableRealtimeMonitoring $false", "Enabling Real-Time Protection"):
            success_count += 1
            
        # Enable Firewall
        if self.run_command("netsh advfirewall set allprofiles state on", "Enabling Firewall"):
            success_count += 1
            
        if success_count == 2:
            messagebox.showinfo("Success", "Normal Mode enabled!\n\nReal-Time Protection: ENABLED\nFirewall: ENABLED\n\nAll security restored.")
            self.status_label.config(text="Normal Mode enabled successfully")
        else:
            messagebox.showerror("Partial Success", f"Normal Mode partially enabled ({success_count}/2 operations successful)")
            
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    # Check if running as administrator
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            messagebox.showerror("Administrator Required", "This application must be run as Administrator!")
            sys.exit(1)
    except:
        pass
        
    app = SimpleSupportTools()
    app.run()
