#!/usr/bin/env python3
"""
Simple Support Tools Interface
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys

class SimpleSupportTools:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Support Tools Interface")
        self.root.geometry("600x400")
        self.root.configure(bg='#2a2a2a')
        
        self.create_interface()
        
    def create_interface(self):
        # Title
        title = tk.Label(
            self.root, 
            text="Support Tools Interface", 
            font=("Arial", 16, "bold"),
            fg='white', 
            bg='#2a2a2a'
        )
        title.pack(pady=20)
        
        # Gaming Mode button
        gaming_btn = tk.Button(
            self.root,
            text="Gaming Mode\n(Support Tools.exe Requirements)\nDisable Real-Time Protection + Disable Firewall + Fix Apps Check",
            font=("Arial", 11, "bold"),
            bg='#00aa00',
            fg='white',
            width=60,
            height=4,
            command=self.gaming_mode
        )
        gaming_btn.pack(pady=10)

        # Normal Mode button
        normal_btn = tk.Button(
            self.root,
            text="Normal Mode\n(Restore All Security)\nEnable Real-Time Protection + Enable Firewall",
            font=("Arial", 11, "bold"),
            bg='#ff6600',
            fg='white',
            width=60,
            height=4,
            command=self.normal_mode
        )
        normal_btn.pack(pady=10)

        # BIOS Instructions button
        bios_btn = tk.Button(
            self.root,
            text="BIOS Instructions\n(Manual Steps Required)",
            font=("Arial", 10, "bold"),
            bg='#666666',
            fg='white',
            width=40,
            height=2,
            command=self.show_bios_instructions
        )
        bios_btn.pack(pady=5)
        
        # Status label
        self.status_label = tk.Label(
            self.root,
            text="Ready",
            font=("Arial", 10),
            fg='white',
            bg='#2a2a2a'
        )
        self.status_label.pack(pady=20)
        
    def run_command(self, command, description):
        try:
            self.status_label.config(text=f"Running: {description}")
            self.root.update()
            
            result = subprocess.run(
                ["powershell", "-Command", command],
                capture_output=True,
                text=True,
                check=True
            )
            
            self.status_label.config(text=f"Success: {description}")
            return True
            
        except Exception as e:
            self.status_label.config(text=f"Failed: {description}")
            return False
            
    def gaming_mode(self):
        self.status_label.config(text="Configuring Gaming Mode for Support Tools.exe...")
        self.root.update()

        success_count = 0
        total_operations = 3

        # 1. Disable Real-Time Protection (REQUIRED)
        if self.run_command("Set-MpPreference -DisableRealtimeMonitoring $true", "Disabling Real-Time Protection"):
            success_count += 1

        # 2. Disable Firewall (REQUIRED - Support Tools.exe wants it OFF)
        if self.run_command("netsh advfirewall set allprofiles state off", "Disabling Windows Firewall"):
            success_count += 1

        # 3. Fix Apps and Files Check (REQUIRED)
        if self.run_command("Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer' -Name 'SmartScreenEnabled' -Value 'Off'", "Fixing Apps and Files Check"):
            success_count += 1

        if success_count == total_operations:
            messagebox.showinfo("Gaming Mode Enabled!",
                "✅ Support Tools.exe Requirements Met!\n\n" +
                "✅ Real-Time Protection: DISABLED\n" +
                "✅ Firewall: DISABLED\n" +
                "✅ Apps and Files Check: FIXED\n\n" +
                "⚠️ STILL NEEDED (Manual BIOS Steps):\n" +
                "❌ Secure Boot: Must be DISABLED\n" +
                "❌ Virtualization: Must be ENABLED\n\n" +
                "Click 'BIOS Instructions' for manual steps.")
            self.status_label.config(text="Gaming Mode enabled - Support Tools.exe ready!")
        else:
            messagebox.showerror("Partial Success", f"Gaming Mode partially enabled ({success_count}/{total_operations} operations successful)\n\nSome operations failed. Try running as Administrator.")
            
    def normal_mode(self):
        self.status_label.config(text="Restoring Normal Security Mode...")
        self.root.update()

        success_count = 0
        total_operations = 3

        # 1. Enable Real-Time Protection (SAFE DEFAULT)
        if self.run_command("Set-MpPreference -DisableRealtimeMonitoring $false", "Enabling Real-Time Protection"):
            success_count += 1

        # 2. Enable Firewall (SAFE DEFAULT)
        if self.run_command("netsh advfirewall set allprofiles state on", "Enabling Windows Firewall"):
            success_count += 1

        # 3. Restore SmartScreen (SAFE DEFAULT)
        if self.run_command("Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer' -Name 'SmartScreenEnabled' -Value 'RequireAdmin'", "Restoring SmartScreen"):
            success_count += 1

        if success_count == total_operations:
            messagebox.showinfo("Normal Mode Enabled!",
                "🛡️ All Security Restored!\n\n" +
                "✅ Real-Time Protection: ENABLED\n" +
                "✅ Firewall: ENABLED\n" +
                "✅ SmartScreen: ENABLED\n\n" +
                "Your system is now fully protected.")
            self.status_label.config(text="Normal Mode enabled - Full security restored!")
        else:
            messagebox.showerror("Partial Success", f"Normal Mode partially enabled ({success_count}/{total_operations} operations successful)\n\nSome operations failed. Try running as Administrator.")

    def show_bios_instructions(self):
        instructions = """🔧 BIOS Settings Required for Support Tools.exe:

⚠️ These settings CANNOT be changed by software - you must enter BIOS manually.

1️⃣ DISABLE SECURE BOOT:
   • Restart computer
   • Press F2/F12/DEL during boot (varies by motherboard)
   • Find "Secure Boot" setting (usually under Security)
   • Set to DISABLED

2️⃣ ENABLE VIRTUALIZATION:
   • In BIOS, find "Intel VT-x" or "AMD-V" setting
   • Usually under Advanced → CPU Configuration
   • Set to ENABLED

3️⃣ SAVE AND EXIT:
   • Press F10 to save and exit
   • Computer will restart

🎯 After BIOS changes + Gaming Mode, Support Tools.exe should show all green checkmarks!"""

        messagebox.showinfo("BIOS Instructions", instructions)
            
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    # Check if running as administrator
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            messagebox.showerror("Administrator Required", "This application must be run as Administrator!")
            sys.exit(1)
    except:
        pass
        
    app = SimpleSupportTools()
    app.run()
