#!/usr/bin/env python3
"""
Simple Support Tools Interface
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
from safety_manager import SafetyManager

class SimpleSupportTools:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Support Tools Interface")
        self.root.geometry("900x700")
        self.root.configure(bg='#2a2a2a')

        # Status tracking - Focus on system issues
        self.status_items = {
            "Real-Time Protection": {"status": "NEEDS DISABLE", "color": "red"},
            "Windows Defender": {"status": "NEEDS DISABLE", "color": "red"},
            "Virtualization (BIOS)": {"status": "NEEDS ENABLE", "color": "red"},
            "Windows Firewall": {"status": "OK", "color": "green"},
            "Apps and Files Check": {"status": "OK", "color": "green"},
            "Secure Boot (BIOS)": {"status": "OK", "color": "green"}
        }

        self.create_interface()
        self.refresh_status()
        self.simple_safety_message()
        
    def create_interface(self):
        # Title
        title = tk.Label(
            self.root,
            text="Support Tools Interface",
            font=("Arial", 18, "bold"),
            fg='white',
            bg='#2a2a2a'
        )
        title.pack(pady=10)

        # Create main frame with two columns
        main_frame = tk.Frame(self.root, bg='#2a2a2a')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Left column - Controls
        left_frame = tk.Frame(main_frame, bg='#2a2a2a')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # Right column - Status
        right_frame = tk.Frame(main_frame, bg='#2a2a2a')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))

        self.create_controls(left_frame)
        self.create_status_panel(right_frame)

    def create_controls(self, parent):
        # Controls title
        controls_title = tk.Label(
            parent,
            text="Controls",
            font=("Arial", 14, "bold"),
            fg='white',
            bg='#2a2a2a'
        )
        controls_title.pack(pady=(0, 10))

        # Fix Real-Time Protection button
        rtp_btn = tk.Button(
            parent,
            text="🎯 Disable Real-Time Protection\n(Fix KernelX Init Failed)",
            font=("Arial", 11, "bold"),
            bg='#ff6600',
            fg='white',
            width=30,
            height=3,
            command=self.disable_realtime_protection
        )
        rtp_btn.pack(pady=5)

        # Disable Windows Defender button
        defender_btn = tk.Button(
            parent,
            text="🛡️ Disable Windows Defender\n(Complete Defender Shutdown)",
            font=("Arial", 11, "bold"),
            bg='#cc3300',
            fg='white',
            width=30,
            height=3,
            command=self.disable_windows_defender
        )
        defender_btn.pack(pady=5)

        # Fix Error 1072 button
        error1072_btn = tk.Button(
            parent,
            text="🔧 Fix Error 1072\n(Service Cleanup & Restart)",
            font=("Arial", 11, "bold"),
            bg='#9900cc',
            fg='white',
            width=30,
            height=3,
            command=self.fix_error_1072
        )
        error1072_btn.pack(pady=5)

        # Normal Mode button
        normal_btn = tk.Button(
            parent,
            text="🛡️ Normal Mode\n(Restore Security)",
            font=("Arial", 12, "bold"),
            bg='#ff6600',
            fg='white',
            width=30,
            height=3,
            command=self.normal_mode
        )
        normal_btn.pack(pady=10)

        # BIOS Instructions button (FINAL STEP)
        bios_btn = tk.Button(
            parent,
            text="🎯 FINAL STEP: BIOS Virtualization\n(Enable in BIOS - Manual Required)",
            font=("Arial", 11, "bold"),
            bg='#cc0066',
            fg='white',
            width=30,
            height=3,
            command=self.show_bios_instructions
        )
        bios_btn.pack(pady=10)

        # Refresh button
        refresh_btn = tk.Button(
            parent,
            text="🔄 Refresh Status",
            font=("Arial", 10),
            bg='#0066cc',
            fg='white',
            width=20,
            command=self.refresh_status
        )
        refresh_btn.pack(pady=10)

        # Safety info
        safety_frame = tk.Frame(parent, bg='#1a4a1a', relief='raised', bd=2)
        safety_frame.pack(fill='x', pady=10, padx=5)

        safety_title = tk.Label(
            safety_frame,
            text="🛡️ SAFETY MEASURES",
            font=("Arial", 10, "bold"),
            fg='#00ff88',
            bg='#1a4a1a'
        )
        safety_title.pack(pady=5)

        safety_text = tk.Label(
            safety_frame,
            text="✅ System Restore Points\n✅ Registry Backups\n✅ Service State Backups\n✅ Emergency Restore Script",
            font=("Arial", 8),
            fg='white',
            bg='#1a4a1a',
            justify='left'
        )
        safety_text.pack(pady=5)

        # Status label
        self.status_label = tk.Label(
            parent,
            text="Ready",
            font=("Arial", 10),
            fg='white',
            bg='#2a2a2a'
        )
        self.status_label.pack(pady=20)

    def create_status_panel(self, parent):
        # Status title
        status_title = tk.Label(
            parent,
            text="Support Tools.exe Requirements",
            font=("Arial", 14, "bold"),
            fg='white',
            bg='#2a2a2a'
        )
        status_title.pack(pady=(0, 10))

        # Status frame with border
        status_frame = tk.Frame(parent, bg='#1a1a1a', relief='sunken', bd=2)
        status_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # Create status labels
        self.status_labels = {}
        for item in self.status_items:
            item_frame = tk.Frame(status_frame, bg='#1a1a1a')
            item_frame.pack(fill='x', padx=10, pady=5)

            # Status icon
            icon_label = tk.Label(
                item_frame,
                text="⏳",
                font=("Arial", 12),
                fg='yellow',
                bg='#1a1a1a',
                width=3
            )
            icon_label.pack(side='left')

            # Item name
            name_label = tk.Label(
                item_frame,
                text=item,
                font=("Arial", 11),
                fg='white',
                bg='#1a1a1a',
                anchor='w'
            )
            name_label.pack(side='left', fill='x', expand=True, padx=(5, 0))

            # Status text
            status_label = tk.Label(
                item_frame,
                text=self.status_items[item]["status"],
                font=("Arial", 10),
                fg=self.status_items[item]["color"],
                bg='#1a1a1a'
            )
            status_label.pack(side='right')

            self.status_labels[item] = {
                'icon': icon_label,
                'status': status_label
            }

        # Log area
        log_title = tk.Label(
            parent,
            text="Operation Log",
            font=("Arial", 12, "bold"),
            fg='white',
            bg='#2a2a2a'
        )
        log_title.pack(pady=(20, 5))

        # Create scrolled text for log
        from tkinter import scrolledtext
        self.log_text = scrolledtext.ScrolledText(
            parent,
            height=8,
            bg='#1a1a1a',
            fg='white',
            font=("Consolas", 9),
            wrap='word'
        )
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)

    def simple_safety_message(self):
        """Show simple safety info without heavy operations"""
        self.log_message("🛡️ Safety: Emergency restore script available in SAFETY_BACKUPS folder", True)

    def log_message(self, message, success=None):
        """Add message to log with timestamp"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")

        if success is True:
            icon = "✅"
            color = "green"
        elif success is False:
            icon = "❌"
            color = "red"
        else:
            icon = "ℹ️"
            color = "white"

        log_entry = f"[{timestamp}] {icon} {message}\n"

        self.log_text.insert('end', log_entry)
        self.log_text.see('end')
        self.root.update()

    def update_status_item(self, item_name, status, success=None):
        """Update status item with icon and color"""
        if item_name in self.status_labels:
            if success is True:
                icon = "✅"
                color = "green"
            elif success is False:
                icon = "❌"
                color = "red"
            else:
                icon = "⚠️"
                color = "orange"

            self.status_labels[item_name]['icon'].config(text=icon, fg=color)
            self.status_labels[item_name]['status'].config(text=status, fg=color)
            self.status_items[item_name] = {"status": status, "color": color}

    def run_command(self, command, description):
        try:
            self.log_message(f"Running: {description}")
            self.status_label.config(text=f"Running: {description}")
            self.root.update()

            result = subprocess.run(
                ["powershell", "-Command", command],
                capture_output=True,
                text=True,
                check=True
            )

            self.log_message(f"Success: {description}", True)
            self.status_label.config(text=f"Success: {description}")
            return True

        except Exception as e:
            self.log_message(f"Failed: {description}", False)
            self.status_label.config(text=f"Failed: {description}")
            return False

    def refresh_status(self):
        """Check current system status and update display"""
        self.log_message("Refreshing system status...")

        # Check Real-Time Protection
        try:
            result = subprocess.run(
                ["powershell", "-Command", "Get-MpPreference | Select-Object DisableRealtimeMonitoring"],
                capture_output=True, text=True
            )
            if "False" in result.stdout:
                self.update_status_item("Real-Time Protection", "ENABLED", False)
            else:
                self.update_status_item("Real-Time Protection", "DISABLED", True)
        except:
            self.update_status_item("Real-Time Protection", "ERROR", False)

        # Check Firewall
        try:
            result = subprocess.run(
                ["powershell", "-Command", "netsh advfirewall show allprofiles state"],
                capture_output=True, text=True
            )
            if "ON" in result.stdout:
                self.update_status_item("Windows Firewall", "ENABLED", False)
            else:
                self.update_status_item("Windows Firewall", "DISABLED", True)
        except:
            self.update_status_item("Windows Firewall", "ERROR", False)

        # Check Apps and Files
        try:
            result = subprocess.run(
                ["powershell", "-Command", "Get-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer' -Name 'SmartScreenEnabled' | Select-Object SmartScreenEnabled"],
                capture_output=True, text=True
            )
            if "Off" in result.stdout:
                self.update_status_item("Apps and Files Check", "FIXED", True)
            else:
                self.update_status_item("Apps and Files Check", "NEEDS FIX", False)
        except:
            self.update_status_item("Apps and Files Check", "ERROR", False)

        self.log_message("Status refresh completed", True)

    def disable_realtime_protection(self):
        """Fix Real-Time Protection in background thread to prevent freezing"""
        import threading

        def fix_thread():
            try:
                self.log_message("🎯 Fixing Real-Time Protection...")
                self.status_label.config(text="Disabling Real-Time Protection...")

                result = subprocess.run(
                    ["powershell", "-Command", "Set-MpPreference -DisableRealtimeMonitoring $true"],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                if result.returncode == 0:
                    self.log_message("✅ SUCCESS: Real-Time Protection DISABLED!", True)
                    self.update_status_item("Real-Time Protection", "DISABLED ✅", True)
                    self.log_message("🎯 1 of 2 issues FIXED!", True)
                    self.log_message("⚠️ Only BIOS Virtualization remains", None)
                    self.status_label.config(text="Almost done - Only BIOS Virtualization remains!")

                    # Show success popup
                    self.root.after(0, lambda: messagebox.showinfo(
                        "Real-Time Protection Fixed!",
                        "✅ Real-Time Protection DISABLED!\n\n" +
                        "🎯 1 of 2 issues FIXED!\n\n" +
                        "✅ Real-Time Protection: DISABLED ✅\n" +
                        "✅ Firewall: Already OK ✅\n" +
                        "✅ Apps and Files Check: Already OK ✅\n" +
                        "✅ Secure Boot: Already OK ✅\n\n" +
                        "❌ ONLY 1 ISSUE REMAINING:\n" +
                        "❌ Virtualization in BIOS: Must be ENABLED\n\n" +
                        "Click 'FINAL STEP: BIOS Virtualization' for instructions!"
                    ))
                else:
                    self.log_message("❌ FAILED: Could not disable Real-Time Protection", False)
                    self.update_status_item("Real-Time Protection", "FAILED ❌", False)
                    self.status_label.config(text="Failed to disable Real-Time Protection")

                    self.root.after(0, lambda: messagebox.showerror(
                        "Fix Failed",
                        "Failed to disable Real-Time Protection.\n\n" +
                        "Try running as Administrator or disable manually in Windows Security."
                    ))

            except Exception as e:
                self.log_message(f"❌ ERROR: {str(e)}", False)
                self.status_label.config(text="Error occurred")
                self.root.after(0, lambda: messagebox.showerror("Error", f"Error: {str(e)}"))

        # Run in background thread to prevent freezing
        self.log_message("=== STARTING REAL-TIME PROTECTION FIX ===")
        thread = threading.Thread(target=fix_thread)
        thread.daemon = True
        thread.start()

    def disable_windows_defender(self):
        """Completely disable Windows Defender to fix kernel driver issues"""
        import threading

        def disable_thread():
            try:
                self.log_message("🛡️ Starting Windows Defender complete disable...")
                self.status_label.config(text="Checking current Defender status...")

                # STEP 1: Check current status first
                self.log_message("=== STEP 1: CHECKING CURRENT STATUS ===")

                diagnostic_commands = [
                    ("Real-Time Protection Status", "Get-MpPreference | Select-Object DisableRealtimeMonitoring"),
                    ("All Defender Preferences", "Get-MpPreference | Select-Object Disable*"),
                    ("Defender Services Status", "Get-Service WinDefend, WdNisSvc, SecurityHealthService | Select-Object Name, Status, StartType"),
                    ("Defender Computer Status", "Get-MpComputerStatus | Select-Object AntivirusEnabled, RealTimeProtectionEnabled"),
                    ("Registry DisableAntiSpyware", "Get-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows Defender' -Name DisableAntiSpyware -ErrorAction SilentlyContinue"),
                    ("Test Signing Status", "bcdedit /enum | findstr -i testsigning"),
                    ("Secure Boot Status", "Confirm-SecureBootUEFI -ErrorAction SilentlyContinue")
                ]

                for desc, cmd in diagnostic_commands:
                    self.log_message(f"🔍 Checking: {desc}")
                    try:
                        result = subprocess.run(
                            ["powershell", "-Command", cmd],
                            capture_output=True,
                            text=True,
                            timeout=15
                        )
                        if result.stdout.strip():
                            self.log_message(f"📋 {desc}: {result.stdout.strip()}")
                        else:
                            self.log_message(f"📋 {desc}: No output or disabled")
                    except Exception as e:
                        self.log_message(f"⚠️ {desc}: Error - {str(e)}")

                self.log_message("=== STEP 2: DISABLING WINDOWS DEFENDER ===")
                self.status_label.config(text="Disabling Windows Defender...")

                disable_commands = [
                    # Disable Real-Time Protection
                    "Set-MpPreference -DisableRealtimeMonitoring $true",
                    # Disable all protection features
                    "Set-MpPreference -DisableBehaviorMonitoring $true",
                    "Set-MpPreference -DisableBlockAtFirstSeen $true",
                    "Set-MpPreference -DisableIOAVProtection $true",
                    "Set-MpPreference -DisablePrivacyMode $true",
                    "Set-MpPreference -DisableScriptScanning $true",
                    "Set-MpPreference -DisableArchiveScanning $true",
                    "Set-MpPreference -DisableIntrusionPreventionSystem $true",
                    "Set-MpPreference -DisableEmailScanning $true",
                    # Disable Windows Defender services
                    "Stop-Service WinDefend -Force -ErrorAction SilentlyContinue",
                    "Set-Service WinDefend -StartupType Disabled -ErrorAction SilentlyContinue",
                    "Stop-Service WdNisSvc -Force -ErrorAction SilentlyContinue",
                    "Set-Service WdNisSvc -StartupType Disabled -ErrorAction SilentlyContinue",
                    "Stop-Service SecurityHealthService -Force -ErrorAction SilentlyContinue",
                    "Set-Service SecurityHealthService -StartupType Disabled -ErrorAction SilentlyContinue"
                ]

                success_count = 0
                total_commands = len(disable_commands)

                for i, command in enumerate(disable_commands, 1):
                    self.log_message(f"Step {i}/{total_commands}: Running command...")

                    result = subprocess.run(
                        ["powershell", "-Command", command],
                        capture_output=True,
                        text=True,
                        timeout=30
                    )

                    if result.returncode == 0:
                        success_count += 1
                        self.log_message(f"✅ Step {i}/{total_commands} completed", True)
                    else:
                        self.log_message(f"⚠️ Step {i}/{total_commands} failed (continuing)", None)

                # STEP 3: Verify disable was successful
                self.log_message("=== STEP 3: VERIFYING DISABLE SUCCESS ===")

                verification_commands = [
                    ("Final Real-Time Protection Status", "Get-MpPreference | Select-Object DisableRealtimeMonitoring"),
                    ("Final Defender Services Status", "Get-Service WinDefend, WdNisSvc, SecurityHealthService | Select-Object Name, Status, StartType"),
                    ("Final Computer Status", "Get-MpComputerStatus | Select-Object AntivirusEnabled, RealTimeProtectionEnabled")
                ]

                for desc, cmd in verification_commands:
                    self.log_message(f"🔍 Verifying: {desc}")
                    try:
                        result = subprocess.run(
                            ["powershell", "-Command", cmd],
                            capture_output=True,
                            text=True,
                            timeout=15
                        )
                        if result.stdout.strip():
                            self.log_message(f"📋 {desc}: {result.stdout.strip()}")
                        else:
                            self.log_message(f"📋 {desc}: No output")
                    except Exception as e:
                        self.log_message(f"⚠️ {desc}: Error - {str(e)}")

                if success_count >= total_commands * 0.7:  # 70% success rate
                    self.log_message("✅ Windows Defender disabled successfully!", True)
                    self.update_status_item("Windows Defender", "DISABLED ✅", True)
                    self.update_status_item("Real-Time Protection", "DISABLED ✅", True)
                    self.status_label.config(text="Windows Defender disabled - Try KernelX again!")

                    self.root.after(0, lambda: messagebox.showinfo(
                        "Windows Defender Disabled!",
                        "✅ Windows Defender completely disabled!\n\n" +
                        "🎯 This should fix KernelX Init Failed errors\n\n" +
                        "✅ Real-Time Protection: DISABLED\n" +
                        "✅ Behavior Monitoring: DISABLED\n" +
                        "✅ All Defender Services: STOPPED\n\n" +
                        "Check the log for detailed verification results.\n" +
                        "Try running your KernelX application again!\n\n" +
                        "⚠️ Remember to re-enable when done."
                    ))
                else:
                    self.log_message(f"⚠️ Partial success: {success_count}/{total_commands} commands completed", None)
                    self.update_status_item("Windows Defender", "PARTIAL ⚠️", None)
                    self.status_label.config(text="Windows Defender partially disabled")

                    self.root.after(0, lambda: messagebox.showwarning(
                        "Partial Success",
                        f"Windows Defender partially disabled ({success_count}/{total_commands} operations).\n\n" +
                        "Some operations failed but core protection is likely disabled.\n" +
                        "Try your kernel driver again."
                    ))

            except Exception as e:
                self.log_message(f"❌ ERROR: {str(e)}", False)
                self.status_label.config(text="Error disabling Windows Defender")
                self.root.after(0, lambda: messagebox.showerror("Error", f"Error: {str(e)}"))

        # Run in background thread
        self.log_message("=== STARTING WINDOWS DEFENDER DISABLE ===")
        thread = threading.Thread(target=disable_thread)
        thread.daemon = True
        thread.start()

    def fix_error_1072(self):
        """Fix Error 1072 - Service marked for deletion"""
        import threading

        def fix_thread():
            try:
                self.log_message("🔧 Fixing Error 1072 - Service marked for deletion...")
                self.status_label.config(text="Fixing Error 1072...")

                # STEP 1: Check current service states
                self.log_message("=== STEP 1: CHECKING SERVICE STATES ===")

                service_check_commands = [
                    ("All Services Status", "Get-Service | Where-Object {$_.Status -eq 'Stopped' -and $_.StartType -eq 'Disabled'} | Select-Object Name, Status, StartType"),
                    ("Pending Deletion Services", "sc query state= all | findstr 'DELETE_PENDING'"),
                    ("Windows Defender Services", "Get-Service WinDefend, WdNisSvc, SecurityHealthService | Select-Object Name, Status, StartType"),
                    ("System Event Log Errors", "Get-WinEvent -FilterHashtable @{LogName='System'; Level=2} -MaxEvents 10 | Where-Object {$_.Message -like '*1072*' -or $_.Message -like '*service*'} | Select-Object TimeCreated, Id, LevelDisplayName, Message")
                ]

                for desc, cmd in service_check_commands:
                    self.log_message(f"🔍 Checking: {desc}")
                    try:
                        result = subprocess.run(
                            ["powershell", "-Command", cmd],
                            capture_output=True,
                            text=True,
                            timeout=20
                        )
                        if result.stdout.strip():
                            self.log_message(f"📋 {desc}:")
                            # Split long output into multiple lines
                            lines = result.stdout.strip().split('\n')
                            for line in lines[:10]:  # Limit to first 10 lines
                                if line.strip():
                                    self.log_message(f"   {line.strip()}")
                        else:
                            self.log_message(f"📋 {desc}: No issues found")
                    except Exception as e:
                        self.log_message(f"⚠️ {desc}: Error - {str(e)}")

                # STEP 2: Service cleanup commands
                self.log_message("=== STEP 2: SERVICE CLEANUP ===")

                cleanup_commands = [
                    "Stop-Service WinDefend -Force -ErrorAction SilentlyContinue",
                    "Stop-Service WdNisSvc -Force -ErrorAction SilentlyContinue",
                    "Stop-Service SecurityHealthService -Force -ErrorAction SilentlyContinue",
                    "Set-Service WinDefend -StartupType Manual -ErrorAction SilentlyContinue",
                    "Set-Service WdNisSvc -StartupType Manual -ErrorAction SilentlyContinue",
                    "Set-Service SecurityHealthService -StartupType Manual -ErrorAction SilentlyContinue",
                    "sc delete WinDefend",
                    "sc delete WdNisSvc",
                    "sc delete SecurityHealthService"
                ]

                success_count = 0
                for i, command in enumerate(cleanup_commands, 1):
                    self.log_message(f"🔧 Cleanup {i}/{len(cleanup_commands)}: {command}")
                    try:
                        if command.startswith("sc "):
                            # Use cmd for sc commands
                            result = subprocess.run(
                                ["cmd", "/c", command],
                                capture_output=True,
                                text=True,
                                timeout=15
                            )
                        else:
                            # Use PowerShell for other commands
                            result = subprocess.run(
                                ["powershell", "-Command", command],
                                capture_output=True,
                                text=True,
                                timeout=15
                            )

                        if result.returncode == 0:
                            success_count += 1
                            self.log_message(f"✅ Cleanup {i} completed")
                        else:
                            self.log_message(f"⚠️ Cleanup {i} failed: {result.stderr.strip()}")

                    except Exception as e:
                        self.log_message(f"❌ Cleanup {i} error: {str(e)}")

                # STEP 3: Registry cleanup
                self.log_message("=== STEP 3: REGISTRY CLEANUP ===")

                registry_commands = [
                    "Remove-Item -Path 'HKLM:\\SYSTEM\\CurrentControlSet\\Services\\WinDefend' -Recurse -Force -ErrorAction SilentlyContinue",
                    "Remove-Item -Path 'HKLM:\\SYSTEM\\CurrentControlSet\\Services\\WdNisSvc' -Recurse -Force -ErrorAction SilentlyContinue",
                    "Remove-Item -Path 'HKLM:\\SYSTEM\\CurrentControlSet\\Services\\SecurityHealthService' -Recurse -Force -ErrorAction SilentlyContinue"
                ]

                for i, command in enumerate(registry_commands, 1):
                    self.log_message(f"🗂️ Registry cleanup {i}/{len(registry_commands)}")
                    try:
                        result = subprocess.run(
                            ["powershell", "-Command", command],
                            capture_output=True,
                            text=True,
                            timeout=15
                        )
                        if result.returncode == 0:
                            self.log_message(f"✅ Registry cleanup {i} completed")
                        else:
                            self.log_message(f"⚠️ Registry cleanup {i} failed (may not exist)")
                    except Exception as e:
                        self.log_message(f"❌ Registry cleanup {i} error: {str(e)}")

                self.log_message("=== STEP 4: FINAL VERIFICATION ===")
                self.log_message("🔄 Error 1072 fix completed!")
                self.log_message("⚠️ SYSTEM RESTART RECOMMENDED to clear service deletion states")
                self.status_label.config(text="Error 1072 fix completed - Restart recommended")

                self.root.after(0, lambda: messagebox.showinfo(
                    "Error 1072 Fix Completed!",
                    "🔧 Service cleanup completed!\n\n" +
                    "✅ Stopped conflicting services\n" +
                    "✅ Cleaned service registry entries\n" +
                    "✅ Removed deletion-pending services\n\n" +
                    "⚠️ RESTART REQUIRED:\n" +
                    "Please restart your computer to complete the fix.\n" +
                    "After restart, try your KernelX application again.\n\n" +
                    "Error 1072 should be resolved!"
                ))

            except Exception as e:
                self.log_message(f"❌ ERROR: {str(e)}", False)
                self.status_label.config(text="Error 1072 fix failed")
                self.root.after(0, lambda: messagebox.showerror("Error", f"Error: {str(e)}"))

        # Run in background thread
        self.log_message("=== STARTING ERROR 1072 FIX ===")
        thread = threading.Thread(target=fix_thread)
        thread.daemon = True
        thread.start()

    def normal_mode(self):
        self.status_label.config(text="Restoring Normal Security Mode...")
        self.root.update()

        success_count = 0
        total_operations = 3

        # 1. Enable Real-Time Protection (SAFE DEFAULT)
        if self.run_command("Set-MpPreference -DisableRealtimeMonitoring $false", "Enabling Real-Time Protection"):
            success_count += 1

        # 2. Enable Firewall (SAFE DEFAULT)
        if self.run_command("netsh advfirewall set allprofiles state on", "Enabling Windows Firewall"):
            success_count += 1

        # 3. Restore SmartScreen (SAFE DEFAULT)
        if self.run_command("Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer' -Name 'SmartScreenEnabled' -Value 'RequireAdmin'", "Restoring SmartScreen"):
            success_count += 1

        if success_count == total_operations:
            messagebox.showinfo("Normal Mode Enabled!",
                "🛡️ All Security Restored!\n\n" +
                "✅ Real-Time Protection: ENABLED\n" +
                "✅ Firewall: ENABLED\n" +
                "✅ SmartScreen: ENABLED\n\n" +
                "Your system is now fully protected.")
            self.status_label.config(text="Normal Mode enabled - Full security restored!")
        else:
            messagebox.showerror("Partial Success", f"Normal Mode partially enabled ({success_count}/{total_operations} operations successful)\n\nSome operations failed. Try running as Administrator.")

    def show_bios_instructions(self):
        instructions = """🎯 FINAL STEP: Enable Virtualization in BIOS

⚠️ This is the ONLY remaining issue for Support Tools.exe!

🔧 STEP-BY-STEP INSTRUCTIONS:

1️⃣ RESTART YOUR COMPUTER
   • Save any work and restart

2️⃣ ENTER BIOS SETUP
   • Press F2, F12, or DEL during boot
   • (Key varies by motherboard - try all three)

3️⃣ FIND VIRTUALIZATION SETTING
   • Look for "Intel VT-x" or "AMD-V"
   • Usually under: Advanced → CPU Configuration
   • Or: Advanced → Processor Options
   • Or: Security → Virtualization

4️⃣ ENABLE VIRTUALIZATION
   • Change setting from DISABLED to ENABLED
   • Make sure it says ENABLED

5️⃣ SAVE AND EXIT
   • Press F10 to save changes
   • Confirm "Yes" to save
   • Computer will restart

🎯 AFTER BIOS FIX:
✅ Run Support Tools.exe again
✅ Should show ALL GREEN CHECKMARKS!
✅ Gaming tool will be 100% ready!

Your motherboard: ASUS PRIME B660M-A AC D4
BIOS key is likely: F2 or DEL"""

        messagebox.showinfo("FINAL STEP - BIOS Virtualization", instructions)
            
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    # Check if running as administrator
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            messagebox.showerror("Administrator Required", "This application must be run as Administrator!")
            sys.exit(1)
    except:
        pass
        
    app = SimpleSupportTools()
    app.run()
