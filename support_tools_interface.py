#!/usr/bin/env python3
"""
Simple Support Tools Interface
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
from safety_manager import SafetyManager

class SimpleSupportTools:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Support Tools Interface")
        self.root.geometry("900x700")
        self.root.configure(bg='#2a2a2a')

        # Initialize safety manager
        self.safety_manager = SafetyManager()

        # Status tracking
        self.status_items = {
            "Real-Time Protection": {"status": "Checking...", "color": "yellow"},
            "Windows Firewall": {"status": "Checking...", "color": "yellow"},
            "Apps and Files Check": {"status": "Checking...", "color": "yellow"},
            "Secure Boot (BIOS)": {"status": "Manual Required", "color": "orange"},
            "Virtualization (BIOS)": {"status": "Manual Required", "color": "orange"}
        }

        self.create_interface()
        self.refresh_status()

        # Perform initial safety check
        self.initial_safety_check()
        
    def create_interface(self):
        # Title
        title = tk.Label(
            self.root,
            text="Support Tools Interface",
            font=("Arial", 18, "bold"),
            fg='white',
            bg='#2a2a2a'
        )
        title.pack(pady=10)

        # Create main frame with two columns
        main_frame = tk.Frame(self.root, bg='#2a2a2a')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Left column - Controls
        left_frame = tk.Frame(main_frame, bg='#2a2a2a')
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # Right column - Status
        right_frame = tk.Frame(main_frame, bg='#2a2a2a')
        right_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))

        self.create_controls(left_frame)
        self.create_status_panel(right_frame)

    def create_controls(self, parent):
        # Controls title
        controls_title = tk.Label(
            parent,
            text="Controls",
            font=("Arial", 14, "bold"),
            fg='white',
            bg='#2a2a2a'
        )
        controls_title.pack(pady=(0, 10))

        # Gaming Mode button
        gaming_btn = tk.Button(
            parent,
            text="🎮 Gaming Mode\n(Support Tools.exe Ready)",
            font=("Arial", 12, "bold"),
            bg='#00aa00',
            fg='white',
            width=30,
            height=3,
            command=self.gaming_mode
        )
        gaming_btn.pack(pady=10)

        # Normal Mode button
        normal_btn = tk.Button(
            parent,
            text="🛡️ Normal Mode\n(Restore Security)",
            font=("Arial", 12, "bold"),
            bg='#ff6600',
            fg='white',
            width=30,
            height=3,
            command=self.normal_mode
        )
        normal_btn.pack(pady=10)

        # BIOS Instructions button
        bios_btn = tk.Button(
            parent,
            text="🔧 BIOS Instructions",
            font=("Arial", 11, "bold"),
            bg='#666666',
            fg='white',
            width=25,
            height=2,
            command=self.show_bios_instructions
        )
        bios_btn.pack(pady=10)

        # Refresh button
        refresh_btn = tk.Button(
            parent,
            text="🔄 Refresh Status",
            font=("Arial", 10),
            bg='#0066cc',
            fg='white',
            width=20,
            command=self.refresh_status
        )
        refresh_btn.pack(pady=10)

        # Safety info
        safety_frame = tk.Frame(parent, bg='#1a4a1a', relief='raised', bd=2)
        safety_frame.pack(fill='x', pady=10, padx=5)

        safety_title = tk.Label(
            safety_frame,
            text="🛡️ SAFETY MEASURES",
            font=("Arial", 10, "bold"),
            fg='#00ff88',
            bg='#1a4a1a'
        )
        safety_title.pack(pady=5)

        safety_text = tk.Label(
            safety_frame,
            text="✅ System Restore Points\n✅ Registry Backups\n✅ Service State Backups\n✅ Emergency Restore Script",
            font=("Arial", 8),
            fg='white',
            bg='#1a4a1a',
            justify='left'
        )
        safety_text.pack(pady=5)

        # Status label
        self.status_label = tk.Label(
            parent,
            text="Ready",
            font=("Arial", 10),
            fg='white',
            bg='#2a2a2a'
        )
        self.status_label.pack(pady=20)

    def create_status_panel(self, parent):
        # Status title
        status_title = tk.Label(
            parent,
            text="Support Tools.exe Requirements",
            font=("Arial", 14, "bold"),
            fg='white',
            bg='#2a2a2a'
        )
        status_title.pack(pady=(0, 10))

        # Status frame with border
        status_frame = tk.Frame(parent, bg='#1a1a1a', relief='sunken', bd=2)
        status_frame.pack(fill='both', expand=True, padx=5, pady=5)

        # Create status labels
        self.status_labels = {}
        for item in self.status_items:
            item_frame = tk.Frame(status_frame, bg='#1a1a1a')
            item_frame.pack(fill='x', padx=10, pady=5)

            # Status icon
            icon_label = tk.Label(
                item_frame,
                text="⏳",
                font=("Arial", 12),
                fg='yellow',
                bg='#1a1a1a',
                width=3
            )
            icon_label.pack(side='left')

            # Item name
            name_label = tk.Label(
                item_frame,
                text=item,
                font=("Arial", 11),
                fg='white',
                bg='#1a1a1a',
                anchor='w'
            )
            name_label.pack(side='left', fill='x', expand=True, padx=(5, 0))

            # Status text
            status_label = tk.Label(
                item_frame,
                text=self.status_items[item]["status"],
                font=("Arial", 10),
                fg=self.status_items[item]["color"],
                bg='#1a1a1a'
            )
            status_label.pack(side='right')

            self.status_labels[item] = {
                'icon': icon_label,
                'status': status_label
            }

        # Log area
        log_title = tk.Label(
            parent,
            text="Operation Log",
            font=("Arial", 12, "bold"),
            fg='white',
            bg='#2a2a2a'
        )
        log_title.pack(pady=(20, 5))

        # Create scrolled text for log
        from tkinter import scrolledtext
        self.log_text = scrolledtext.ScrolledText(
            parent,
            height=8,
            bg='#1a1a1a',
            fg='white',
            font=("Consolas", 9),
            wrap='word'
        )
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)

    def initial_safety_check(self):
        """Perform initial safety verification"""
        self.log_message("🛡️ Performing initial safety check...")

        # Check system integrity
        integrity = self.safety_manager.verify_system_integrity()

        if not integrity.get("windows_activation", True):
            self.log_message("⚠️ Warning: Windows activation status unclear", None)

        if not integrity.get("system_files", True):
            self.log_message("⚠️ Warning: System file integrity issues detected", None)

        if not integrity.get("disk_space", True):
            self.log_message("⚠️ Warning: Low disk space - need at least 1GB free", None)

        # Create emergency restore script
        script_file = self.safety_manager.create_emergency_restore_script()
        self.log_message(f"✅ Emergency restore script created: {script_file}", True)

        self.log_message("🛡️ Safety check completed", True)

    def perform_safety_backup(self):
        """Perform comprehensive safety backup before changes"""
        self.log_message("🛡️ Creating safety backup before changes...")

        try:
            backup_results = self.safety_manager.full_safety_backup()

            # Check backup results
            restore_point = backup_results["operations"].get("restore_point", {})
            if restore_point.get("success"):
                self.log_message("✅ System restore point created", True)
            else:
                self.log_message("⚠️ System restore point failed - continuing with caution", None)

            registry_backup = backup_results["operations"].get("registry_backup", {})
            success_count = sum(1 for key, data in registry_backup.items() if data.get("status") == "success")
            self.log_message(f"✅ Registry backup: {success_count} keys backed up", True)

            service_backup = backup_results["operations"].get("service_backup", {})
            if service_backup.get("success"):
                self.log_message("✅ Service states backed up", True)
            else:
                self.log_message("⚠️ Service backup partial", None)

            self.log_message("🛡️ Safety backup completed successfully", True)
            return True

        except Exception as e:
            self.log_message(f"❌ Safety backup failed: {str(e)}", False)

            # Ask user if they want to continue without backup
            response = messagebox.askyesno(
                "Safety Backup Failed",
                "Safety backup failed to complete.\n\n" +
                "This increases the risk of system issues.\n\n" +
                "Do you want to continue anyway?\n\n" +
                "Click 'No' to cancel for safety."
            )

            if response:
                self.log_message("⚠️ User chose to continue without full backup", None)
                return True
            else:
                self.log_message("🛡️ Operation cancelled for safety", True)
                return False

    def log_message(self, message, success=None):
        """Add message to log with timestamp"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")

        if success is True:
            icon = "✅"
            color = "green"
        elif success is False:
            icon = "❌"
            color = "red"
        else:
            icon = "ℹ️"
            color = "white"

        log_entry = f"[{timestamp}] {icon} {message}\n"

        self.log_text.insert('end', log_entry)
        self.log_text.see('end')
        self.root.update()

    def update_status_item(self, item_name, status, success=None):
        """Update status item with icon and color"""
        if item_name in self.status_labels:
            if success is True:
                icon = "✅"
                color = "green"
            elif success is False:
                icon = "❌"
                color = "red"
            else:
                icon = "⚠️"
                color = "orange"

            self.status_labels[item_name]['icon'].config(text=icon, fg=color)
            self.status_labels[item_name]['status'].config(text=status, fg=color)
            self.status_items[item_name] = {"status": status, "color": color}

    def run_command(self, command, description):
        try:
            self.log_message(f"Running: {description}")
            self.status_label.config(text=f"Running: {description}")
            self.root.update()

            result = subprocess.run(
                ["powershell", "-Command", command],
                capture_output=True,
                text=True,
                check=True
            )

            self.log_message(f"Success: {description}", True)
            self.status_label.config(text=f"Success: {description}")
            return True

        except Exception as e:
            self.log_message(f"Failed: {description}", False)
            self.status_label.config(text=f"Failed: {description}")
            return False

    def refresh_status(self):
        """Check current system status and update display"""
        self.log_message("Refreshing system status...")

        # Check Real-Time Protection
        try:
            result = subprocess.run(
                ["powershell", "-Command", "Get-MpPreference | Select-Object DisableRealtimeMonitoring"],
                capture_output=True, text=True
            )
            if "False" in result.stdout:
                self.update_status_item("Real-Time Protection", "ENABLED", False)
            else:
                self.update_status_item("Real-Time Protection", "DISABLED", True)
        except:
            self.update_status_item("Real-Time Protection", "ERROR", False)

        # Check Firewall
        try:
            result = subprocess.run(
                ["powershell", "-Command", "netsh advfirewall show allprofiles state"],
                capture_output=True, text=True
            )
            if "ON" in result.stdout:
                self.update_status_item("Windows Firewall", "ENABLED", False)
            else:
                self.update_status_item("Windows Firewall", "DISABLED", True)
        except:
            self.update_status_item("Windows Firewall", "ERROR", False)

        # Check Apps and Files
        try:
            result = subprocess.run(
                ["powershell", "-Command", "Get-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer' -Name 'SmartScreenEnabled' | Select-Object SmartScreenEnabled"],
                capture_output=True, text=True
            )
            if "Off" in result.stdout:
                self.update_status_item("Apps and Files Check", "FIXED", True)
            else:
                self.update_status_item("Apps and Files Check", "NEEDS FIX", False)
        except:
            self.update_status_item("Apps and Files Check", "ERROR", False)

        self.log_message("Status refresh completed", True)

    def gaming_mode(self):
        self.log_message("=== STARTING GAMING MODE ===")
        self.status_label.config(text="Performing safety checks...")

        # SAFETY FIRST: Create backup before any changes
        if not self.perform_safety_backup():
            self.log_message("❌ Gaming Mode cancelled for safety", False)
            return

        self.log_message("🛡️ Safety backup completed - proceeding with Gaming Mode")
        self.status_label.config(text="Configuring Gaming Mode for Support Tools.exe...")

        success_count = 0
        total_operations = 3

        # 1. Disable Real-Time Protection (REQUIRED)
        self.log_message("Step 1/3: Disabling Real-Time Protection...")
        if self.run_command("Set-MpPreference -DisableRealtimeMonitoring $true", "Disabling Real-Time Protection"):
            success_count += 1
            self.update_status_item("Real-Time Protection", "DISABLED", True)
        else:
            self.update_status_item("Real-Time Protection", "FAILED", False)

        # 2. Disable Firewall (REQUIRED - Support Tools.exe wants it OFF)
        self.log_message("Step 2/3: Disabling Windows Firewall...")
        if self.run_command("netsh advfirewall set allprofiles state off", "Disabling Windows Firewall"):
            success_count += 1
            self.update_status_item("Windows Firewall", "DISABLED", True)
        else:
            self.update_status_item("Windows Firewall", "FAILED", False)

        # 3. Fix Apps and Files Check (REQUIRED)
        self.log_message("Step 3/3: Fixing Apps and Files Check...")
        if self.run_command("Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer' -Name 'SmartScreenEnabled' -Value 'Off'", "Fixing Apps and Files Check"):
            success_count += 1
            self.update_status_item("Apps and Files Check", "FIXED", True)
        else:
            self.update_status_item("Apps and Files Check", "FAILED", False)

        # Summary
        if success_count == total_operations:
            self.log_message("=== GAMING MODE ENABLED SUCCESSFULLY ===", True)
            self.log_message("✅ All software requirements met for Support Tools.exe!", True)
            self.log_message("⚠️ BIOS settings still need manual configuration", None)

            messagebox.showinfo("Gaming Mode Enabled!",
                "🎮 Gaming Mode Enabled Successfully!\n\n" +
                "✅ Real-Time Protection: DISABLED\n" +
                "✅ Firewall: DISABLED\n" +
                "✅ Apps and Files Check: FIXED\n\n" +
                "⚠️ MANUAL BIOS STEPS STILL NEEDED:\n" +
                "❌ Secure Boot: Must be DISABLED\n" +
                "❌ Virtualization: Must be ENABLED\n\n" +
                "Click 'BIOS Instructions' for step-by-step guide.")
            self.status_label.config(text="Gaming Mode enabled - Support Tools.exe ready!")
        else:
            self.log_message(f"Gaming Mode partially completed ({success_count}/{total_operations} operations)", False)
            messagebox.showerror("Partial Success", f"Gaming Mode partially enabled ({success_count}/{total_operations} operations successful)\n\nSome operations failed. Try running as Administrator.")
            
    def normal_mode(self):
        self.status_label.config(text="Restoring Normal Security Mode...")
        self.root.update()

        success_count = 0
        total_operations = 3

        # 1. Enable Real-Time Protection (SAFE DEFAULT)
        if self.run_command("Set-MpPreference -DisableRealtimeMonitoring $false", "Enabling Real-Time Protection"):
            success_count += 1

        # 2. Enable Firewall (SAFE DEFAULT)
        if self.run_command("netsh advfirewall set allprofiles state on", "Enabling Windows Firewall"):
            success_count += 1

        # 3. Restore SmartScreen (SAFE DEFAULT)
        if self.run_command("Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer' -Name 'SmartScreenEnabled' -Value 'RequireAdmin'", "Restoring SmartScreen"):
            success_count += 1

        if success_count == total_operations:
            messagebox.showinfo("Normal Mode Enabled!",
                "🛡️ All Security Restored!\n\n" +
                "✅ Real-Time Protection: ENABLED\n" +
                "✅ Firewall: ENABLED\n" +
                "✅ SmartScreen: ENABLED\n\n" +
                "Your system is now fully protected.")
            self.status_label.config(text="Normal Mode enabled - Full security restored!")
        else:
            messagebox.showerror("Partial Success", f"Normal Mode partially enabled ({success_count}/{total_operations} operations successful)\n\nSome operations failed. Try running as Administrator.")

    def show_bios_instructions(self):
        instructions = """🔧 BIOS Settings Required for Support Tools.exe:

⚠️ These settings CANNOT be changed by software - you must enter BIOS manually.

1️⃣ DISABLE SECURE BOOT:
   • Restart computer
   • Press F2/F12/DEL during boot (varies by motherboard)
   • Find "Secure Boot" setting (usually under Security)
   • Set to DISABLED

2️⃣ ENABLE VIRTUALIZATION:
   • In BIOS, find "Intel VT-x" or "AMD-V" setting
   • Usually under Advanced → CPU Configuration
   • Set to ENABLED

3️⃣ SAVE AND EXIT:
   • Press F10 to save and exit
   • Computer will restart

🎯 After BIOS changes + Gaming Mode, Support Tools.exe should show all green checkmarks!"""

        messagebox.showinfo("BIOS Instructions", instructions)
            
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    # Check if running as administrator
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            messagebox.showerror("Administrator Required", "This application must be run as Administrator!")
            sys.exit(1)
    except:
        pass
        
    app = SimpleSupportTools()
    app.run()
