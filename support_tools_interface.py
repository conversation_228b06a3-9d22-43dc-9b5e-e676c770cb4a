#!/usr/bin/env python3
"""
Support Tools Interface - Clean Version
Only handles the specific requirements from Support Tools.exe
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import threading
import json
import os
import sys
import time
from datetime import datetime

class SupportToolsInterface:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Support Tools Interface - Clean Version")
        self.root.geometry("800x600")
        self.root.configure(bg='#1a1a1a')
        
        # State tracking
        self.operations_log = []
        
        # Create main interface
        self.create_interface()
        
        # Load current state
        self.refresh_status()
        
    def create_interface(self):
        # Title
        title_label = tk.Label(
            self.root, 
            text="Support Tools Interface", 
            font=("Arial", 18, "bold"),
            fg='white', 
            bg='#1a1a1a'
        )
        title_label.pack(pady=10)
        
        # Subtitle
        subtitle_label = tk.Label(
            self.root, 
            text="Only handles Support Tools.exe requirements", 
            font=("Arial", 10),
            fg='#cccccc', 
            bg='#1a1a1a'
        )
        subtitle_label.pack(pady=5)
        
        # Individual controls frame
        controls_frame = tk.LabelFrame(
            self.root,
            text="Support Tools Requirements",
            font=("Arial", 12, "bold"),
            fg='white',
            bg='#2a2a2a'
        )
        controls_frame.pack(pady=20, padx=20, fill='x')
        
        # Create individual toggle buttons
        self.create_individual_controls(controls_frame)
        
        # Status display
        status_frame = tk.LabelFrame(
            self.root,
            text="Operation Log",
            font=("Arial", 12, "bold"),
            fg='white',
            bg='#2a2a2a'
        )
        status_frame.pack(pady=10, padx=20, fill='both', expand=True)
        
        self.status_text = scrolledtext.ScrolledText(
            status_frame,
            height=12,
            bg='#1a1a1a',
            fg='white',
            font=("Consolas", 9)
        )
        self.status_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Button frame
        button_frame = tk.Frame(self.root, bg='#1a1a1a')
        button_frame.pack(pady=10)

        # Gaming Mode button (Support Tools requirements)
        gaming_btn = tk.Button(
            button_frame,
            text="Gaming Mode\n(Support Tools Ready)",
            font=("Arial", 11, "bold"),
            bg='#00aa00',
            fg='white',
            width=20,
            height=2,
            command=self.enable_gaming_mode
        )
        gaming_btn.pack(side=tk.LEFT, padx=10)

        # Normal Mode button (Restore safety)
        normal_btn = tk.Button(
            button_frame,
            text="Normal Mode\n(Restore Safety)",
            font=("Arial", 11, "bold"),
            bg='#ff6600',
            fg='white',
            width=20,
            height=2,
            command=self.enable_normal_mode
        )
        normal_btn.pack(side=tk.LEFT, padx=10)

        # Refresh button
        refresh_btn = tk.Button(
            button_frame,
            text="Refresh Status",
            font=("Arial", 11),
            bg='#0066cc',
            fg='white',
            width=15,
            command=self.refresh_status
        )
        refresh_btn.pack(side=tk.LEFT, padx=10)
        
    def create_individual_controls(self, parent):
        # Real-Time Protection (MUST BE DISABLED)
        rtp_frame = tk.Frame(parent, bg='#2a2a2a')
        rtp_frame.pack(fill='x', padx=5, pady=3)
        
        tk.Label(rtp_frame, text="Real-Time Protection:", fg='white', bg='#2a2a2a', width=25, anchor='w').pack(side=tk.LEFT)
        self.rtp_status = tk.Label(rtp_frame, text="Checking...", fg='yellow', bg='#2a2a2a', width=12)
        self.rtp_status.pack(side=tk.LEFT, padx=5)
        self.rtp_btn = tk.Button(rtp_frame, text="Disable", bg='#cc0000', fg='white', width=10, command=self.toggle_realtime_protection)
        self.rtp_btn.pack(side=tk.RIGHT, padx=5)
        
        # Firewall (MUST BE ENABLED)
        fw_frame = tk.Frame(parent, bg='#2a2a2a')
        fw_frame.pack(fill='x', padx=5, pady=3)
        
        tk.Label(fw_frame, text="Windows Firewall:", fg='white', bg='#2a2a2a', width=25, anchor='w').pack(side=tk.LEFT)
        self.fw_status = tk.Label(fw_frame, text="Checking...", fg='yellow', bg='#2a2a2a', width=12)
        self.fw_status.pack(side=tk.LEFT, padx=5)
        self.fw_btn = tk.Button(fw_frame, text="Enable", bg='#00aa00', fg='white', width=10, command=self.toggle_firewall)
        self.fw_btn.pack(side=tk.RIGHT, padx=5)
        
        # Apps and Files Check (MUST WORK)
        apps_frame = tk.Frame(parent, bg='#2a2a2a')
        apps_frame.pack(fill='x', padx=5, pady=3)
        
        tk.Label(apps_frame, text="Apps and Files Check:", fg='white', bg='#2a2a2a', width=25, anchor='w').pack(side=tk.LEFT)
        self.apps_status = tk.Label(apps_frame, text="Checking...", fg='yellow', bg='#2a2a2a', width=12)
        self.apps_status.pack(side=tk.LEFT, padx=5)
        self.apps_btn = tk.Button(apps_frame, text="Fix", bg='#0066cc', fg='white', width=10, command=self.fix_apps_check)
        self.apps_btn.pack(side=tk.RIGHT, padx=5)
        
        # BIOS Settings Info (MANUAL)
        bios_frame = tk.Frame(parent, bg='#2a2a2a')
        bios_frame.pack(fill='x', padx=5, pady=3)
        
        tk.Label(bios_frame, text="BIOS Settings:", fg='white', bg='#2a2a2a', width=25, anchor='w').pack(side=tk.LEFT)
        self.bios_status = tk.Label(bios_frame, text="Manual Required", fg='orange', bg='#2a2a2a', width=12)
        self.bios_status.pack(side=tk.LEFT, padx=5)
        self.bios_btn = tk.Button(bios_frame, text="Instructions", bg='#666666', fg='white', width=10, command=self.show_bios_instructions)
        self.bios_btn.pack(side=tk.RIGHT, padx=5)
        
    def log_operation(self, operation, success=True, details=""):
        timestamp = datetime.now().strftime("%H:%M:%S")
        status = "✓" if success else "✗"
        log_entry = f"[{timestamp}] {status} {operation}"
        if details:
            log_entry += f" - {details}"
        
        self.operations_log.append(log_entry)
        self.status_text.insert(tk.END, log_entry + "\n")
        self.status_text.see(tk.END)
        self.root.update()
        
    def run_powershell_command(self, command, description=""):
        try:
            if description:
                self.log_operation(f"Running: {description}")
            
            result = subprocess.run(
                ["powershell", "-Command", command],
                capture_output=True,
                text=True,
                check=True
            )
            
            if description:
                self.log_operation(f"Completed: {description}", True)
            return True, result.stdout
            
        except subprocess.CalledProcessError as e:
            if description:
                self.log_operation(f"Failed: {description}", False, str(e))
            return False, str(e)
            
    def toggle_realtime_protection(self):
        current_state = self.get_realtime_protection_status()
        
        if current_state:
            # Disable Real-Time Protection
            command = "Set-MpPreference -DisableRealtimeMonitoring $true"
            success, output = self.run_powershell_command(command, "Disabling Real-Time Protection")
            if success:
                self.rtp_btn.config(text="Enable", bg='#00aa00')
                self.rtp_status.config(text="DISABLED", fg='red')
        else:
            # Enable Real-Time Protection
            command = "Set-MpPreference -DisableRealtimeMonitoring $false"
            success, output = self.run_powershell_command(command, "Enabling Real-Time Protection")
            if success:
                self.rtp_btn.config(text="Disable", bg='#cc0000')
                self.rtp_status.config(text="ENABLED", fg='green')
                
    def toggle_firewall(self):
        current_state = self.get_firewall_status()
        
        if current_state:
            # Disable Firewall
            command = "netsh advfirewall set allprofiles state off"
            success, output = self.run_powershell_command(command, "Disabling Windows Firewall")
            if success:
                self.fw_btn.config(text="Enable", bg='#00aa00')
                self.fw_status.config(text="DISABLED", fg='red')
        else:
            # Enable Firewall
            command = "netsh advfirewall set allprofiles state on"
            success, output = self.run_powershell_command(command, "Enabling Windows Firewall")
            if success:
                self.fw_btn.config(text="Disable", bg='#cc0000')
                self.fw_status.config(text="ENABLED", fg='green')
                
    def fix_apps_check(self):
        self.log_operation("Fixing Apps and Files check...")
        
        # Fix SmartScreen settings
        commands = [
            "Set-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer' -Name 'SmartScreenEnabled' -Value 'RequireAdmin'",
            "Set-ItemProperty -Path 'HKCU:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\AppHost' -Name 'EnableWebContentEvaluation' -Value 1 -ErrorAction SilentlyContinue"
        ]
        
        success_count = 0
        for cmd in commands:
            success, output = self.run_powershell_command(cmd)
            if success:
                success_count += 1
                
        if success_count > 0:
            self.apps_status.config(text="WORKING", fg='green')
            self.log_operation("Apps and Files check fixed", True)
        else:
            self.apps_status.config(text="FAILED", fg='red')
            self.log_operation("Apps and Files check fix failed", False)
            
    def show_bios_instructions(self):
        instructions = """
BIOS Settings Required for Support Tools.exe:

1. SECURE BOOT: Must be DISABLED
   - Restart computer
   - Press F2/F12/DEL during boot
   - Find "Secure Boot" setting
   - Set to DISABLED
   
2. VIRTUALIZATION: Must be ENABLED
   - In BIOS, find "Intel VT-x" or "AMD-V"
   - Set to ENABLED
   - May be under "Advanced" or "CPU" settings
   
3. Save and Exit BIOS
   - Press F10 to save and exit
   - Computer will restart

These settings CANNOT be changed by software.
You must enter BIOS manually.
        """
        
        messagebox.showinfo("BIOS Instructions", instructions)
        self.log_operation("BIOS instructions displayed", True)
        
    def get_realtime_protection_status(self):
        try:
            result = subprocess.run(
                ["powershell", "-Command", "Get-MpPreference | Select-Object DisableRealtimeMonitoring"],
                capture_output=True,
                text=True
            )
            return "False" in result.stdout
        except:
            return True
            
    def get_firewall_status(self):
        try:
            result = subprocess.run(
                ["powershell", "-Command", "netsh advfirewall show allprofiles state"],
                capture_output=True,
                text=True
            )
            return "ON" in result.stdout
        except:
            return False
            
    def get_apps_check_status(self):
        try:
            result = subprocess.run(
                ["powershell", "-Command", "Get-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer' -Name 'SmartScreenEnabled' | Select-Object SmartScreenEnabled"],
                capture_output=True,
                text=True
            )
            return "RequireAdmin" in result.stdout
        except:
            return False
            
    def refresh_status(self):
        self.log_operation("Refreshing system status...")
        
        # Update Real-Time Protection status
        if self.get_realtime_protection_status():
            self.rtp_btn.config(text="Disable", bg='#cc0000')
            self.rtp_status.config(text="ENABLED", fg='green')
        else:
            self.rtp_btn.config(text="Enable", bg='#00aa00')
            self.rtp_status.config(text="DISABLED", fg='red')
            
        # Update Firewall status
        if self.get_firewall_status():
            self.fw_btn.config(text="Disable", bg='#cc0000')
            self.fw_status.config(text="ENABLED", fg='green')
        else:
            self.fw_btn.config(text="Enable", bg='#00aa00')
            self.fw_status.config(text="DISABLED", fg='red')
            
        # Update Apps check status
        if self.get_apps_check_status():
            self.apps_status.config(text="WORKING", fg='green')
        else:
            self.apps_status.config(text="NEEDS FIX", fg='orange')
            
        self.log_operation("Status refresh completed", True)

    def enable_gaming_mode(self):
        """Configure system for Support Tools.exe requirements"""
        self.log_operation("=== ENABLING GAMING MODE ===", True)
        self.log_operation("Configuring system for Support Tools.exe...", True)

        success_count = 0
        total_operations = 2

        # 1. Disable Real-Time Protection (REQUIRED)
        if self.get_realtime_protection_status():
            command = "Set-MpPreference -DisableRealtimeMonitoring $true"
            success, output = self.run_powershell_command(command, "Disabling Real-Time Protection")
            if success:
                success_count += 1
                self.rtp_btn.config(text="Enable", bg='#00aa00')
                self.rtp_status.config(text="DISABLED", fg='red')
        else:
            success_count += 1
            self.log_operation("Real-Time Protection already disabled", True)

        # 2. Enable Firewall (REQUIRED)
        if not self.get_firewall_status():
            command = "netsh advfirewall set allprofiles state on"
            success, output = self.run_powershell_command(command, "Enabling Windows Firewall")
            if success:
                success_count += 1
                self.fw_btn.config(text="Disable", bg='#cc0000')
                self.fw_status.config(text="ENABLED", fg='green')
        else:
            success_count += 1
            self.log_operation("Windows Firewall already enabled", True)

        # 3. Fix Apps and Files check
        self.fix_apps_check()

        # Summary
        if success_count == total_operations:
            self.log_operation("=== GAMING MODE ENABLED SUCCESSFULLY ===", True)
            self.log_operation("Support Tools.exe should now pass software checks!", True)
            self.log_operation("REMINDER: You still need to configure BIOS settings manually", True)
        else:
            self.log_operation(f"Gaming Mode partially enabled ({success_count}/{total_operations} operations successful)", False)

    def enable_normal_mode(self):
        """Restore all security settings to safe defaults"""
        self.log_operation("=== ENABLING NORMAL MODE ===", True)
        self.log_operation("Restoring all security settings to safe defaults...", True)

        success_count = 0
        total_operations = 2

        # 1. Enable Real-Time Protection (SAFE DEFAULT)
        if not self.get_realtime_protection_status():
            command = "Set-MpPreference -DisableRealtimeMonitoring $false"
            success, output = self.run_powershell_command(command, "Enabling Real-Time Protection")
            if success:
                success_count += 1
                self.rtp_btn.config(text="Disable", bg='#cc0000')
                self.rtp_status.config(text="ENABLED", fg='green')
        else:
            success_count += 1
            self.log_operation("Real-Time Protection already enabled", True)

        # 2. Enable Firewall (SAFE DEFAULT)
        if not self.get_firewall_status():
            command = "netsh advfirewall set allprofiles state on"
            success, output = self.run_powershell_command(command, "Enabling Windows Firewall")
            if success:
                success_count += 1
                self.fw_btn.config(text="Disable", bg='#cc0000')
                self.fw_status.config(text="ENABLED", fg='green')
        else:
            success_count += 1
            self.log_operation("Windows Firewall already enabled", True)

        # 3. Ensure Apps and Files check is working
        self.fix_apps_check()

        # Summary
        if success_count == total_operations:
            self.log_operation("=== NORMAL MODE ENABLED SUCCESSFULLY ===", True)
            self.log_operation("All security settings restored to safe defaults!", True)
            self.log_operation("Your system is now fully protected", True)
        else:
            self.log_operation(f"Normal Mode partially enabled ({success_count}/{total_operations} operations successful)", False)

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    # Check if running as administrator
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            messagebox.showerror("Administrator Required", "This application must be run as Administrator!")
            sys.exit(1)
    except:
        pass
        
    app = SupportToolsInterface()
    app.run()
