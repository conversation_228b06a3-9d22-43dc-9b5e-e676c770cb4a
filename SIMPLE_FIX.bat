@echo off
title SIMPLE SUPPORT TOOLS FIX
color 0E

echo.
echo SIMPLE SUPPORT TOOLS FIX
echo ========================
echo.

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [SUCCESS] Administrator privileges confirmed
    echo.
    
    echo FIXING SECURE BOOT...
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\SecureBoot\State" /v UEFISecureBootEnabled /t REG_DWORD /d 0 /f >nul 2>&1
    if %errorLevel% == 0 (echo [SUCCESS] Secure Boot registry 1) else (echo [FAILED] Secure Boot registry 1)
    
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\SecureBoot" /v SecureBootEnabled /t REG_DWORD /d 0 /f >nul 2>&1
    if %errorLevel% == 0 (echo [SUCCESS] Secure Boot registry 2) else (echo [FAILED] Secure Boot registry 2)
    
    bcdedit /set {bootmgr} secureboot off >nul 2>&1
    if %errorLevel% == 0 (echo [SUCCESS] Boot Manager Secure Boot) else (echo [FAILED] Boot Manager Secure Boot)
    
    echo.
    echo FIXING VIRTUALIZATION...
    dism /online /enable-feature /featurename:Microsoft-Hyper-V-All /all /norestart >nul 2>&1
    if %errorLevel% == 0 (echo [SUCCESS] Hyper-V Features) else (echo [FAILED] Hyper-V Features)
    
    dism /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart >nul 2>&1
    if %errorLevel% == 0 (echo [SUCCESS] VM Platform) else (echo [FAILED] VM Platform)
    
    bcdedit /set hypervisorlaunchtype auto >nul 2>&1
    if %errorLevel% == 0 (echo [SUCCESS] Hypervisor Launch) else (echo [FAILED] Hypervisor Launch)
    
    echo.
    echo FIXING FIREWALL...
    netsh advfirewall reset >nul 2>&1
    if %errorLevel% == 0 (echo [SUCCESS] Firewall Reset) else (echo [FAILED] Firewall Reset)
    
    sc config MpsSvc start= auto >nul 2>&1
    if %errorLevel% == 0 (echo [SUCCESS] Firewall Auto Start) else (echo [FAILED] Firewall Auto Start)
    
    sc start MpsSvc >nul 2>&1
    if %errorLevel% == 0 (echo [SUCCESS] Firewall Service Start) else (echo [FAILED] Firewall Service Start)
    
    echo.
    echo FIXING DEFENDER...
    sc config WinDefend start= auto >nul 2>&1
    if %errorLevel% == 0 (echo [SUCCESS] Defender Auto Start) else (echo [FAILED] Defender Auto Start)
    
    sc start WinDefend >nul 2>&1
    if %errorLevel% == 0 (echo [SUCCESS] Defender Service Start) else (echo [FAILED] Defender Service Start)
    
    powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $false" >nul 2>&1
    if %errorLevel% == 0 (echo [SUCCESS] Enable Real-time Protection) else (echo [FAILED] Enable Real-time Protection)
    
    echo.
    echo ========================
    echo SOFTWARE FIXES COMPLETED
    echo ========================
    echo.
    echo NEXT STEPS REQUIRED:
    echo 1. RESTART your computer
    echo 2. Enter BIOS (F2, F12, DEL, or ESC during boot)
    echo 3. DISABLE Secure Boot (Security section)
    echo 4. ENABLE Virtualization (Advanced/CPU section)
    echo 5. SAVE and exit BIOS
    echo 6. Run Support Tools.exe to verify
    echo.
    
    choice /c YN /m "Would you like to restart now to access BIOS? (Y/N)"
    if errorlevel 2 goto :no_restart
    if errorlevel 1 goto :restart
    
    :restart
    echo.
    echo Restarting in 10 seconds...
    echo Enter BIOS during boot to make manual changes!
    timeout /t 10
    shutdown /r /t 0
    goto :end
    
    :no_restart
    echo.
    echo Please restart manually and enter BIOS
    echo Check BIOS_INSTRUCTIONS.txt for detailed steps
    goto :end
    
) else (
    echo [ERROR] Administrator privileges required!
    echo Right-click this file and "Run as administrator"
    echo.
    pause
)

:end
pause
exit /b
