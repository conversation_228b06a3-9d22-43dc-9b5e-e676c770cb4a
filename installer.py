#!/usr/bin/env python3
"""
GameBoost Pro Installer
Professional installation system for commercial distribution
"""

import os
import sys
import shutil
import subprocess
import winreg
from pathlib import Path
import requests
import zipfile
import tempfile

class GameBoostInstaller:
    def __init__(self):
        self.product_name = "GameBoost Pro"
        self.version = "1.0.0"
        self.company = "TechFlow Solutions"
        
        # Installation paths
        self.install_dir = Path("C:/Program Files/GameBoost Pro")
        self.data_dir = Path(os.path.expanduser("~/AppData/Local/GameBoost Pro"))
        self.desktop_shortcut = Path(os.path.expanduser("~/Desktop/GameBoost Pro.lnk"))
        self.start_menu_dir = Path(os.path.expanduser("~/AppData/Roaming/Microsoft/Windows/Start Menu/Programs/GameBoost Pro"))
        
        # Required files
        self.required_files = [
            "main.py",
            "security_toggle.py", 
            "branding.py",
            "license_system.py",
            "modules/",
            "requirements.txt"
        ]
        
    def check_admin_privileges(self):
        """Check if running with administrator privileges"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def install_python_dependencies(self):
        """Install required Python packages"""
        print("📦 Installing Python dependencies...")
        
        dependencies = [
            "customtkinter",
            "requests", 
            "psutil",
            "wmi"
        ]
        
        for dep in dependencies:
            try:
                print(f"   Installing {dep}...")
                subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                             check=True, capture_output=True)
                print(f"   ✅ {dep} installed successfully")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ Failed to install {dep}: {e}")
                return False
        
        return True
    
    def create_directories(self):
        """Create installation directories"""
        print("📁 Creating directories...")
        
        directories = [self.install_dir, self.data_dir, self.start_menu_dir]
        
        for directory in directories:
            try:
                directory.mkdir(parents=True, exist_ok=True)
                print(f"   ✅ Created: {directory}")
            except Exception as e:
                print(f"   ❌ Failed to create {directory}: {e}")
                return False
        
        return True
    
    def copy_files(self):
        """Copy application files to installation directory"""
        print("📋 Copying application files...")
        
        source_dir = Path(".")
        
        for file_path in self.required_files:
            source = source_dir / file_path
            dest = self.install_dir / file_path
            
            try:
                if source.is_file():
                    dest.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(source, dest)
                    print(f"   ✅ Copied: {file_path}")
                elif source.is_dir():
                    if dest.exists():
                        shutil.rmtree(dest)
                    shutil.copytree(source, dest)
                    print(f"   ✅ Copied directory: {file_path}")
                else:
                    print(f"   ⚠️ Not found: {file_path}")
            except Exception as e:
                print(f"   ❌ Failed to copy {file_path}: {e}")
                return False
        
        return True
    
    def create_shortcuts(self):
        """Create desktop and start menu shortcuts"""
        print("🔗 Creating shortcuts...")
        
        try:
            # Create batch file to launch the application
            launcher_path = self.install_dir / "GameBoost Pro.bat"
            with open(launcher_path, 'w') as f:
                f.write(f'@echo off\n')
                f.write(f'cd /d "{self.install_dir}"\n')
                f.write(f'python main.py\n')
                f.write(f'pause\n')
            
            # Create PowerShell script for desktop shortcut
            ps_script = f'''
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("{self.desktop_shortcut}")
$Shortcut.TargetPath = "{launcher_path}"
$Shortcut.WorkingDirectory = "{self.install_dir}"
$Shortcut.IconLocation = "{self.install_dir}/icon.ico"
$Shortcut.Description = "GameBoost Pro - Professional Gaming Optimization"
$Shortcut.Save()
'''
            
            # Execute PowerShell script
            subprocess.run(["powershell", "-Command", ps_script], check=True)
            print(f"   ✅ Desktop shortcut created")
            
            # Start menu shortcut
            start_menu_shortcut = self.start_menu_dir / "GameBoost Pro.lnk"
            ps_script_start = f'''
$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("{start_menu_shortcut}")
$Shortcut.TargetPath = "{launcher_path}"
$Shortcut.WorkingDirectory = "{self.install_dir}"
$Shortcut.IconLocation = "{self.install_dir}/icon.ico"
$Shortcut.Description = "GameBoost Pro - Professional Gaming Optimization"
$Shortcut.Save()
'''
            subprocess.run(["powershell", "-Command", ps_script_start], check=True)
            print(f"   ✅ Start menu shortcut created")
            
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to create shortcuts: {e}")
            return False
    
    def register_uninstaller(self):
        """Register uninstaller in Windows Add/Remove Programs"""
        print("📝 Registering uninstaller...")
        
        try:
            # Create uninstaller script
            uninstaller_path = self.install_dir / "uninstall.bat"
            with open(uninstaller_path, 'w') as f:
                f.write('@echo off\n')
                f.write('echo Uninstalling GameBoost Pro...\n')
                f.write(f'rmdir /s /q "{self.install_dir}"\n')
                f.write(f'rmdir /s /q "{self.data_dir}"\n')
                f.write(f'del "{self.desktop_shortcut}"\n')
                f.write(f'rmdir /s /q "{self.start_menu_dir}"\n')
                f.write('echo GameBoost Pro has been uninstalled.\n')
                f.write('pause\n')
            
            # Register in Windows registry
            registry_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\GameBoost Pro"
            
            with winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, registry_path) as key:
                winreg.SetValueEx(key, "DisplayName", 0, winreg.REG_SZ, "GameBoost Pro")
                winreg.SetValueEx(key, "DisplayVersion", 0, winreg.REG_SZ, self.version)
                winreg.SetValueEx(key, "Publisher", 0, winreg.REG_SZ, self.company)
                winreg.SetValueEx(key, "InstallLocation", 0, winreg.REG_SZ, str(self.install_dir))
                winreg.SetValueEx(key, "UninstallString", 0, winreg.REG_SZ, str(uninstaller_path))
                winreg.SetValueEx(key, "DisplayIcon", 0, winreg.REG_SZ, str(self.install_dir / "icon.ico"))
                winreg.SetValueEx(key, "NoModify", 0, winreg.REG_DWORD, 1)
                winreg.SetValueEx(key, "NoRepair", 0, winreg.REG_DWORD, 1)
            
            print("   ✅ Uninstaller registered")
            return True
            
        except Exception as e:
            print(f"   ❌ Failed to register uninstaller: {e}")
            return False
    
    def install(self):
        """Main installation process"""
        print("🎮 GAMEBOOST PRO INSTALLER")
        print("=" * 50)
        print(f"Version: {self.version}")
        print(f"Company: {self.company}")
        print()
        
        # Check admin privileges
        if not self.check_admin_privileges():
            print("❌ Administrator privileges required!")
            print("Please run this installer as Administrator.")
            input("Press Enter to exit...")
            return False
        
        print("✅ Administrator privileges confirmed")
        print()
        
        # Installation steps
        steps = [
            ("Installing Python dependencies", self.install_python_dependencies),
            ("Creating directories", self.create_directories),
            ("Copying application files", self.copy_files),
            ("Creating shortcuts", self.create_shortcuts),
            ("Registering uninstaller", self.register_uninstaller)
        ]
        
        for step_name, step_func in steps:
            print(f"🔄 {step_name}...")
            if not step_func():
                print(f"❌ Installation failed at: {step_name}")
                input("Press Enter to exit...")
                return False
            print()
        
        print("🎉 INSTALLATION COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print("✅ GameBoost Pro has been installed")
        print(f"📁 Installation directory: {self.install_dir}")
        print(f"🖥️ Desktop shortcut created")
        print(f"📋 Start menu entry created")
        print()
        print("🚀 You can now launch GameBoost Pro from:")
        print("   • Desktop shortcut")
        print("   • Start menu")
        print("   • Windows search")
        print()
        
        # Ask to launch
        launch = input("Would you like to launch GameBoost Pro now? (y/n): ").lower().strip()
        if launch == 'y':
            try:
                subprocess.Popen([str(self.install_dir / "GameBoost Pro.bat")], shell=True)
                print("🎮 GameBoost Pro is starting...")
            except Exception as e:
                print(f"❌ Failed to launch: {e}")
        
        input("Press Enter to exit installer...")
        return True

def main():
    installer = GameBoostInstaller()
    installer.install()

if __name__ == "__main__":
    main()
