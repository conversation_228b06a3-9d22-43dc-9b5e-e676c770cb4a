@echo off
title Support Tools Interface Launcher
echo.
echo ========================================
echo  Support Tools Interface Launcher
echo ========================================
echo.
echo Starting Support Tools Interface...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from python.org
    pause
    exit /b 1
)

REM Check if dependencies are installed
echo Checking Python dependencies...
python -c "import psutil" >nul 2>&1
if errorlevel 1 (
    echo.
    echo Python dependencies not found!
    echo Installing dependencies automatically...
    echo.
    call install_dependencies.bat
    if errorlevel 1 (
        echo.
        echo Dependency installation failed!
        echo Please run install_dependencies.bat manually
        pause
        exit /b 1
    )
)

REM Run the Support Tools Interface as Administrator
echo Launching Support Tools Interface...
powershell -Command "Start-Process python -ArgumentList 'support_tools_interface.py' -Verb RunAs"

echo.
echo Support Tools Interface launched!
echo Check the new window that opened.
echo.
pause
