@echo off
title SAFE MODE - RESTORE ALL SECURITY
color 0A

echo.
echo  ==========================================
echo   🛡️ SAFE MODE - RESTORE ALL SECURITY
echo  ==========================================
echo.
echo  This will restore ALL Windows security features:
echo  ✅ Windows Defender (Real-time Protection)
echo  ✅ Windows Firewall (All profiles)
echo  ✅ SmartScreen Protection
echo  ✅ User Account Control (UAC)
echo  ✅ Fast Boot (if previously enabled)
echo  ✅ Windows Update
echo.
echo  💾 Will restore your previous security settings
echo  🔄 You can disable them again with GAMING_MODE.bat
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  [!] This will restore Windows security protection
    echo  [!] Recommended when not gaming
    echo.
    pause
    
    REM Change to script directory
    cd /d "%~dp0"
    
    echo  [🛡️] Enabling Safe Mode...
    echo.
    
    REM Run the toggle script
    python -c "from security_toggle import SecurityToggle; t = SecurityToggle(); t.enable_safe_mode()"
    
    echo.
    echo  ==========================================
    echo   🛡️ SAFE MODE ENABLED!
    echo  ==========================================
    echo.
    echo  [✅] All security features restored
    echo  [🛡️] Your system is now protected
    echo  [🔄] Run GAMING_MODE.bat to disable for gaming
    echo  [⚠️] Restart recommended for full effect
    echo.
    
    choice /c YN /m "Would you like to restart now? (Y/N)"
    if errorlevel 2 goto :no_restart
    if errorlevel 1 goto :restart
    
    :restart
    echo  [🔄] Restarting in 10 seconds...
    timeout /t 10
    shutdown /r /t 0
    goto :end
    
    :no_restart
    echo  [⚠️] Please restart manually for full effect
    goto :end
    
    :end
    pause
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator.
    echo  Right-click this file and select "Run as administrator"
    echo.
    pause
    
    REM Try to restart with admin privileges
    echo  Attempting to restart with administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
)

exit /b
