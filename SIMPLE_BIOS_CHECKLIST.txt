🎯 SIMPLE BIOS CHANGES CHECKLIST
================================

STEP 1: COMPLETE SOFTWARE FIXES
□ Right-click SIMPLE_FIX.bat → Run as administrator
□ Wait for completion message

STEP 2: AUTO BIOS ENTRY
□ Right-click AUTO_BIOS_ENTRY.bat → Run as administrator
□ Choose "Y" to boot to BIOS automatically
□ Wait 60 seconds - system will restart to BIOS

STEP 3: IN BIOS INTERFACE
□ Use arrow keys to navigate
□ Look for these 2 changes:

CHANGE #1: DISABLE SECURE BOOT
□ Go to "Security" tab (use → arrow key)
□ Find "Secure Boot" option
□ Press ENTER on Secure Boot
□ Change from "Enabled" to "Disabled"
□ Press ENTER to confirm

CHANGE #2: ENABLE VIRTUALIZATION  
□ Go to "Advanced" tab (use → arrow key)
□ Find "CPU Configuration" or "Processor"
□ Press ENTER to open it
□ Find "Intel VT-x" or "AMD-V" or "Virtualization Technology"
□ Change from "Disabled" to "Enabled"
□ Press ENTER to confirm

STEP 4: SAVE AND EXIT
□ Press F10 (Save and Exit)
□ OR go to "Save & Exit" tab
□ Select "Save Changes and Exit"
□ Choose "Yes" to confirm
□ System will restart

STEP 5: VERIFY SUCCESS
□ Let Windows boot normally
□ Run Support Tools.exe
□ All checks should now pass ✅

QUICK TIPS:
- Use arrow keys to navigate BIOS
- ENTER = select/change option
- ESC = go back
- F10 = save and exit
- Don't worry if menus look different - just find the options!

WHAT YOU'RE LOOKING FOR:
- Secure Boot: Change to DISABLED
- Virtualization: Change to ENABLED
- Save changes before exiting

That's it! Just 2 simple changes in BIOS!
