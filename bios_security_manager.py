#!/usr/bin/env python3
"""
BIOS and Security Settings Manager
Comprehensive system to fix virtualization, secure boot, firewall, and real-time protection
"""

import subprocess
import json
import os
import winreg
from datetime import datetime

class BiosSecurityManager:
    def __init__(self):
        self.results = []
        self.state_file = "bios_security_state.json"
    
    def log(self, message):
        """Add message to results"""
        print(message)
        self.results.append(message)
    
    def run_command(self, command, description):
        """Run command and return success status"""
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                self.log(f"✅ {description}: SUCCESS")
                return True
            else:
                self.log(f"❌ {description}: FAILED")
                return False
        except subprocess.TimeoutExpired:
            self.log(f"⏰ {description}: TIMEOUT")
            return False
        except Exception as e:
            self.log(f"❌ {description}: ERROR - {str(e)}")
            return False
    
    def is_admin(self):
        """Check if running with administrator privileges"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def check_virtualization_status(self):
        """Check if virtualization is enabled"""
        try:
            # Check via systeminfo
            result = subprocess.run('systeminfo', shell=True, capture_output=True, text=True)
            if "Hyper-V Requirements" in result.stdout:
                lines = result.stdout.split('\n')
                for line in lines:
                    if "Virtualization Enabled In Firmware" in line:
                        return "Yes" in line
            
            # Fallback: Check via PowerShell
            ps_cmd = 'powershell.exe -Command "Get-ComputerInfo | Select-Object HyperVRequirementVirtualizationFirmwareEnabled"'
            result = subprocess.run(ps_cmd, shell=True, capture_output=True, text=True)
            return "True" in result.stdout
            
        except:
            return False
    
    def check_secure_boot_status(self):
        """Check if Secure Boot is enabled"""
        try:
            # Check via PowerShell
            ps_cmd = 'powershell.exe -Command "Confirm-SecureBootUEFI"'
            result = subprocess.run(ps_cmd, shell=True, capture_output=True, text=True)
            return "True" in result.stdout
        except:
            return False
    
    def check_firewall_status(self):
        """Check Windows Firewall status"""
        try:
            result = subprocess.run('netsh advfirewall show allprofiles state', shell=True, capture_output=True, text=True)
            return "ON" in result.stdout
        except:
            return False
    
    def check_realtime_protection_status(self):
        """Check Windows Defender Real-time Protection status"""
        try:
            ps_cmd = 'powershell.exe -Command "Get-MpPreference | Select-Object DisableRealtimeMonitoring"'
            result = subprocess.run(ps_cmd, shell=True, capture_output=True, text=True)
            return "False" in result.stdout  # False means real-time protection is enabled
        except:
            return True  # Assume enabled if can't check
    
    def get_current_status(self):
        """Get current status of all settings"""
        return {
            "virtualization_enabled": self.check_virtualization_status(),
            "secure_boot_enabled": self.check_secure_boot_status(),
            "firewall_enabled": self.check_firewall_status(),
            "realtime_protection_enabled": self.check_realtime_protection_status()
        }
    
    def disable_secure_boot(self):
        """Disable Secure Boot (requires restart)"""
        if not self.is_admin():
            self.log("❌ Administrator privileges required!")
            return False
        
        self.log("🔒 DISABLING SECURE BOOT...")
        self.log("=" * 50)
        
        success_count = 0
        total_operations = 0
        
        # Method 1: PowerShell disable
        self.log("\n⚡ POWERSHELL SECURE BOOT DISABLE...")
        ps_commands = [
            ('powershell.exe -Command "Set-SecureBootUEFI -Mode Off"', "PowerShell Secure Boot Disable"),
            ('powershell.exe -Command "Disable-SecureBootPolicy"', "PowerShell Secure Boot Policy Disable"),
        ]
        
        for cmd, desc in ps_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Method 2: Registry modifications
        self.log("\n📝 REGISTRY SECURE BOOT DISABLE...")
        registry_commands = [
            ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\SecureBoot\\State" /v UEFISecureBootEnabled /t REG_DWORD /d 0 /f', "Secure Boot Registry Disable"),
            ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\SecureBoot" /v SecureBootEnabled /t REG_DWORD /d 0 /f', "Secure Boot Control Disable"),
        ]
        
        for cmd, desc in registry_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Method 3: BCDEdit modifications
        self.log("\n🔧 BCDEDIT SECURE BOOT DISABLE...")
        bcdedit_commands = [
            ('bcdedit /set {bootmgr} secureboot off', "Boot Manager Secure Boot Off"),
            ('bcdedit /set {current} secureboot off', "Current Boot Secure Boot Off"),
        ]
        
        for cmd, desc in bcdedit_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        success_rate = success_count / total_operations if total_operations > 0 else 0
        
        self.log(f"\n📊 SECURE BOOT DISABLE RESULTS: {success_count}/{total_operations} ({success_rate*100:.1f}%)")
        
        if success_rate >= 0.3:  # Lower threshold since some methods may fail
            self.log("✅ SECURE BOOT DISABLE INITIATED!")
            self.log("⚠️ RESTART REQUIRED for changes to take effect")
            self.log("💡 You may need to disable Secure Boot in BIOS manually")
            return True
        else:
            self.log("⚠️ SECURE BOOT DISABLE FAILED")
            self.log("💡 Manual BIOS configuration required")
            return False
    
    def enable_virtualization_software(self):
        """Enable virtualization features in Windows"""
        if not self.is_admin():
            self.log("❌ Administrator privileges required!")
            return False
        
        self.log("🖥️ ENABLING VIRTUALIZATION FEATURES...")
        self.log("=" * 50)
        
        success_count = 0
        total_operations = 0
        
        # Enable Hyper-V features
        self.log("\n⚡ ENABLING HYPER-V FEATURES...")
        hyperv_commands = [
            ('dism /online /enable-feature /featurename:Microsoft-Hyper-V-All /all /norestart', "Hyper-V All Features"),
            ('dism /online /enable-feature /featurename:Microsoft-Hyper-V-Hypervisor /all /norestart', "Hyper-V Hypervisor"),
            ('dism /online /enable-feature /featurename:Microsoft-Hyper-V-Management-PowerShell /all /norestart', "Hyper-V PowerShell"),
        ]
        
        for cmd, desc in hyperv_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Enable Windows Subsystem for Linux
        self.log("\n🐧 ENABLING WSL FEATURES...")
        wsl_commands = [
            ('dism /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart', "WSL Feature"),
            ('dism /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart', "Virtual Machine Platform"),
        ]
        
        for cmd, desc in wsl_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Enable additional virtualization features
        self.log("\n🔧 ENABLING ADDITIONAL VIRTUALIZATION...")
        additional_commands = [
            ('bcdedit /set hypervisorlaunchtype auto', "Hypervisor Launch Type"),
            ('powershell.exe -Command "Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V-All -All -NoRestart"', "PS Hyper-V Enable"),
        ]
        
        for cmd, desc in additional_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        success_rate = success_count / total_operations if total_operations > 0 else 0
        
        self.log(f"\n📊 VIRTUALIZATION ENABLE RESULTS: {success_count}/{total_operations} ({success_rate*100:.1f}%)")
        
        if success_rate >= 0.5:
            self.log("✅ VIRTUALIZATION FEATURES ENABLED!")
            self.log("⚠️ RESTART REQUIRED for changes to take effect")
            self.log("💡 You may still need to enable virtualization in BIOS")
            return True
        else:
            self.log("⚠️ VIRTUALIZATION ENABLE FAILED")
            return False
    
    def fix_firewall_issues(self):
        """Fix Windows Firewall issues"""
        if not self.is_admin():
            self.log("❌ Administrator privileges required!")
            return False
        
        self.log("🔥 FIXING FIREWALL ISSUES...")
        self.log("=" * 50)
        
        success_count = 0
        total_operations = 0
        
        # Reset firewall to defaults
        self.log("\n🔄 RESETTING FIREWALL...")
        reset_commands = [
            ('netsh advfirewall reset', "Firewall Reset"),
            ('netsh firewall reset', "Legacy Firewall Reset"),
        ]
        
        for cmd, desc in reset_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Restart firewall service
        self.log("\n🔧 RESTARTING FIREWALL SERVICE...")
        service_commands = [
            ('sc stop MpsSvc', "Stop Firewall Service"),
            ('sc start MpsSvc', "Start Firewall Service"),
            ('sc config MpsSvc start= auto', "Set Firewall Auto Start"),
        ]
        
        for cmd, desc in service_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Configure firewall profiles
        self.log("\n⚙️ CONFIGURING FIREWALL PROFILES...")
        config_commands = [
            ('netsh advfirewall set allprofiles state on', "Enable All Profiles"),
            ('netsh advfirewall set allprofiles firewallpolicy blockinbound,allowoutbound', "Set Default Policy"),
        ]
        
        for cmd, desc in config_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        success_rate = success_count / total_operations if total_operations > 0 else 0
        
        self.log(f"\n📊 FIREWALL FIX RESULTS: {success_count}/{total_operations} ({success_rate*100:.1f}%)")
        
        if success_rate >= 0.6:
            self.log("✅ FIREWALL ISSUES FIXED!")
            return True
        else:
            self.log("⚠️ FIREWALL ISSUES PARTIALLY FIXED")
            return False
    
    def fix_realtime_protection(self):
        """Fix Windows Defender Real-time Protection"""
        if not self.is_admin():
            self.log("❌ Administrator privileges required!")
            return False
        
        self.log("🛡️ FIXING REAL-TIME PROTECTION...")
        self.log("=" * 50)
        
        success_count = 0
        total_operations = 0
        
        # Restart Windows Defender service
        self.log("\n🔄 RESTARTING DEFENDER SERVICE...")
        service_commands = [
            ('sc stop WinDefend', "Stop Defender Service"),
            ('sc start WinDefend', "Start Defender Service"),
            ('sc config WinDefend start= auto', "Set Defender Auto Start"),
        ]
        
        for cmd, desc in service_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Reset Defender settings
        self.log("\n⚙️ RESETTING DEFENDER SETTINGS...")
        reset_commands = [
            ('powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $false"', "Enable Real-time Protection"),
            ('powershell.exe -Command "Set-MpPreference -DisableBehaviorMonitoring $false"', "Enable Behavior Monitoring"),
            ('powershell.exe -Command "Set-MpPreference -MAPSReporting Advanced"', "Enable Cloud Protection"),
        ]
        
        for cmd, desc in reset_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        # Update definitions
        self.log("\n📥 UPDATING DEFENDER DEFINITIONS...")
        update_commands = [
            ('powershell.exe -Command "Update-MpSignature"', "Update Signatures"),
            ('powershell.exe -Command "Start-MpScan -ScanType QuickScan"', "Quick Scan Test"),
        ]
        
        for cmd, desc in update_commands:
            if self.run_command(cmd, desc):
                success_count += 1
            total_operations += 1
        
        success_rate = success_count / total_operations if total_operations > 0 else 0
        
        self.log(f"\n📊 REAL-TIME PROTECTION FIX RESULTS: {success_count}/{total_operations} ({success_rate*100:.1f}%)")
        
        if success_rate >= 0.6:
            self.log("✅ REAL-TIME PROTECTION FIXED!")
            return True
        else:
            self.log("⚠️ REAL-TIME PROTECTION PARTIALLY FIXED")
            return False
    
    def create_bios_instructions(self):
        """Create detailed BIOS configuration instructions"""
        instructions = """
🔧 BIOS CONFIGURATION INSTRUCTIONS
==================================

⚠️ IMPORTANT: These settings require BIOS/UEFI access

🖥️ VIRTUALIZATION SETTINGS:
1. Restart computer and enter BIOS (usually F2, F12, DEL, or ESC during boot)
2. Look for these settings (names vary by manufacturer):
   • Intel: "Intel VT-x" or "Virtualization Technology"
   • AMD: "AMD-V" or "SVM Mode"
   • Location: Usually in "Advanced" → "CPU Configuration"
3. Set to "Enabled"
4. Save and exit BIOS

🔒 SECURE BOOT SETTINGS:
1. In BIOS, navigate to "Security" or "Boot" section
2. Find "Secure Boot" option
3. Set to "Disabled"
4. May need to set "Boot Mode" to "Legacy" or "CSM Enabled"
5. Save and exit BIOS

🏭 MANUFACTURER-SPECIFIC LOCATIONS:

ASUS:
• Advanced → CPU Configuration → Intel Virtualization Technology
• Boot → Secure Boot → OS Type → Other OS

MSI:
• OC → CPU Features → Intel Virtualization Tech
• Settings → Security → Secure Boot → Secure Boot Mode

Gigabyte:
• M.I.T. → Advanced Frequency Settings → Advanced CPU Settings
• BIOS → Windows 8/10 Features → Secure Boot

Dell:
• Virtualization Support → Virtualization
• Secure Boot → Secure Boot Enable

HP:
• System Configuration → Virtualization Technology
• System Configuration → Secure Boot Configuration

⚠️ AFTER BIOS CHANGES:
1. Save settings and restart
2. Run this tool again to verify changes
3. Some changes may require multiple restarts
        """
        
        # Save instructions to file
        with open("BIOS_INSTRUCTIONS.txt", "w") as f:
            f.write(instructions)
        
        self.log("📄 BIOS instructions saved to BIOS_INSTRUCTIONS.txt")
        return instructions
    
    def get_results(self):
        """Get operation results"""
        return self.results
    
    def clear_results(self):
        """Clear operation results"""
        self.results = []

# Example usage and testing
if __name__ == "__main__":
    manager = BiosSecurityManager()
    
    print("🔧 BIOS & SECURITY MANAGER TEST")
    print("=" * 40)
    
    # Check current status
    status = manager.get_current_status()
    print("Current Status:")
    for key, value in status.items():
        symbol = "❌" if value else "✅"
        print(f"  {symbol} {key}: {value}")
    
    print("\nAvailable fixes:")
    print("1. Disable Secure Boot")
    print("2. Enable Virtualization Features")
    print("3. Fix Firewall Issues")
    print("4. Fix Real-time Protection")
    print("5. Create BIOS Instructions")
    
    choice = input("\nSelect option (1-5): ").strip()
    
    if choice == "1":
        manager.disable_secure_boot()
    elif choice == "2":
        manager.enable_virtualization_software()
    elif choice == "3":
        manager.fix_firewall_issues()
    elif choice == "4":
        manager.fix_realtime_protection()
    elif choice == "5":
        manager.create_bios_instructions()
    
    print("\nOperation completed!")
    input("Press Enter to exit...")
