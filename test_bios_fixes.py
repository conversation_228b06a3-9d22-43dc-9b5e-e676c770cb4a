#!/usr/bin/env python3
"""
Test BIOS Security Fixes
Quick test of the BIOS and security fix functionality
"""

from bios_security_manager import BiosSecurityManager

def main():
    print("🔧 BIOS & SECURITY FIXES TEST")
    print("=" * 50)
    
    manager = BiosSecurityManager()
    
    if not manager.is_admin():
        print("❌ Administrator privileges required!")
        print("Right-click and 'Run as administrator'")
        input("Press Enter to exit...")
        return
    
    print("✅ Administrator privileges confirmed")
    print()
    
    # Check current status
    print("🔍 CHECKING CURRENT STATUS...")
    status = manager.get_current_status()
    
    print("\nCurrent System Status:")
    print("-" * 30)
    
    issues = []
    
    if not status["virtualization_enabled"]:
        print("❌ Virtualization not enabled in BIOS")
        issues.append("virtualization")
    else:
        print("✅ Virtualization is enabled")
    
    if status["secure_boot_enabled"]:
        print("❌ Secure Boot is enabled")
        issues.append("secure_boot")
    else:
        print("✅ Secure Boot is disabled")
    
    if not status["firewall_enabled"]:
        print("❌ Error checking Firewall status")
        issues.append("firewall")
    else:
        print("✅ Firewall is working")
    
    if status["realtime_protection_enabled"]:
        print("❌ Real-Time Protection is enabled")
        issues.append("realtime")
    else:
        print("✅ Real-Time Protection is disabled")
    
    print()
    
    if not issues:
        print("🎉 No issues found! System looks good for gaming tools.")
        input("Press Enter to exit...")
        return
    
    print(f"⚠️ Found {len(issues)} issue(s) that need fixing:")
    print()
    
    # Show available fixes
    print("Available fixes:")
    if "virtualization" in issues:
        print("1. Fix Virtualization (enable Windows features)")
    if "secure_boot" in issues:
        print("2. Fix Secure Boot (disable via software + BIOS instructions)")
    if "firewall" in issues:
        print("3. Fix Firewall (reset and reconfigure)")
    if "realtime" in issues:
        print("4. Fix Real-time Protection (restart and reset Defender)")
    print("5. Create BIOS Instructions")
    print("0. Exit")
    
    choice = input("\nSelect fix to apply (0-5): ").strip()
    
    if choice == "1" and "virtualization" in issues:
        print("\n🖥️ FIXING VIRTUALIZATION...")
        manager.enable_virtualization_software()
        
    elif choice == "2" and "secure_boot" in issues:
        print("\n🔒 FIXING SECURE BOOT...")
        manager.disable_secure_boot()
        
    elif choice == "3" and "firewall" in issues:
        print("\n🔥 FIXING FIREWALL...")
        manager.fix_firewall_issues()
        
    elif choice == "4" and "realtime" in issues:
        print("\n🛡️ FIXING REAL-TIME PROTECTION...")
        manager.fix_realtime_protection()
        
    elif choice == "5":
        print("\n📄 CREATING BIOS INSTRUCTIONS...")
        instructions = manager.create_bios_instructions()
        print(instructions)
        
    elif choice == "0":
        print("Exiting...")
        return
    else:
        print("Invalid choice or fix not applicable.")
    
    print("\n" + "=" * 50)
    print("🎯 OPERATION COMPLETED!")
    
    # Show results
    results = manager.get_results()
    if results:
        print("\nDetailed Results:")
        for result in results[-10:]:  # Show last 10 results
            print(f"   {result}")
    
    print("\n💡 RECOMMENDATIONS:")
    print("• Restart system after making changes")
    print("• Check BIOS_INSTRUCTIONS.txt for manual steps")
    print("• Run this test again after restart to verify fixes")
    print("• Use the main GameBoost Pro interface for full functionality")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
