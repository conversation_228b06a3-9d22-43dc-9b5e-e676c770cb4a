#!/usr/bin/env python3
"""
Test Gaming Mode
Quick test of the gaming mode functionality
"""

from security_toggle import SecurityToggle
import sys

def main():
    print("🎮 GAMING MODE TEST")
    print("=" * 40)
    
    toggle = SecurityToggle()
    
    if not toggle.is_admin():
        print("❌ Administrator privileges required!")
        print("Right-click and 'Run as administrator'")
        input("Press Enter to exit...")
        return
    
    print("✅ Administrator privileges confirmed")
    print("🚀 Starting Gaming Mode test...")
    print()
    
    try:
        result = toggle.enable_gaming_mode()
        
        print("\n" + "=" * 50)
        print("🎯 FINAL RESULT:")
        if result:
            print("✅ GAMING MODE: SUCCESS")
            print("🎮 System ready for gaming tools!")
        else:
            print("⚠️ GAMING MODE: PARTIAL SUCCESS")
            print("💡 Core interference reduced, should work for most tools")
        
        print("\n📋 WHAT WAS ATTEMPTED:")
        for line in toggle.get_results():
            print(f"   {line}")
            
    except Exception as e:
        print(f"❌ Error during Gaming Mode: {str(e)}")
    
    print("\n" + "=" * 50)
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
