@echo off
title DISABLE WINDOWS UPDATE
color 0E

echo.
echo  ==========================================
echo   🔄 DISABLING WINDOWS UPDATE
echo  ==========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  [1/6] Stopping Windows Update services...
    net stop wuauserv >nul 2>&1
    net stop UsoSvc >nul 2>&1
    net stop bits >nul 2>&1
    net stop dosvc >nul 2>&1
    echo  [✓] Services stopped
    
    echo  [2/6] Disabling Windows Update services...
    sc config wuauserv start= disabled >nul 2>&1
    sc config UsoSvc start= disabled >nul 2>&1
    sc config bits start= disabled >nul 2>&1
    sc config dosvc start= disabled >nul 2>&1
    echo  [✓] Services disabled
    
    echo  [3/6] Registry disable - Auto Update...
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" /v NoAutoUpdate /t REG_DWORD /d 1 /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" /v AUOptions /t REG_DWORD /d 1 /f >nul 2>&1
    echo  [✓] Auto Update disabled
    
    echo  [4/6] Registry disable - Update Orchestrator...
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" /v DisableWindowsUpdateAccess /t REG_DWORD /d 1 /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" /v SetDisableUXWUAccess /t REG_DWORD /d 1 /f >nul 2>&1
    echo  [✓] Update Orchestrator disabled
    
    echo  [5/6] Registry disable - Delivery Optimization...
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\DeliveryOptimization" /v DODownloadMode /t REG_DWORD /d 0 /f >nul 2>&1
    echo  [✓] Delivery Optimization disabled
    
    echo  [6/6] Registry disable - Update Notifications...
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" /v SetUpdateNotificationLevel /t REG_DWORD /d 1 /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Microsoft\WindowsUpdate\UX\Settings" /v UxOption /t REG_DWORD /d 1 /f >nul 2>&1
    echo  [✓] Update notifications disabled
    
    echo.
    echo  ==========================================
    echo   ✅ WINDOWS UPDATE COMPLETELY DISABLED!
    echo  ==========================================
    echo.
    echo  [✓] All Windows Update services stopped
    echo  [✓] All Windows Update services disabled
    echo  [✓] Auto Update registry disabled
    echo  [✓] Update Orchestrator disabled
    echo  [✓] Delivery Optimization disabled
    echo  [✓] Update notifications disabled
    echo.
    echo  Windows Update is now completely disabled.
    echo.
    pause
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator.
    echo  Right-click this file and select "Run as administrator"
    echo.
    pause
    
    REM Try to restart with admin privileges
    echo  Attempting to restart with administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
)

exit /b
