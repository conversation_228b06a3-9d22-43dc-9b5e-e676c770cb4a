#!/usr/bin/env python3
"""
Fix All Support Tools Issues
Comprehensive fix for all issues detected by Support Tools.exe
"""

import subprocess
import sys
import os

class SupportToolsFixer:
    def __init__(self):
        self.results = []
        
    def log(self, message):
        """Safe logging with encoding handling"""
        try:
            safe_message = str(message).encode('ascii', 'ignore').decode('ascii')
            print(safe_message)
            self.results.append(safe_message)
        except:
            print("[LOG] Operation completed")
            self.results.append("[LOG] Operation completed")
    
    def run_command(self, command, description):
        """Run command safely"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=30,
                encoding='utf-8',
                errors='ignore'
            )
            if result.returncode == 0:
                self.log(f"[SUCCESS] {description}")
                return True
            else:
                self.log(f"[FAILED] {description}")
                return False
        except Exception as e:
            self.log(f"[ERROR] {description} - {str(e)[:50]}")
            return False
    
    def is_admin(self):
        """Check admin privileges"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def fix_secure_boot(self):
        """Fix Secure Boot issues"""
        self.log("FIXING SECURE BOOT...")
        self.log("=" * 40)
        
        success_count = 0
        
        # Method 1: Registry disable
        commands = [
            ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\SecureBoot\\State" /v UEFISecureBootEnabled /t REG_DWORD /d 0 /f', "Secure Boot Registry 1"),
            ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\SecureBoot" /v SecureBootEnabled /t REG_DWORD /d 0 /f', "Secure Boot Registry 2"),
            ('bcdedit /set {bootmgr} secureboot off', "Boot Manager Secure Boot"),
            ('bcdedit /set {current} secureboot off', "Current Boot Secure Boot"),
        ]
        
        for cmd, desc in commands:
            if self.run_command(cmd, desc):
                success_count += 1
        
        # Create BIOS instructions
        self.create_bios_instructions()
        
        if success_count >= 2:
            self.log("[SUCCESS] Secure Boot fix completed")
            self.log("[INFO] RESTART REQUIRED")
            self.log("[INFO] Check BIOS_INSTRUCTIONS.txt for manual steps")
            return True
        else:
            self.log("[WARNING] Manual BIOS configuration required")
            return False
    
    def fix_virtualization(self):
        """Fix Virtualization issues"""
        self.log("FIXING VIRTUALIZATION...")
        self.log("=" * 40)
        
        success_count = 0
        
        # Enable Windows virtualization features
        commands = [
            ('dism /online /enable-feature /featurename:Microsoft-Hyper-V-All /all /norestart', "Hyper-V Features"),
            ('dism /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart', "VM Platform"),
            ('dism /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart', "WSL Feature"),
            ('bcdedit /set hypervisorlaunchtype auto', "Hypervisor Launch"),
        ]
        
        for cmd, desc in commands:
            if self.run_command(cmd, desc):
                success_count += 1
        
        # Create BIOS instructions
        self.create_bios_instructions()
        
        if success_count >= 2:
            self.log("[SUCCESS] Virtualization fix completed")
            self.log("[INFO] RESTART REQUIRED")
            self.log("[INFO] Check BIOS_INSTRUCTIONS.txt for manual steps")
            return True
        else:
            self.log("[WARNING] Manual BIOS configuration required")
            return False
    
    def fix_firewall(self):
        """Fix Firewall issues"""
        self.log("FIXING FIREWALL...")
        self.log("=" * 40)
        
        success_count = 0
        
        # Reset and configure firewall
        commands = [
            ('netsh advfirewall reset', "Firewall Reset"),
            ('sc stop MpsSvc', "Stop Firewall Service"),
            ('sc start MpsSvc', "Start Firewall Service"),
            ('sc config MpsSvc start= auto', "Firewall Auto Start"),
            ('netsh advfirewall set allprofiles state on', "Enable All Profiles"),
        ]
        
        for cmd, desc in commands:
            if self.run_command(cmd, desc):
                success_count += 1
        
        if success_count >= 3:
            self.log("[SUCCESS] Firewall fix completed")
            return True
        else:
            self.log("[WARNING] Firewall partially fixed")
            return False
    
    def fix_realtime_protection(self):
        """Fix Real-time Protection issues"""
        self.log("FIXING REAL-TIME PROTECTION...")
        self.log("=" * 40)
        
        success_count = 0
        
        # Reset Windows Defender
        commands = [
            ('sc stop WinDefend', "Stop Defender Service"),
            ('sc start WinDefend', "Start Defender Service"),
            ('sc config WinDefend start= auto', "Defender Auto Start"),
            ('powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $false"', "Enable Real-time"),
            ('powershell.exe -Command "Update-MpSignature"', "Update Signatures"),
        ]
        
        for cmd, desc in commands:
            if self.run_command(cmd, desc):
                success_count += 1
        
        if success_count >= 3:
            self.log("[SUCCESS] Real-time Protection fix completed")
            return True
        else:
            self.log("[WARNING] Real-time Protection partially fixed")
            return False
    
    def create_bios_instructions(self):
        """Create BIOS configuration instructions"""
        instructions = """
BIOS CONFIGURATION INSTRUCTIONS
===============================

SECURE BOOT DISABLE:
1. Restart computer and enter BIOS (F2, F12, DEL, or ESC during boot)
2. Navigate to Security or Boot section
3. Find "Secure Boot" option
4. Set to "Disabled"
5. Save and exit BIOS

VIRTUALIZATION ENABLE:
1. In BIOS, go to Advanced or CPU Configuration
2. Find "Intel VT-x" or "AMD-V" or "Virtualization Technology"
3. Set to "Enabled"
4. Save and exit BIOS

MANUFACTURER LOCATIONS:
- ASUS: Advanced > CPU Configuration
- MSI: OC > CPU Features
- Gigabyte: M.I.T. > Advanced CPU Settings
- Dell: Virtualization Support
- HP: System Configuration

AFTER BIOS CHANGES:
1. Save settings and restart
2. Run Support Tools.exe again to verify
3. All checks should now pass
        """
        
        try:
            with open("BIOS_INSTRUCTIONS.txt", "w", encoding='utf-8') as f:
                f.write(instructions)
            self.log("[SUCCESS] BIOS instructions created")
        except:
            self.log("[WARNING] Could not create BIOS instructions file")
    
    def fix_all_issues(self):
        """Fix all issues detected by Support Tools"""
        if not self.is_admin():
            self.log("[ERROR] Administrator privileges required!")
            self.log("Right-click and 'Run as administrator'")
            return False
        
        self.log("SUPPORT TOOLS ISSUE FIXER")
        self.log("=" * 50)
        self.log("[INFO] Administrator privileges confirmed")
        self.log("")
        
        fixes_applied = 0
        total_fixes = 4
        
        # Fix each issue
        self.log("1/4 FIXING SECURE BOOT...")
        if self.fix_secure_boot():
            fixes_applied += 1
        self.log("")
        
        self.log("2/4 FIXING VIRTUALIZATION...")
        if self.fix_virtualization():
            fixes_applied += 1
        self.log("")
        
        self.log("3/4 FIXING FIREWALL...")
        if self.fix_firewall():
            fixes_applied += 1
        self.log("")
        
        self.log("4/4 FIXING REAL-TIME PROTECTION...")
        if self.fix_realtime_protection():
            fixes_applied += 1
        self.log("")
        
        # Summary
        self.log("=" * 50)
        self.log("FIX SUMMARY")
        self.log("=" * 50)
        self.log(f"[RESULT] {fixes_applied}/{total_fixes} fixes completed")
        
        if fixes_applied >= 3:
            self.log("[SUCCESS] Most issues fixed!")
            self.log("[INFO] Run Support Tools.exe to verify")
            self.log("[INFO] RESTART RECOMMENDED")
            return True
        else:
            self.log("[WARNING] Some issues require manual intervention")
            self.log("[INFO] Check BIOS_INSTRUCTIONS.txt")
            return False

def main():
    print("SUPPORT TOOLS ISSUE FIXER")
    print("=" * 50)
    print("This will fix all issues detected by Support Tools.exe")
    print("")
    
    fixer = SupportToolsFixer()
    
    if not fixer.is_admin():
        print("ERROR: Administrator privileges required!")
        print("Right-click this script and 'Run as administrator'")
        input("Press Enter to exit...")
        return
    
    print("Starting comprehensive fix...")
    print("")
    
    success = fixer.fix_all_issues()
    
    print("")
    print("=" * 50)
    print("OPERATION COMPLETED")
    print("=" * 50)
    
    if success:
        print("SUCCESS: Most issues have been fixed!")
        print("")
        print("NEXT STEPS:")
        print("1. RESTART your computer")
        print("2. Run Support Tools.exe again")
        print("3. All checks should now pass")
        print("4. If issues persist, check BIOS_INSTRUCTIONS.txt")
    else:
        print("WARNING: Some issues require manual intervention")
        print("")
        print("MANUAL STEPS REQUIRED:")
        print("1. Check BIOS_INSTRUCTIONS.txt")
        print("2. Configure BIOS settings manually")
        print("3. Restart and test again")
    
    print("")
    restart = input("Would you like to restart now? (y/n): ").lower().strip()
    if restart == 'y':
        print("Restarting in 10 seconds...")
        os.system("shutdown /r /t 10")
    else:
        print("Please restart manually for changes to take effect")
    
    input("Press Enter to exit...")

if __name__ == "__main__":
    main()
