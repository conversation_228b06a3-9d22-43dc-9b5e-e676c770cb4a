#!/usr/bin/env python3
"""
Safety Manager - Prevents factory resets and system corruption
Creates automatic backups and recovery points before any changes
"""

import subprocess
import json
import os
from datetime import datetime

class SafetyManager:
    def __init__(self):
        self.backup_dir = "SAFETY_BACKUPS"
        self.ensure_backup_dir()
        
    def ensure_backup_dir(self):
        """Ensure backup directory exists"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
            
    def create_system_restore_point(self):
        """Create Windows System Restore Point before any changes"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            restore_point_name = f"GamingTool_Backup_{timestamp}"
            
            command = f'''
            Checkpoint-Computer -Description "{restore_point_name}" -RestorePointType "MODIFY_SETTINGS"
            '''
            
            result = subprocess.run(
                ["powershell", "-Command", command],
                capture_output=True,
                text=True,
                check=True
            )
            
            return True, f"System restore point created: {restore_point_name}"
            
        except Exception as e:
            return False, f"Failed to create restore point: {str(e)}"
            
    def backup_registry_keys(self):
        """Backup critical registry keys before modification"""
        backups = {}
        
        # Registry keys we modify
        keys_to_backup = [
            {
                "name": "defender_realtime",
                "path": "HKLM\\SOFTWARE\\Microsoft\\Windows Defender\\Real-Time Protection",
                "file": "defender_realtime_backup.reg"
            },
            {
                "name": "smartscreen",
                "path": "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer",
                "file": "smartscreen_backup.reg"
            },
            {
                "name": "firewall_policy",
                "path": "HKLM\\SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy",
                "file": "firewall_policy_backup.reg"
            }
        ]
        
        for key_info in keys_to_backup:
            try:
                backup_file = os.path.join(self.backup_dir, key_info["file"])
                
                # Export registry key
                command = f'reg export "{key_info["path"]}" "{backup_file}" /y'
                
                result = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    backups[key_info["name"]] = {
                        "status": "success",
                        "file": backup_file,
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    backups[key_info["name"]] = {
                        "status": "failed",
                        "error": result.stderr,
                        "timestamp": datetime.now().isoformat()
                    }
                    
            except Exception as e:
                backups[key_info["name"]] = {
                    "status": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
                
        # Save backup manifest
        manifest_file = os.path.join(self.backup_dir, "backup_manifest.json")
        with open(manifest_file, 'w') as f:
            json.dump(backups, f, indent=2)
            
        return backups
        
    def backup_service_states(self):
        """Backup current service states"""
        try:
            services_to_backup = [
                "MpsSvc",  # Windows Firewall
                "WinDefend",  # Windows Defender
                "WdNisSvc",  # Windows Defender Network Inspection
                "SecurityHealthService"  # Windows Security Service
            ]
            
            service_states = {}
            
            for service in services_to_backup:
                try:
                    # Get service status
                    result = subprocess.run(
                        ["powershell", "-Command", f"Get-Service {service} | Select-Object Name,Status,StartType | ConvertTo-Json"],
                        capture_output=True,
                        text=True
                    )
                    
                    if result.returncode == 0:
                        service_info = json.loads(result.stdout)
                        service_states[service] = service_info
                        
                except Exception as e:
                    service_states[service] = {"error": str(e)}
                    
            # Save service states
            backup_file = os.path.join(self.backup_dir, "service_states_backup.json")
            with open(backup_file, 'w') as f:
                json.dump({
                    "timestamp": datetime.now().isoformat(),
                    "services": service_states
                }, f, indent=2)
                
            return True, service_states
            
        except Exception as e:
            return False, str(e)
            
    def create_emergency_restore_script(self):
        """Create emergency restore script for worst-case scenarios"""
        restore_script = '''@echo off
title EMERGENCY RESTORE - Gaming Tool Safety Recovery
echo.
echo ========================================
echo  EMERGENCY RESTORE - Gaming Tool Safety
echo ========================================
echo.
echo This script will restore your system to safe defaults
echo if the gaming tool caused any issues.
echo.
pause

echo [1/5] Enabling Windows Defender Real-Time Protection...
powershell -Command "Set-MpPreference -DisableRealtimeMonitoring $false"

echo [2/5] Enabling Windows Firewall...
netsh advfirewall set allprofiles state on

echo [3/5] Restoring SmartScreen...
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Explorer" /v SmartScreenEnabled /t REG_SZ /d RequireAdmin /f

echo [4/5] Starting security services...
net start MpsSvc
net start WinDefend
net start WdNisSvc

echo [5/5] Enabling Windows Update...
sc config wuauserv start= auto
sc config UsoSvc start= auto
net start wuauserv
net start UsoSvc

echo.
echo ========================================
echo  EMERGENCY RESTORE COMPLETE!
echo ========================================
echo.
echo Your system security has been restored to safe defaults.
echo All protection features are now enabled.
echo.
pause
'''
        
        script_file = os.path.join(self.backup_dir, "EMERGENCY_RESTORE.bat")
        with open(script_file, 'w') as f:
            f.write(restore_script)
            
        return script_file
        
    def verify_system_integrity(self):
        """Check system integrity before making changes"""
        checks = {}
        
        # Check if Windows is activated
        try:
            result = subprocess.run(
                ["powershell", "-Command", "Get-CimInstance -ClassName SoftwareLicensingProduct | Where-Object {$_.PartialProductKey} | Select-Object LicenseStatus"],
                capture_output=True,
                text=True
            )
            checks["windows_activation"] = "1" in result.stdout
        except:
            checks["windows_activation"] = False
            
        # Check if system files are intact
        try:
            result = subprocess.run(
                ["sfc", "/verifyonly"],
                capture_output=True,
                text=True
            )
            checks["system_files"] = result.returncode == 0
        except:
            checks["system_files"] = False
            
        # Check available disk space (need at least 1GB free)
        try:
            result = subprocess.run(
                ["powershell", "-Command", "Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq 'C:'} | Select-Object FreeSpace"],
                capture_output=True,
                text=True
            )
            free_space = int(result.stdout.split()[-1]) if result.stdout.strip() else 0
            checks["disk_space"] = free_space > 1073741824  # 1GB in bytes
        except:
            checks["disk_space"] = False
            
        return checks
        
    def full_safety_backup(self):
        """Perform complete safety backup before any changes"""
        results = {
            "timestamp": datetime.now().isoformat(),
            "operations": {}
        }
        
        # 1. System integrity check
        results["operations"]["integrity_check"] = self.verify_system_integrity()
        
        # 2. Create restore point
        success, message = self.create_system_restore_point()
        results["operations"]["restore_point"] = {"success": success, "message": message}
        
        # 3. Backup registry keys
        results["operations"]["registry_backup"] = self.backup_registry_keys()
        
        # 4. Backup service states
        success, data = self.backup_service_states()
        results["operations"]["service_backup"] = {"success": success, "data": data}
        
        # 5. Create emergency restore script
        script_file = self.create_emergency_restore_script()
        results["operations"]["emergency_script"] = {"file": script_file}
        
        # Save complete backup log
        log_file = os.path.join(self.backup_dir, f"safety_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        with open(log_file, 'w') as f:
            json.dump(results, f, indent=2)
            
        return results

if __name__ == "__main__":
    safety = SafetyManager()
    results = safety.full_safety_backup()
    print("Safety backup completed!")
    print(json.dumps(results, indent=2))
