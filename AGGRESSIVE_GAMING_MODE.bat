@echo off
title AGGRESSIVE GAMING MODE - UNTIL SUCCESS
color 0C

echo.
echo  ==========================================
echo   ⚔️ AGGRESSIVE GAMING MODE - UNTIL SUCCESS
echo  ==========================================
echo.
echo  This will continue trying different methods
echo  until ALL security features are disabled!
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  [!] AGGRESSIVE MODE: Will not stop until 100%% success!
    echo.
    pause
    
    set attempt=1
    set max_attempts=3
    
    :attempt_loop
    echo.
    echo  ==========================================
    echo   🚀 ATTEMPT %attempt%/%max_attempts%
    echo  ==========================================
    echo.
    
    REM Run the aggressive gaming mode
    python -c "from security_toggle import SecurityToggle; t = SecurityToggle(); result = t.enable_gaming_mode(); print('ATTEMPT RESULT:', 'SUCCESS' if result else 'PARTIAL')"
    
    REM Check if we should continue
    if %attempt% LSS %max_attempts% (
        echo.
        echo  [🔄] Preparing for next attempt with more aggressive methods...
        timeout /t 3 >nul
        set /a attempt+=1
        goto attempt_loop
    )
    
    echo.
    echo  ==========================================
    echo   🎯 AGGRESSIVE MODE COMPLETED
    echo  ==========================================
    echo.
    echo  [✓] Maximum attempts reached
    echo  [✓] Used all available methods
    echo  [✓] System should be gaming-ready
    echo.
    
    echo  💡 FINAL RECOMMENDATIONS:
    echo  • Most gaming tools should now work
    echo  • If issues persist, try manual disable
    echo  • Use Safe Mode to restore security
    echo  • Restart for full effect
    echo.
    
    choice /c YN /m "Would you like to restart now? (Y/N)"
    if errorlevel 2 goto :no_restart
    if errorlevel 1 goto :restart
    
    :restart
    echo  [🔄] Restarting in 10 seconds...
    timeout /t 10
    shutdown /r /t 0
    goto :end
    
    :no_restart
    echo  [⚠️] Please restart manually for full effect
    goto :end
    
    :end
    pause
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator.
    echo  Right-click this file and select "Run as administrator"
    echo.
    pause
    
    REM Try to restart with admin privileges
    echo  Attempting to restart with administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
)

exit /b
