@echo off
title Gaming Tool Setup - Professional Edition
color 0B

echo.
echo  ========================================
echo   GAMING TOOL SETUP - PROFESSIONAL EDITION
echo  ========================================
echo.
echo  Starting professional gaming tool interface...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  Please right-click this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo  [✓] Launching interface...
echo.

REM Launch the professional interface
python gaming_tool_interface.py

if %errorLevel% neq 0 (
    echo.
    echo  [!] Error launching interface
    echo  [i] Trying fallback interface...
    python main.py
)

echo.
echo  Interface closed.
pause
