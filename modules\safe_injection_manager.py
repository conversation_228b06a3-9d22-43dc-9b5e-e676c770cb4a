#!/usr/bin/env python3
"""
Safe Injection Manager
Multi-level injection system with comprehensive recovery mechanisms
"""

import os
import sys
import subprocess
import threading
import time
import json
import shutil
import winreg
from pathlib import Path
import psutil

class SafeInjectionManager:
    def __init__(self):
        self.injection_results = []
        self.safety_level = "MAXIMUM"  # MAXIMUM, HIGH, MEDIUM, LOW
        self.recovery_points = []
        self.injection_active = False
        
        # Create multiple backup locations
        self.primary_backup = Path("C:/GamingToolRecovery")
        self.secondary_backup = Path.home() / "Desktop" / "GamingToolBackup"
        self.tertiary_backup = Path("D:/GamingToolBackup") if Path("D:/").exists() else None
        
        # Initialize backup directories
        for backup_dir in [self.primary_backup, self.secondary_backup, self.tertiary_backup]:
            if backup_dir:
                backup_dir.mkdir(exist_ok=True)
    
    def create_comprehensive_backup(self):
        """Create comprehensive system backup before injection"""
        try:
            self.injection_results.append("🛡️ Creating comprehensive system backup...")
            
            backup_data = {
                "timestamp": time.strftime("%Y-%m-%d_%H-%M-%S"),
                "system_state": {},
                "registry_backups": [],
                "service_states": {},
                "file_backups": [],
                "process_list": []
            }
            
            # 1. Create System Restore Point
            self.injection_results.append("📍 Creating System Restore Point...")
            try:
                ps_command = 'Checkpoint-Computer -Description "Gaming Tool Safe Injection Backup" -RestorePointType "MODIFY_SETTINGS"'
                result = subprocess.run(f'powershell.exe -Command "{ps_command}"', 
                                      shell=True, capture_output=True, text=True, timeout=120)
                if result.returncode == 0:
                    backup_data["system_restore_point"] = True
                    self.injection_results.append("✅ System Restore Point created")
                else:
                    self.injection_results.append("⚠️ System Restore Point failed - continuing with other backups")
            except Exception as e:
                self.injection_results.append(f"⚠️ System Restore Point error: {str(e)}")
            
            # 2. Backup Critical Registry Keys
            self.injection_results.append("📋 Backing up critical registry keys...")
            critical_registry_keys = [
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run", "startup_programs"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows Defender", "windows_defender"),
                (winreg.HKEY_LOCAL_MACHINE, r"SYSTEM\CurrentControlSet\Services", "system_services"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Policies\Microsoft\Windows", "windows_policies"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run", "user_startup")
            ]
            
            for hkey, path, name in critical_registry_keys:
                try:
                    backup_file = self.primary_backup / f"registry_{name}_{backup_data['timestamp']}.reg"
                    reg_command = f'reg export "{self.get_hkey_name(hkey)}\\{path}" "{backup_file}"'
                    result = subprocess.run(reg_command, shell=True, capture_output=True)
                    if result.returncode == 0:
                        backup_data["registry_backups"].append(str(backup_file))
                        self.injection_results.append(f"✅ Backed up {name}")
                        
                        # Create copies in secondary locations
                        if self.secondary_backup:
                            shutil.copy2(backup_file, self.secondary_backup)
                        if self.tertiary_backup:
                            shutil.copy2(backup_file, self.tertiary_backup)
                except Exception as e:
                    self.injection_results.append(f"⚠️ Registry backup error for {name}: {str(e)}")
            
            # 3. Backup Service States
            self.injection_results.append("⚙️ Backing up service states...")
            critical_services = [
                "WinDefend", "MpsSvc", "WdNisSvc", "SecurityHealthService",
                "wuauserv", "BITS", "DiagTrack", "dmwappushservice", "Themes",
                "SysMain", "WSearch", "Spooler", "AudioSrv", "AudioEndpointBuilder"
            ]
            
            for service in critical_services:
                try:
                    result = subprocess.run(f'sc query {service}', shell=True, capture_output=True, text=True)
                    if "RUNNING" in result.stdout:
                        backup_data["service_states"][service] = "running"
                    elif "STOPPED" in result.stdout:
                        backup_data["service_states"][service] = "stopped"
                    else:
                        backup_data["service_states"][service] = "unknown"
                except:
                    backup_data["service_states"][service] = "unknown"
            
            # 4. Backup Critical System Files
            self.injection_results.append("📁 Backing up critical system files...")
            critical_files = [
                "C:/Windows/System32/drivers/etc/hosts",
                "C:/Windows/System32/config/SAM",
                "C:/Windows/System32/config/SYSTEM",
                "C:/Windows/System32/config/SOFTWARE"
            ]
            
            for file_path in critical_files:
                try:
                    source = Path(file_path)
                    if source.exists():
                        dest = self.primary_backup / f"{source.name}_{backup_data['timestamp']}.backup"
                        shutil.copy2(source, dest)
                        backup_data["file_backups"].append(str(dest))
                        self.injection_results.append(f"✅ Backed up {source.name}")
                except Exception as e:
                    self.injection_results.append(f"⚠️ File backup error for {file_path}: {str(e)}")
            
            # 5. Record Current Process List
            backup_data["process_list"] = []
            for proc in psutil.process_iter(['pid', 'name', 'exe']):
                try:
                    backup_data["process_list"].append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'exe': proc.info['exe']
                    })
                except:
                    continue
            
            # 6. Save backup metadata
            for backup_dir in [self.primary_backup, self.secondary_backup, self.tertiary_backup]:
                if backup_dir:
                    metadata_file = backup_dir / f"backup_metadata_{backup_data['timestamp']}.json"
                    with open(metadata_file, 'w') as f:
                        json.dump(backup_data, f, indent=2)
            
            self.recovery_points.append(backup_data)
            self.injection_results.append(f"✅ Comprehensive backup completed - {len(backup_data['registry_backups'])} registry keys backed up")
            
            return backup_data
            
        except Exception as e:
            self.injection_results.append(f"❌ Backup creation error: {str(e)}")
            return None
    
    def get_hkey_name(self, hkey):
        """Convert registry hkey to string name"""
        hkey_names = {
            winreg.HKEY_LOCAL_MACHINE: "HKEY_LOCAL_MACHINE",
            winreg.HKEY_CURRENT_USER: "HKEY_CURRENT_USER",
            winreg.HKEY_CLASSES_ROOT: "HKEY_CLASSES_ROOT",
            winreg.HKEY_USERS: "HKEY_USERS",
            winreg.HKEY_CURRENT_CONFIG: "HKEY_CURRENT_CONFIG"
        }
        return hkey_names.get(hkey, "HKEY_LOCAL_MACHINE")
    
    def create_emergency_recovery_system(self):
        """Create multiple emergency recovery options"""
        try:
            self.injection_results.append("🚨 Creating emergency recovery system...")
            
            # 1. Create Emergency Recovery Script
            recovery_script = f'''@echo off
echo ===============================================
echo GAMING TOOL EMERGENCY RECOVERY SYSTEM
echo ===============================================
echo.
echo This script will restore your system to its previous state
echo.
pause

echo Step 1: Stopping potentially problematic services...
sc stop WinDefend 2>nul
sc stop MpsSvc 2>nul
sc stop WdNisSvc 2>nul

echo Step 2: Restoring Windows Defender...
powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $false" 2>nul
sc config WinDefend start= auto 2>nul
sc start WinDefend 2>nul

echo Step 3: Restoring Windows Firewall...
netsh advfirewall set allprofiles state on 2>nul

echo Step 4: Restoring Windows Update...
sc config wuauserv start= auto 2>nul
sc start wuauserv 2>nul

echo Step 5: Restoring registry backups...
'''
            
            # Add registry restore commands for each backup
            for backup_file in self.recovery_points[-1]["registry_backups"] if self.recovery_points else []:
                recovery_script += f'reg import "{backup_file}" 2>nul\n'
            
            recovery_script += '''
echo Step 6: Restoring services...
sc start Themes 2>nul
sc start SysMain 2>nul
sc start WSearch 2>nul
sc start Spooler 2>nul
sc start AudioSrv 2>nul

echo Step 7: Enabling UAC...
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System" /v EnableLUA /t REG_DWORD /d 1 /f 2>nul

echo Step 8: System file check...
sfc /scannow

echo.
echo ===============================================
echo RECOVERY COMPLETED!
echo ===============================================
echo.
echo Please restart your computer for all changes to take effect.
echo If you still have issues, use System Restore to go back further.
echo.
pause
'''
            
            # Save recovery script to multiple locations
            recovery_locations = [
                self.primary_backup / "EMERGENCY_RECOVERY.bat",
                self.secondary_backup / "EMERGENCY_RECOVERY.bat",
                Path.home() / "Desktop" / "EMERGENCY_RECOVERY_GAMING_TOOL.bat"
            ]
            
            if self.tertiary_backup:
                recovery_locations.append(self.tertiary_backup / "EMERGENCY_RECOVERY.bat")
            
            for location in recovery_locations:
                try:
                    with open(location, 'w') as f:
                        f.write(recovery_script)
                    self.injection_results.append(f"✅ Recovery script created: {location}")
                except Exception as e:
                    self.injection_results.append(f"⚠️ Could not create recovery script at {location}: {str(e)}")
            
            # 2. Create Safe Mode Recovery Instructions
            safe_mode_instructions = f'''
GAMING TOOL SAFE MODE RECOVERY GUIDE
====================================

If your system becomes unresponsive or won't boot normally:

OPTION 1: EMERGENCY RECOVERY SCRIPT
1. Boot into Safe Mode (press F8 during startup)
2. Navigate to Desktop
3. Run "EMERGENCY_RECOVERY_GAMING_TOOL.bat" as Administrator
4. Follow the prompts
5. Restart computer

OPTION 2: SYSTEM RESTORE
1. Boot into Safe Mode
2. Type "rstrui" in Start menu
3. Select restore point from before gaming tool use
4. Follow the wizard
5. Restart computer

OPTION 3: MANUAL REGISTRY RESTORE
1. Boot into Safe Mode
2. Navigate to C:\\GamingToolRecovery\\
3. Double-click each .reg file to restore
4. Restart computer

OPTION 4: WINDOWS RECOVERY ENVIRONMENT
1. Boot from Windows installation media
2. Select "Repair your computer"
3. Choose "System Restore" or "Startup Repair"
4. Follow the recovery wizard

OPTION 5: COMMAND LINE RECOVERY
1. Boot into Safe Mode with Command Prompt
2. Run: sfc /scannow
3. Run: DISM /Online /Cleanup-Image /RestoreHealth
4. Run: powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $false"
5. Run: netsh advfirewall set allprofiles state on
6. Restart computer

BACKUP LOCATIONS:
- Primary: C:\\GamingToolRecovery\\
- Secondary: {self.secondary_backup}
- Desktop: Emergency recovery script

CREATED: {time.strftime("%Y-%m-%d %H:%M:%S")}

REMEMBER: You can ALWAYS use System Restore to go back!
'''
            
            # Save instructions to multiple locations
            instruction_locations = [
                self.primary_backup / "SAFE_MODE_RECOVERY_GUIDE.txt",
                self.secondary_backup / "SAFE_MODE_RECOVERY_GUIDE.txt",
                Path.home() / "Desktop" / "SAFE_MODE_RECOVERY_GUIDE.txt"
            ]
            
            for location in instruction_locations:
                try:
                    with open(location, 'w') as f:
                        f.write(safe_mode_instructions)
                    self.injection_results.append(f"✅ Recovery guide created: {location}")
                except Exception as e:
                    self.injection_results.append(f"⚠️ Could not create recovery guide at {location}: {str(e)}")
            
            # 3. Enable Advanced Boot Options
            try:
                subprocess.run('bcdedit /set {default} bootmenupolicy legacy', shell=True, capture_output=True)
                subprocess.run('bcdedit /timeout 10', shell=True, capture_output=True)
                self.injection_results.append("✅ Advanced boot options enabled (F8 menu)")
            except Exception as e:
                self.injection_results.append(f"⚠️ Could not enable boot options: {str(e)}")
            
            self.injection_results.append("🛡️ Emergency recovery system created successfully!")
            return True
            
        except Exception as e:
            self.injection_results.append(f"❌ Emergency recovery system error: {str(e)}")
            return False
    
    def monitor_system_stability(self):
        """Continuously monitor system stability during injection"""
        stability_issues = 0
        max_issues = 3
        
        while self.injection_active:
            try:
                # Check system responsiveness
                start_time = time.time()
                
                # Test registry access
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion"):
                    pass
                
                # Test file system
                test_file = self.primary_backup / "stability_test.tmp"
                with open(test_file, 'w') as f:
                    f.write("test")
                test_file.unlink()
                
                # Test process execution
                subprocess.run('echo test', shell=True, capture_output=True, timeout=3)
                
                response_time = time.time() - start_time
                
                if response_time > 5.0:
                    stability_issues += 1
                    self.injection_results.append(f"⚠️ System slow response: {response_time:.2f}s (Issue {stability_issues}/{max_issues})")
                    
                    if stability_issues >= max_issues:
                        self.injection_results.append("🚨 STABILITY ALERT: System becoming unresponsive!")
                        self.injection_results.append("🛑 Automatically pausing operations for safety...")
                        self.pause_injection()
                        break
                else:
                    # Reset counter on good response
                    if stability_issues > 0:
                        stability_issues = max(0, stability_issues - 1)
                
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                stability_issues += 1
                self.injection_results.append(f"⚠️ Stability check error: {str(e)} (Issue {stability_issues}/{max_issues})")
                
                if stability_issues >= max_issues:
                    self.injection_results.append("🚨 CRITICAL: System stability compromised!")
                    self.emergency_pause()
                    break
                
                time.sleep(10)  # Longer wait on errors
    
    def pause_injection(self):
        """Pause injection operations safely"""
        self.injection_results.append("⏸️ Pausing injection operations...")
        self.injection_active = False
        
        # Give user options
        self.injection_results.append("🔧 Options:")
        self.injection_results.append("1. Wait for system to stabilize and continue")
        self.injection_results.append("2. Run emergency recovery now")
        self.injection_results.append("3. Use System Restore to go back")
    
    def emergency_pause(self):
        """Emergency pause with immediate recovery options"""
        self.injection_results.append("🚨 EMERGENCY PAUSE ACTIVATED!")
        self.injection_active = False
        
        self.injection_results.append("🛑 System stability compromised - operations halted")
        self.injection_results.append("🔧 Immediate recovery options:")
        self.injection_results.append("1. Run EMERGENCY_RECOVERY.bat from Desktop")
        self.injection_results.append("2. Restart in Safe Mode and run recovery")
        self.injection_results.append("3. Use System Restore immediately")
        self.injection_results.append("4. Contact support with system logs")
    
    def safe_injection_with_monitoring(self, injection_function, *args, **kwargs):
        """Execute injection with comprehensive monitoring"""
        try:
            self.injection_results.append("🛡️ Starting safe injection with monitoring...")
            
            # 1. Create comprehensive backup
            backup_data = self.create_comprehensive_backup()
            if not backup_data:
                self.injection_results.append("❌ Backup failed - aborting injection for safety")
                return False
            
            # 2. Create emergency recovery system
            recovery_success = self.create_emergency_recovery_system()
            if not recovery_success:
                self.injection_results.append("⚠️ Emergency recovery setup incomplete - proceeding with caution")
            
            # 3. Start system monitoring
            self.injection_active = True
            monitor_thread = threading.Thread(target=self.monitor_system_stability)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 4. Execute injection with safety checks
            self.injection_results.append("💉 Beginning safe injection process...")
            
            try:
                result = injection_function(*args, **kwargs)
                
                if result:
                    self.injection_results.append("✅ Injection completed successfully!")
                else:
                    self.injection_results.append("⚠️ Injection completed with warnings")
                
                return result
                
            except Exception as e:
                self.injection_results.append(f"❌ Injection error: {str(e)}")
                self.injection_results.append("🔄 Initiating automatic recovery...")
                self.auto_recover()
                return False
            
            finally:
                # 5. Stop monitoring
                self.injection_active = False
                
                # 6. Final system check
                self.injection_results.append("🔍 Performing final system stability check...")
                if self.final_stability_check():
                    self.injection_results.append("✅ System stable after injection")
                else:
                    self.injection_results.append("⚠️ System instability detected - recovery recommended")
                    self.suggest_recovery()
        
        except Exception as e:
            self.injection_results.append(f"❌ Safe injection system error: {str(e)}")
            self.emergency_pause()
            return False
    
    def final_stability_check(self):
        """Perform comprehensive final stability check"""
        try:
            # Test all critical system functions
            tests = [
                ("Registry Access", self.test_registry_access),
                ("File System", self.test_file_system),
                ("Process Execution", self.test_process_execution),
                ("Service Status", self.test_service_status),
                ("Network Connectivity", self.test_network)
            ]
            
            passed_tests = 0
            for test_name, test_function in tests:
                try:
                    if test_function():
                        self.injection_results.append(f"✅ {test_name}: OK")
                        passed_tests += 1
                    else:
                        self.injection_results.append(f"⚠️ {test_name}: Issues detected")
                except Exception as e:
                    self.injection_results.append(f"❌ {test_name}: Error - {str(e)}")
            
            success_rate = (passed_tests / len(tests)) * 100
            self.injection_results.append(f"📊 System stability: {success_rate:.1f}% ({passed_tests}/{len(tests)} tests passed)")
            
            return success_rate >= 80  # 80% success rate required
            
        except Exception as e:
            self.injection_results.append(f"❌ Stability check error: {str(e)}")
            return False
    
    def test_registry_access(self):
        """Test registry access"""
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion"):
                pass
            return True
        except:
            return False
    
    def test_file_system(self):
        """Test file system operations"""
        try:
            test_file = self.primary_backup / "final_test.tmp"
            with open(test_file, 'w') as f:
                f.write("test")
            test_file.unlink()
            return True
        except:
            return False
    
    def test_process_execution(self):
        """Test process execution"""
        try:
            result = subprocess.run('echo test', shell=True, capture_output=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def test_service_status(self):
        """Test critical service status"""
        try:
            critical_services = ["WinDefend", "MpsSvc", "Themes", "AudioSrv"]
            working_services = 0
            
            for service in critical_services:
                try:
                    result = subprocess.run(f'sc query {service}', shell=True, capture_output=True, text=True)
                    if result.returncode == 0:
                        working_services += 1
                except:
                    continue
            
            return working_services >= len(critical_services) * 0.75  # 75% of services working
        except:
            return False
    
    def test_network(self):
        """Test network connectivity"""
        try:
            import urllib.request
            urllib.request.urlopen('http://www.google.com', timeout=5)
            return True
        except:
            return False
    
    def auto_recover(self):
        """Automatic recovery if injection fails"""
        try:
            self.injection_results.append("🔄 Starting automatic recovery...")
            
            if self.recovery_points:
                latest_backup = self.recovery_points[-1]
                
                # Restore registry backups
                for backup_file in latest_backup.get("registry_backups", []):
                    try:
                        subprocess.run(f'reg import "{backup_file}"', shell=True, capture_output=True)
                        self.injection_results.append(f"✅ Restored {Path(backup_file).name}")
                    except Exception as e:
                        self.injection_results.append(f"⚠️ Could not restore {backup_file}: {str(e)}")
                
                # Restore service states
                for service, state in latest_backup.get("service_states", {}).items():
                    try:
                        if state == "running":
                            subprocess.run(f'sc start {service}', shell=True, capture_output=True)
                        elif state == "stopped":
                            subprocess.run(f'sc stop {service}', shell=True, capture_output=True)
                    except:
                        continue
                
                self.injection_results.append("✅ Automatic recovery completed")
            else:
                self.injection_results.append("⚠️ No recovery points available - use manual recovery")
                
        except Exception as e:
            self.injection_results.append(f"❌ Auto-recovery error: {str(e)}")
    
    def suggest_recovery(self):
        """Suggest recovery options to user"""
        self.injection_results.append("\n🔧 Recovery Options Available:")
        self.injection_results.append("1. 🔄 Run automatic recovery (recommended)")
        self.injection_results.append("2. 🖥️ Run EMERGENCY_RECOVERY.bat from Desktop")
        self.injection_results.append("3. 🔙 Use System Restore to go back")
        self.injection_results.append("4. 🛡️ Boot into Safe Mode and run recovery")
        self.injection_results.append("5. 📞 Contact support with system logs")
        
        self.injection_results.append(f"\n📁 Backup files located at:")
        self.injection_results.append(f"   • {self.primary_backup}")
        self.injection_results.append(f"   • {self.secondary_backup}")
        if self.tertiary_backup:
            self.injection_results.append(f"   • {self.tertiary_backup}")
    
    def get_injection_results(self):
        """Get injection results and logs"""
        return self.injection_results
