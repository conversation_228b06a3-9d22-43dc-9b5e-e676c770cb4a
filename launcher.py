#!/usr/bin/env python3
"""
GameBoost Pro Launcher
Choose between different interface styles
"""

import customtkinter as ctk
from tkinter import messagebox
import subprocess
import sys
import os

class GameBoostLauncher:
    def __init__(self):
        # Set appearance mode
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("dark-blue")
        
        # Create launcher window
        self.root = ctk.CTk()
        self.root.title("GameBoost Pro Launcher")
        self.root.geometry("600x400")
        self.root.resizable(False, False)
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the launcher interface"""
        # Main frame
        self.main_frame = ctk.CTkFrame(self.root, fg_color="#1a1a1a")
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        self.title_label = ctk.CTkLabel(
            self.main_frame,
            text="🎮 GameBoost Pro Launcher",
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color="#00ff88"
        )
        self.title_label.pack(pady=(30, 10))
        
        # Subtitle
        self.subtitle_label = ctk.CTkLabel(
            self.main_frame,
            text="Choose your preferred interface style",
            font=ctk.CTkFont(size=14),
            text_color="#888888"
        )
        self.subtitle_label.pack(pady=(0, 40))
        
        # Interface options
        self.options_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        self.options_frame.pack(fill="both", expand=True, padx=40)
        
        # Gaming Interface (New)
        self.gaming_interface_frame = ctk.CTkFrame(self.options_frame, fg_color="#0d4f3c")
        self.gaming_interface_frame.pack(fill="x", pady=(0, 20))
        
        self.gaming_interface_button = ctk.CTkButton(
            self.gaming_interface_frame,
            text="🎯 GAMING INTERFACE\n(New & Improved)",
            command=self.launch_gaming_interface,
            font=ctk.CTkFont(size=18, weight="bold"),
            height=80,
            fg_color="#00ff88",
            hover_color="#00cc6a",
            text_color="#000000"
        )
        self.gaming_interface_button.pack(fill="x", padx=20, pady=20)
        
        self.gaming_description = ctk.CTkLabel(
            self.gaming_interface_frame,
            text="✅ Modern gaming-style design\n✅ Split-screen layout with large log area\n✅ All buttons fully functional\n✅ Professional appearance",
            font=ctk.CTkFont(size=12),
            text_color="#ffffff",
            justify="left"
        )
        self.gaming_description.pack(pady=(0, 20))
        
        # Original Interface
        self.original_interface_frame = ctk.CTkFrame(self.options_frame, fg_color="#2a2a2a")
        self.original_interface_frame.pack(fill="x", pady=(0, 20))
        
        self.original_interface_button = ctk.CTkButton(
            self.original_interface_frame,
            text="🔧 ORIGINAL INTERFACE\n(Classic)",
            command=self.launch_original_interface,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=60,
            fg_color="#4488ff",
            hover_color="#66aaff"
        )
        self.original_interface_button.pack(fill="x", padx=20, pady=20)
        
        self.original_description = ctk.CTkLabel(
            self.original_interface_frame,
            text="✅ Original compact design\n✅ All features available\n✅ Familiar layout",
            font=ctk.CTkFont(size=12),
            text_color="#cccccc",
            justify="left"
        )
        self.original_description.pack(pady=(0, 20))
        
        # One-Click Fix (Direct)
        self.oneclick_frame = ctk.CTkFrame(self.options_frame, fg_color="#4a2c2a")
        self.oneclick_frame.pack(fill="x")
        
        self.oneclick_button = ctk.CTkButton(
            self.oneclick_frame,
            text="🎯 ONE-CLICK FIX ONLY\n(Support Tools Issues)",
            command=self.run_oneclick_fix,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=60,
            fg_color="#ff6644",
            hover_color="#ff8866"
        )
        self.oneclick_button.pack(fill="x", padx=20, pady=20)
        
        self.oneclick_description = ctk.CTkLabel(
            self.oneclick_frame,
            text="✅ Direct fix without interface\n✅ Fastest option for Support Tools issues",
            font=ctk.CTkFont(size=12),
            text_color="#cccccc",
            justify="left"
        )
        self.oneclick_description.pack(pady=(0, 20))
        
    def launch_gaming_interface(self):
        """Launch the new gaming interface"""
        try:
            self.root.destroy()
            subprocess.run([sys.executable, "gaming_interface.py"], cwd=os.getcwd())
        except Exception as e:
            messagebox.showerror("Launch Error", f"Error launching gaming interface:\n{e}")
            
    def launch_original_interface(self):
        """Launch the original interface"""
        try:
            self.root.destroy()
            subprocess.run([sys.executable, "main.py"], cwd=os.getcwd())
        except Exception as e:
            messagebox.showerror("Launch Error", f"Error launching original interface:\n{e}")
            
    def run_oneclick_fix(self):
        """Run the one-click fix directly"""
        try:
            result = messagebox.askyesno(
                "One-Click Fix",
                "🎯 RUN ONE-CLICK FIX?\n\n" +
                "This will run the batch file to fix\n" +
                "all Support Tools issues directly.\n\n" +
                "Continue?"
            )
            
            if result:
                self.root.destroy()
                subprocess.run("ONE_CLICK_FIX_EVERYTHING.bat", shell=True, cwd=os.getcwd())
        except Exception as e:
            messagebox.showerror("Fix Error", f"Error running one-click fix:\n{e}")
    
    def run(self):
        """Run the launcher"""
        self.root.mainloop()

def main():
    """Main function"""
    try:
        launcher = GameBoostLauncher()
        launcher.run()
    except Exception as e:
        print(f"Error starting launcher: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
