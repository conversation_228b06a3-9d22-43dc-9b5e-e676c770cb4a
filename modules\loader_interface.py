#!/usr/bin/env python3
"""
Loader Interface Module
Create secure loader interface with key authentication and spoofing options
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, filedialog
import os
import sys
import subprocess
import threading
import time
import hashlib
import json
from pathlib import Path
import requests

class LoaderInterface:
    def __init__(self):
        self.loader_window = None
        self.authenticated = False
        self.user_key = ""
        self.loader_results = []
        
        # Loader configuration
        self.config = {
            "menu_key": "INSERT",
            "auto_inject": True,
            "spoof_enabled": False,
            "loader_path": "",
            "game_path": "",
            "injection_method": "manual"
        }
        
        # Load saved configuration
        self.load_config()
    
    def load_config(self):
        """Load loader configuration from file"""
        try:
            config_path = Path("loader_config.json")
            if config_path.exists():
                with open(config_path, 'r') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
        except Exception as e:
            print(f"Could not load config: {e}")
    
    def save_config(self):
        """Save loader configuration to file"""
        try:
            config_path = Path("loader_config.json")
            with open(config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Could not save config: {e}")
    
    def validate_key(self, key):
        """Validate user key (placeholder implementation)"""
        # In a real implementation, this would validate against a server
        # For demo purposes, we'll accept any key that looks valid
        if len(key) >= 16 and '-' in key:
            return True
        return False
    
    def authenticate_user(self, key):
        """Authenticate user with provided key"""
        self.loader_results.append(f"🔐 Validating key: {key[:8]}...")
        
        if self.validate_key(key):
            self.authenticated = True
            self.user_key = key
            self.loader_results.append("✅ Authentication successful!")
            return True
        else:
            self.loader_results.append("❌ Invalid key!")
            return False
    
    def show_interface(self):
        """Show the loader interface window"""
        if self.loader_window is not None:
            self.loader_window.lift()
            return
        
        # Create loader window
        self.loader_window = ctk.CTkToplevel()
        self.loader_window.title("Gaming Tool Loader")
        self.loader_window.geometry("800x600")
        self.loader_window.resizable(True, True)
        
        # Make window stay on top
        self.loader_window.attributes("-topmost", True)
        
        # Setup loader UI
        self.setup_loader_ui()
        
        # Handle window close
        self.loader_window.protocol("WM_DELETE_WINDOW", self.close_loader)
    
    def setup_loader_ui(self):
        """Setup the loader user interface"""
        # Main container
        main_frame = ctk.CTkFrame(self.loader_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="Gaming Tool Loader",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # Authentication section
        auth_frame = ctk.CTkFrame(main_frame)
        auth_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        auth_label = ctk.CTkLabel(auth_frame, text="Authentication", font=ctk.CTkFont(size=16, weight="bold"))
        auth_label.pack(pady=(10, 5))
        
        # Key input
        key_frame = ctk.CTkFrame(auth_frame)
        key_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(key_frame, text="License Key:").pack(side="left", padx=(10, 5), pady=10)
        
        self.key_entry = ctk.CTkEntry(key_frame, placeholder_text="Enter your license key", width=300)
        self.key_entry.pack(side="left", padx=5, pady=10)
        
        self.auth_button = ctk.CTkButton(
            key_frame,
            text="Authenticate",
            command=self.handle_authentication,
            width=100
        )
        self.auth_button.pack(side="left", padx=(5, 10), pady=10)
        
        # Status indicator
        self.auth_status = ctk.CTkLabel(auth_frame, text="⚠️ Not Authenticated", text_color="orange")
        self.auth_status.pack(pady=(0, 10))
        
        # Configuration section
        config_frame = ctk.CTkFrame(main_frame)
        config_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        config_label = ctk.CTkLabel(config_frame, text="Configuration", font=ctk.CTkFont(size=16, weight="bold"))
        config_label.pack(pady=(10, 5))
        
        # Menu key setting
        menu_key_frame = ctk.CTkFrame(config_frame)
        menu_key_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(menu_key_frame, text="Menu Key:").pack(side="left", padx=(10, 5), pady=10)
        
        self.menu_key_var = ctk.StringVar(value=self.config["menu_key"])
        self.menu_key_entry = ctk.CTkEntry(menu_key_frame, textvariable=self.menu_key_var, width=100)
        self.menu_key_entry.pack(side="left", padx=5, pady=10)
        
        # Spoofing option
        spoof_frame = ctk.CTkFrame(config_frame)
        spoof_frame.pack(fill="x", padx=10, pady=5)
        
        self.spoof_var = ctk.BooleanVar(value=self.config["spoof_enabled"])
        self.spoof_checkbox = ctk.CTkCheckBox(
            spoof_frame,
            text="Enable HWID Spoofing",
            variable=self.spoof_var,
            command=self.update_spoof_setting
        )
        self.spoof_checkbox.pack(side="left", padx=10, pady=10)
        
        # Auto-inject option
        auto_inject_frame = ctk.CTkFrame(config_frame)
        auto_inject_frame.pack(fill="x", padx=10, pady=5)
        
        self.auto_inject_var = ctk.BooleanVar(value=self.config["auto_inject"])
        self.auto_inject_checkbox = ctk.CTkCheckBox(
            auto_inject_frame,
            text="Auto-inject on game start",
            variable=self.auto_inject_var,
            command=self.update_auto_inject_setting
        )
        self.auto_inject_checkbox.pack(side="left", padx=10, pady=10)
        
        # File selection section
        files_frame = ctk.CTkFrame(main_frame)
        files_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        files_label = ctk.CTkLabel(files_frame, text="File Selection", font=ctk.CTkFont(size=16, weight="bold"))
        files_label.pack(pady=(10, 5))
        
        # Loader file selection
        loader_file_frame = ctk.CTkFrame(files_frame)
        loader_file_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(loader_file_frame, text="Loader File:").pack(side="left", padx=(10, 5), pady=10)
        
        self.loader_path_var = ctk.StringVar(value=self.config["loader_path"])
        self.loader_path_entry = ctk.CTkEntry(loader_file_frame, textvariable=self.loader_path_var, width=300)
        self.loader_path_entry.pack(side="left", padx=5, pady=10)
        
        self.browse_loader_button = ctk.CTkButton(
            loader_file_frame,
            text="Browse",
            command=self.browse_loader_file,
            width=80
        )
        self.browse_loader_button.pack(side="left", padx=(5, 10), pady=10)
        
        # Control buttons
        control_frame = ctk.CTkFrame(main_frame)
        control_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.inject_button = ctk.CTkButton(
            control_frame,
            text="Inject",
            command=self.start_injection,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=40,
            state="disabled"
        )
        self.inject_button.pack(side="left", padx=(20, 10), pady=10)
        
        self.spoof_button = ctk.CTkButton(
            control_frame,
            text="Spoof HWID",
            command=self.start_spoofing,
            height=40,
            state="disabled"
        )
        self.spoof_button.pack(side="left", padx=10, pady=10)
        
        self.save_config_button = ctk.CTkButton(
            control_frame,
            text="Save Config",
            command=self.save_config,
            height=40
        )
        self.save_config_button.pack(side="right", padx=(10, 20), pady=10)
        
        # Status/Log area
        status_frame = ctk.CTkFrame(main_frame)
        status_frame.pack(fill="both", expand=True, padx=20)
        
        status_label = ctk.CTkLabel(status_frame, text="Status Log", font=ctk.CTkFont(size=16, weight="bold"))
        status_label.pack(pady=(10, 5))
        
        self.status_text = ctk.CTkTextbox(status_frame, height=150)
        self.status_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # Initial status
        self.log_message("Loader interface ready")
        self.log_message("Please authenticate with your license key")
    
    def log_message(self, message):
        """Add message to status log"""
        timestamp = time.strftime("%H:%M:%S")
        self.status_text.insert("end", f"[{timestamp}] {message}\n")
        self.status_text.see("end")
        self.loader_results.append(message)
    
    def handle_authentication(self):
        """Handle user authentication"""
        key = self.key_entry.get().strip()
        if not key:
            messagebox.showerror("Error", "Please enter a license key")
            return
        
        self.log_message(f"Authenticating key: {key[:8]}...")
        
        # Run authentication in separate thread
        auth_thread = threading.Thread(target=self.authenticate_async, args=(key,))
        auth_thread.daemon = True
        auth_thread.start()
    
    def authenticate_async(self, key):
        """Authenticate user asynchronously"""
        if self.authenticate_user(key):
            # Update UI on successful authentication
            self.loader_window.after(0, self.on_authentication_success)
        else:
            # Update UI on failed authentication
            self.loader_window.after(0, self.on_authentication_failure)
    
    def on_authentication_success(self):
        """Handle successful authentication"""
        self.auth_status.configure(text="✅ Authenticated", text_color="green")
        self.inject_button.configure(state="normal")
        self.spoof_button.configure(state="normal")
        self.log_message("Authentication successful! Loader ready.")
    
    def on_authentication_failure(self):
        """Handle failed authentication"""
        self.auth_status.configure(text="❌ Authentication Failed", text_color="red")
        messagebox.showerror("Authentication Failed", "Invalid license key!")
    
    def update_spoof_setting(self):
        """Update spoofing setting"""
        self.config["spoof_enabled"] = self.spoof_var.get()
        self.log_message(f"HWID Spoofing: {'Enabled' if self.config['spoof_enabled'] else 'Disabled'}")
    
    def update_auto_inject_setting(self):
        """Update auto-inject setting"""
        self.config["auto_inject"] = self.auto_inject_var.get()
        self.log_message(f"Auto-inject: {'Enabled' if self.config['auto_inject'] else 'Disabled'}")
    
    def browse_loader_file(self):
        """Browse for loader file"""
        file_path = filedialog.askopenfilename(
            title="Select Loader File",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        
        if file_path:
            self.loader_path_var.set(file_path)
            self.config["loader_path"] = file_path
            self.log_message(f"Loader file selected: {Path(file_path).name}")
    
    def start_injection(self):
        """Start the injection process"""
        if not self.authenticated:
            messagebox.showerror("Error", "Please authenticate first!")
            return
        
        loader_path = self.loader_path_var.get().strip()
        if not loader_path or not Path(loader_path).exists():
            messagebox.showerror("Error", "Please select a valid loader file!")
            return
        
        self.log_message("Starting injection process...")
        
        # Run injection in separate thread
        inject_thread = threading.Thread(target=self.inject_async)
        inject_thread.daemon = True
        inject_thread.start()
    
    def inject_async(self):
        """Perform injection asynchronously"""
        try:
            loader_path = self.config["loader_path"]
            
            self.loader_window.after(0, lambda: self.log_message("Executing loader..."))
            
            # Execute the loader
            result = subprocess.run(
                [loader_path],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                self.loader_window.after(0, lambda: self.log_message("✅ Injection successful!"))
                self.loader_window.after(0, lambda: self.log_message("Game integration ready. Launch your game now."))
            else:
                self.loader_window.after(0, lambda: self.log_message(f"❌ Injection failed (exit code: {result.returncode})"))
                
        except subprocess.TimeoutExpired:
            self.loader_window.after(0, lambda: self.log_message("❌ Injection timed out"))
        except Exception as e:
            self.loader_window.after(0, lambda: self.log_message(f"❌ Injection error: {str(e)}"))
    
    def start_spoofing(self):
        """Start HWID spoofing"""
        if not self.authenticated:
            messagebox.showerror("Error", "Please authenticate first!")
            return
        
        if not self.config["spoof_enabled"]:
            messagebox.showwarning("Warning", "HWID Spoofing is disabled in settings!")
            return
        
        self.log_message("Starting HWID spoofing...")
        
        # Run spoofing in separate thread
        spoof_thread = threading.Thread(target=self.spoof_async)
        spoof_thread.daemon = True
        spoof_thread.start()
    
    def spoof_async(self):
        """Perform HWID spoofing asynchronously"""
        try:
            # Simulate spoofing process
            self.loader_window.after(0, lambda: self.log_message("Generating new hardware IDs..."))
            time.sleep(2)
            
            self.loader_window.after(0, lambda: self.log_message("Applying HWID changes..."))
            time.sleep(1)
            
            self.loader_window.after(0, lambda: self.log_message("✅ HWID spoofing completed!"))
            self.loader_window.after(0, lambda: self.log_message("⚠️ Restart required for changes to take effect"))
            
        except Exception as e:
            self.loader_window.after(0, lambda: self.log_message(f"❌ Spoofing error: {str(e)}"))
    
    def close_loader(self):
        """Close the loader window"""
        self.save_config()
        self.loader_window.destroy()
        self.loader_window = None
    
    def get_loader_results(self):
        """Get the results of loader operations"""
        return self.loader_results
