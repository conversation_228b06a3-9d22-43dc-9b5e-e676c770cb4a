#!/usr/bin/env python3
"""
Simple test to check if GUI works
"""

try:
    import tkinter as tk
    print("tkinter imported successfully")
    
    root = tk.Tk()
    root.title("Test GUI")
    root.geometry("400x200")
    
    label = tk.Label(root, text="GUI Test - If you see this, tkinter works!")
    label.pack(pady=50)
    
    button = tk.Button(root, text="Close", command=root.quit)
    button.pack(pady=10)
    
    print("GUI created, starting mainloop...")
    root.mainloop()
    print("GUI closed")
    
except Exception as e:
    print(f"Error: {e}")
    input("Press Enter to continue...")
