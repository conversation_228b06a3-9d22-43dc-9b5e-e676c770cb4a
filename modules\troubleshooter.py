#!/usr/bin/env python3
"""
Troubleshooting System Module
Implement automated fixes for common issues like menu closing problems and crashes
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import os
import sys
import subprocess
import shutil
import threading
import time
from pathlib import Path
import winreg

class Troubleshooter:
    def __init__(self):
        self.troubleshoot_window = None
        self.troubleshoot_results = []
        
        # Common issues and their fixes
        self.common_issues = {
            "menu_wont_close": {
                "name": "Unable to close menu",
                "description": "<PERSON><PERSON> stays open and won't close",
                "fix_function": self.fix_menu_close_issue,
                "severity": "Medium"
            },
            "game_crashes": {
                "name": "Game crashes on injection",
                "description": "Game crashes when injecting or after injection",
                "fix_function": self.fix_game_crashes,
                "severity": "High"
            },
            "injection_fails": {
                "name": "Injection fails",
                "description": "Loader fails to inject into game process",
                "fix_function": self.fix_injection_failure,
                "severity": "High"
            },
            "menu_not_appearing": {
                "name": "<PERSON>u not appearing",
                "description": "<PERSON><PERSON> doesn't show up after injection",
                "fix_function": self.fix_menu_not_appearing,
                "severity": "Medium"
            },
            "performance_issues": {
                "name": "Performance issues",
                "description": "Low FPS or stuttering after injection",
                "fix_function": self.fix_performance_issues,
                "severity": "Low"
            },
            "antivirus_blocking": {
                "name": "Antivirus blocking",
                "description": "Antivirus software blocking the loader",
                "fix_function": self.fix_antivirus_blocking,
                "severity": "High"
            }
        }
    
    def show_interface(self):
        """Show the troubleshooter interface window"""
        if self.troubleshoot_window is not None:
            self.troubleshoot_window.lift()
            return
        
        # Create troubleshooter window
        self.troubleshoot_window = ctk.CTkToplevel()
        self.troubleshoot_window.title("Gaming Tool Troubleshooter")
        self.troubleshoot_window.geometry("900x700")
        self.troubleshoot_window.resizable(True, True)
        
        # Setup troubleshooter UI
        self.setup_troubleshooter_ui()
        
        # Handle window close
        self.troubleshoot_window.protocol("WM_DELETE_WINDOW", self.close_troubleshooter)
    
    def setup_troubleshooter_ui(self):
        """Setup the troubleshooter user interface"""
        # Main container
        main_frame = ctk.CTkFrame(self.troubleshoot_window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="Gaming Tool Troubleshooter",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # Quick fixes section
        quick_fixes_frame = ctk.CTkFrame(main_frame)
        quick_fixes_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        quick_fixes_label = ctk.CTkLabel(
            quick_fixes_frame,
            text="Quick Fixes",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        quick_fixes_label.pack(pady=(10, 5))
        
        # Quick fix buttons
        quick_fix_buttons_frame = ctk.CTkFrame(quick_fixes_frame)
        quick_fix_buttons_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        self.auto_fix_button = ctk.CTkButton(
            quick_fix_buttons_frame,
            text="Auto Fix All Issues",
            command=self.auto_fix_all,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=35
        )
        self.auto_fix_button.pack(side="left", padx=(10, 5), pady=10)
        
        self.system_scan_button = ctk.CTkButton(
            quick_fix_buttons_frame,
            text="System Scan",
            command=self.run_system_scan,
            height=35
        )
        self.system_scan_button.pack(side="left", padx=5, pady=10)
        
        self.reset_config_button = ctk.CTkButton(
            quick_fix_buttons_frame,
            text="Reset Configuration",
            command=self.reset_configuration,
            height=35
        )
        self.reset_config_button.pack(side="left", padx=5, pady=10)
        
        # Common issues section
        issues_frame = ctk.CTkScrollableFrame(main_frame, height=300)
        issues_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        issues_label = ctk.CTkLabel(
            issues_frame,
            text="Common Issues",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        issues_label.pack(pady=(10, 10))
        
        # Create issue buttons
        self.create_issue_buttons(issues_frame)
        
        # Status/Log area
        status_frame = ctk.CTkFrame(main_frame)
        status_frame.pack(fill="x", padx=20)
        
        status_label = ctk.CTkLabel(
            status_frame,
            text="Troubleshooting Log",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_label.pack(pady=(10, 5))
        
        self.status_text = ctk.CTkTextbox(status_frame, height=150)
        self.status_text.pack(fill="x", padx=10, pady=(0, 10))
        
        # Initial message
        self.log_message("Troubleshooter ready. Select an issue to fix or run auto-fix.")
    
    def create_issue_buttons(self, parent):
        """Create buttons for common issues"""
        for issue_id, issue_info in self.common_issues.items():
            issue_frame = ctk.CTkFrame(parent)
            issue_frame.pack(fill="x", padx=10, pady=5)
            
            # Severity indicator
            severity_colors = {"High": "red", "Medium": "orange", "Low": "green"}
            severity_color = severity_colors.get(issue_info["severity"], "gray")
            
            severity_label = ctk.CTkLabel(
                issue_frame,
                text=f"[{issue_info['severity']}]",
                text_color=severity_color,
                width=80
            )
            severity_label.pack(side="left", padx=(10, 5), pady=10)
            
            # Issue info
            info_frame = ctk.CTkFrame(issue_frame)
            info_frame.pack(side="left", fill="x", expand=True, padx=5, pady=5)
            
            name_label = ctk.CTkLabel(
                info_frame,
                text=issue_info["name"],
                font=ctk.CTkFont(weight="bold")
            )
            name_label.pack(anchor="w", padx=10, pady=(5, 0))
            
            desc_label = ctk.CTkLabel(
                info_frame,
                text=issue_info["description"],
                font=ctk.CTkFont(size=12)
            )
            desc_label.pack(anchor="w", padx=10, pady=(0, 5))
            
            # Fix button
            fix_button = ctk.CTkButton(
                issue_frame,
                text="Fix",
                command=lambda f=issue_info["fix_function"]: self.run_fix(f),
                width=80,
                height=35
            )
            fix_button.pack(side="right", padx=10, pady=10)
    
    def log_message(self, message):
        """Add message to troubleshooting log"""
        timestamp = time.strftime("%H:%M:%S")
        self.status_text.insert("end", f"[{timestamp}] {message}\n")
        self.status_text.see("end")
        self.troubleshoot_results.append(message)
        
        if self.troubleshoot_window:
            self.troubleshoot_window.update()
    
    def run_fix(self, fix_function):
        """Run a specific fix function"""
        self.log_message(f"Running fix: {fix_function.__name__}")
        
        # Run fix in separate thread
        fix_thread = threading.Thread(target=fix_function)
        fix_thread.daemon = True
        fix_thread.start()
    
    def fix_menu_close_issue(self):
        """Fix: Unable to close menu"""
        self.log_message("🔧 Fixing menu close issue...")
        
        try:
            # Delete COD players folder
            cod_documents = Path.home() / "Documents" / "Call of Duty"
            if cod_documents.exists():
                players_folder = cod_documents / "players"
                if players_folder.exists():
                    shutil.rmtree(players_folder)
                    self.log_message("✅ Deleted COD players folder")
                else:
                    self.log_message("⚠️ COD players folder not found")
            
            # Also check other possible locations
            other_locations = [
                Path.home() / "Documents" / "COD",
                Path.home() / "Documents" / "Call of Duty Black Ops 6",
                Path.home() / "Documents" / "Call of Duty Modern Warfare"
            ]
            
            for location in other_locations:
                if location.exists():
                    players_folder = location / "players"
                    if players_folder.exists():
                        shutil.rmtree(players_folder)
                        self.log_message(f"✅ Deleted players folder at {location}")
            
            self.log_message("✅ Menu close issue fix completed")
            self.log_message("⚠️ Please restart your PC and reinject")
            
        except Exception as e:
            self.log_message(f"❌ Error fixing menu close issue: {str(e)}")
    
    def fix_game_crashes(self):
        """Fix: Game crashes on injection"""
        self.log_message("🔧 Fixing game crash issues...")
        
        try:
            # Delete STZ file from C drive
            stz_files = [
                Path("C:/STZ"),
                Path("C:/STZ.dat"),
                Path("C:/STZ.tmp")
            ]
            
            deleted_count = 0
            for stz_file in stz_files:
                if stz_file.exists():
                    try:
                        if stz_file.is_file():
                            stz_file.unlink()
                        else:
                            shutil.rmtree(stz_file)
                        deleted_count += 1
                        self.log_message(f"✅ Deleted {stz_file}")
                    except Exception as e:
                        self.log_message(f"❌ Could not delete {stz_file}: {str(e)}")
            
            if deleted_count == 0:
                self.log_message("⚠️ No STZ files found")
            
            # Clear game cache directories
            cache_dirs = [
                Path.home() / "AppData" / "Local" / "Call of Duty",
                Path.home() / "AppData" / "Roaming" / "Call of Duty",
                Path("C:/ProgramData/Call of Duty")
            ]
            
            for cache_dir in cache_dirs:
                if cache_dir.exists():
                    try:
                        # Clear cache but keep the directory
                        for item in cache_dir.iterdir():
                            if item.is_file():
                                item.unlink()
                            elif item.is_dir() and item.name.lower() in ["cache", "temp", "logs"]:
                                shutil.rmtree(item)
                        self.log_message(f"✅ Cleared cache at {cache_dir}")
                    except Exception as e:
                        self.log_message(f"❌ Could not clear cache at {cache_dir}: {str(e)}")
            
            self.log_message("✅ Game crash fix completed")
            self.log_message("⚠️ Please restart your PC and reinject")
            
        except Exception as e:
            self.log_message(f"❌ Error fixing game crashes: {str(e)}")
    
    def fix_injection_failure(self):
        """Fix: Injection fails"""
        self.log_message("🔧 Fixing injection failure...")
        
        try:
            # Check if antivirus is blocking
            self.log_message("Checking for antivirus interference...")
            
            # Disable Windows Defender real-time protection temporarily
            ps_command = 'Set-MpPreference -DisableRealtimeMonitoring $true'
            result = subprocess.run(
                f'powershell.exe -Command "{ps_command}"',
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                self.log_message("✅ Temporarily disabled Windows Defender")
            else:
                self.log_message("⚠️ Could not disable Windows Defender")
            
            # Check for running processes that might interfere
            interfering_processes = [
                "vgtray.exe", "vgc.exe", "BEService.exe", "EasyAntiCheat.exe",
                "FACEITService.exe", "ESEADriver2.sys"
            ]
            
            terminated_count = 0
            for proc_name in interfering_processes:
                try:
                    result = subprocess.run(f'taskkill /f /im {proc_name}', 
                                          shell=True, capture_output=True)
                    if result.returncode == 0:
                        terminated_count += 1
                        self.log_message(f"✅ Terminated {proc_name}")
                except:
                    pass
            
            if terminated_count > 0:
                self.log_message(f"✅ Terminated {terminated_count} interfering processes")
            else:
                self.log_message("⚠️ No interfering processes found")
            
            self.log_message("✅ Injection failure fix completed")
            
        except Exception as e:
            self.log_message(f"❌ Error fixing injection failure: {str(e)}")
    
    def fix_menu_not_appearing(self):
        """Fix: Menu not appearing"""
        self.log_message("🔧 Fixing menu not appearing issue...")
        
        try:
            # Check if INSERT key is working
            self.log_message("Checking menu key configuration...")
            
            # Reset game overlay settings
            overlay_registry_paths = [
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\GameDVR"),
                (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\GameBar")
            ]
            
            for hkey, path in overlay_registry_paths:
                try:
                    with winreg.OpenKey(hkey, path, 0, winreg.KEY_SET_VALUE) as key:
                        winreg.SetValueEx(key, "GameDVR_Enabled", 0, winreg.REG_DWORD, 0)
                        winreg.SetValueEx(key, "AppCaptureEnabled", 0, winreg.REG_DWORD, 0)
                    self.log_message(f"✅ Disabled overlay at {path}")
                except:
                    pass
            
            # Check game display settings
            self.log_message("Checking game display settings...")
            self.log_message("⚠️ Ensure game is running in Fullscreen Borderless mode")
            self.log_message("⚠️ Try pressing INSERT key multiple times")
            
            self.log_message("✅ Menu not appearing fix completed")
            
        except Exception as e:
            self.log_message(f"❌ Error fixing menu not appearing: {str(e)}")
    
    def fix_performance_issues(self):
        """Fix: Performance issues"""
        self.log_message("🔧 Fixing performance issues...")
        
        try:
            # Set high priority for game processes
            game_processes = ["BlackOps6.exe", "Warzone.exe", "ModernWarfare.exe", "cod.exe"]
            
            for proc_name in game_processes:
                try:
                    result = subprocess.run(
                        f'wmic process where name="{proc_name}" CALL setpriority "high priority"',
                        shell=True,
                        capture_output=True
                    )
                    if result.returncode == 0:
                        self.log_message(f"✅ Set high priority for {proc_name}")
                except:
                    pass
            
            # Optimize system for gaming
            self.log_message("Optimizing system for gaming...")
            
            # Disable unnecessary services temporarily
            services_to_stop = ["Themes", "Fax", "TabletInputService"]
            
            for service in services_to_stop:
                try:
                    subprocess.run(f'sc stop {service}', shell=True, capture_output=True)
                    self.log_message(f"✅ Stopped {service} service")
                except:
                    pass
            
            self.log_message("✅ Performance optimization completed")
            
        except Exception as e:
            self.log_message(f"❌ Error fixing performance issues: {str(e)}")
    
    def fix_antivirus_blocking(self):
        """Fix: Antivirus blocking"""
        self.log_message("🔧 Fixing antivirus blocking...")
        
        try:
            # Add exclusions to Windows Defender
            exclusion_paths = [
                "C:\\GamingTools",
                "C:\\Games",
                str(Path.home() / "Desktop"),
                str(Path.home() / "Downloads")
            ]
            
            for path in exclusion_paths:
                ps_command = f'Add-MpPreference -ExclusionPath "{path}"'
                result = subprocess.run(
                    f'powershell.exe -Command "{ps_command}"',
                    shell=True,
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    self.log_message(f"✅ Added exclusion for {path}")
                else:
                    self.log_message(f"⚠️ Could not add exclusion for {path}")
            
            # Disable real-time protection temporarily
            ps_command = 'Set-MpPreference -DisableRealtimeMonitoring $true'
            result = subprocess.run(
                f'powershell.exe -Command "{ps_command}"',
                shell=True,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                self.log_message("✅ Disabled real-time protection")
            else:
                self.log_message("⚠️ Could not disable real-time protection")
            
            self.log_message("✅ Antivirus blocking fix completed")
            self.log_message("⚠️ Remember to re-enable protection after use")
            
        except Exception as e:
            self.log_message(f"❌ Error fixing antivirus blocking: {str(e)}")
    
    def auto_fix_all(self):
        """Run all fixes automatically"""
        self.log_message("🚀 Starting auto-fix for all issues...")
        
        # Run all fixes in sequence
        fixes = [
            self.fix_menu_close_issue,
            self.fix_game_crashes,
            self.fix_injection_failure,
            self.fix_menu_not_appearing,
            self.fix_performance_issues,
            self.fix_antivirus_blocking
        ]
        
        def run_all_fixes():
            for fix_func in fixes:
                try:
                    fix_func()
                    time.sleep(1)  # Small delay between fixes
                except Exception as e:
                    self.log_message(f"❌ Error in {fix_func.__name__}: {str(e)}")
            
            self.log_message("🎉 Auto-fix completed!")
            self.log_message("⚠️ Please restart your computer for all changes to take effect")
        
        # Run in separate thread
        fix_thread = threading.Thread(target=run_all_fixes)
        fix_thread.daemon = True
        fix_thread.start()
    
    def run_system_scan(self):
        """Run system scan for issues"""
        self.log_message("🔍 Running system scan...")
        
        def scan_system():
            # Check for common issues
            issues_found = []
            
            # Check for COD players folder
            cod_documents = Path.home() / "Documents" / "Call of Duty" / "players"
            if cod_documents.exists():
                issues_found.append("COD players folder exists (may cause menu issues)")
            
            # Check for STZ files
            stz_files = [Path("C:/STZ"), Path("C:/STZ.dat")]
            for stz_file in stz_files:
                if stz_file.exists():
                    issues_found.append(f"STZ file found: {stz_file} (may cause crashes)")
            
            # Check Windows Defender status
            try:
                result = subprocess.run(
                    'powershell.exe -Command "Get-MpPreference | Select-Object DisableRealtimeMonitoring"',
                    shell=True,
                    capture_output=True,
                    text=True
                )
                if "False" in result.stdout:
                    issues_found.append("Windows Defender real-time protection is enabled")
            except:
                pass
            
            # Report findings
            if issues_found:
                self.log_message(f"⚠️ Found {len(issues_found)} potential issues:")
                for issue in issues_found:
                    self.log_message(f"  - {issue}")
            else:
                self.log_message("✅ No common issues detected")
            
            self.log_message("🔍 System scan completed")
        
        # Run scan in separate thread
        scan_thread = threading.Thread(target=scan_system)
        scan_thread.daemon = True
        scan_thread.start()
    
    def reset_configuration(self):
        """Reset all configuration to defaults"""
        self.log_message("🔄 Resetting configuration...")
        
        try:
            # Remove configuration files
            config_files = [
                "loader_config.json",
                "gaming_tool_config.json"
            ]
            
            for config_file in config_files:
                config_path = Path(config_file)
                if config_path.exists():
                    config_path.unlink()
                    self.log_message(f"✅ Removed {config_file}")
            
            self.log_message("✅ Configuration reset completed")
            self.log_message("⚠️ Please restart the application")
            
        except Exception as e:
            self.log_message(f"❌ Error resetting configuration: {str(e)}")
    
    def close_troubleshooter(self):
        """Close the troubleshooter window"""
        self.troubleshoot_window.destroy()
        self.troubleshoot_window = None
    
    def get_troubleshoot_results(self):
        """Get the results of troubleshooting operations"""
        return self.troubleshoot_results
