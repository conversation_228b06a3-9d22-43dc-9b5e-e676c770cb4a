#!/usr/bin/env python3
"""
System Profiler Module
Detects system configuration and customizes the tool for different setups
"""

import os
import sys
import platform
import subprocess
import winreg
import psutil
import json
import ctypes
from pathlib import Path
try:
    import wmi
except ImportError:
    wmi = None

class SystemProfiler:
    def __init__(self):
        self.profile_results = []
        self.system_profile = {}
        self.compatibility_issues = []
        self.customizations_needed = []
        
    def detect_windows_version(self):
        """Detect Windows version and build"""
        try:
            version_info = platform.version()
            release = platform.release()
            build = platform.win32_ver()[1]
            edition = platform.win32_edition()
            
            self.system_profile['windows'] = {
                'version': version_info,
                'release': release,
                'build': build,
                'edition': edition,
                'is_windows_11': int(build) >= 22000 if build else False
            }
            
            self.profile_results.append(f"✅ Windows {release} {edition} (Build {build})")
            
            # Check compatibility
            if release not in ['10', '11']:
                self.compatibility_issues.append(f"⚠️ Windows {release} may have limited compatibility")
            
            return True
            
        except Exception as e:
            self.profile_results.append(f"❌ Windows detection error: {str(e)}")
            return False
    
    def detect_antivirus_software(self):
        """Detect installed antivirus software"""
        try:
            self.profile_results.append("🔍 Scanning for antivirus software...")
            
            # WMI query for antivirus products
            c = wmi.WMI()
            antivirus_products = []
            
            # Windows Security Center
            try:
                for av in c.Win32_Product():
                    av_name = av.Name.lower()
                    if any(keyword in av_name for keyword in ['antivirus', 'security', 'defender', 'norton', 'mcafee', 'kaspersky', 'avast', 'avg', 'bitdefender']):
                        antivirus_products.append({
                            'name': av.Name,
                            'version': av.Version,
                            'vendor': av.Vendor
                        })
            except:
                pass
            
            # Check Windows Defender status
            try:
                result = subprocess.run(
                    'powershell.exe -Command "Get-MpComputerStatus | Select-Object AntivirusEnabled,RealTimeProtectionEnabled"',
                    shell=True, capture_output=True, text=True
                )
                if "True" in result.stdout:
                    antivirus_products.append({
                        'name': 'Windows Defender',
                        'version': 'Built-in',
                        'vendor': 'Microsoft'
                    })
            except:
                pass
            
            self.system_profile['antivirus'] = antivirus_products
            
            if antivirus_products:
                self.profile_results.append(f"🛡️ Found {len(antivirus_products)} security products:")
                for av in antivirus_products:
                    self.profile_results.append(f"   - {av['name']} ({av['vendor']})")
                    self.customizations_needed.append(f"Custom removal procedure for {av['name']}")
            else:
                self.profile_results.append("✅ No third-party antivirus detected")
            
            return True
            
        except Exception as e:
            self.profile_results.append(f"❌ Antivirus detection error: {str(e)}")
            return False
    
    def detect_game_launchers(self):
        """Detect installed game launchers and their paths"""
        try:
            self.profile_results.append("🎮 Scanning for game launchers...")
            
            launchers = {}
            
            # Steam detection
            steam_paths = [
                r"SOFTWARE\Valve\Steam",
                r"SOFTWARE\WOW6432Node\Valve\Steam"
            ]
            
            for path in steam_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path) as key:
                        install_path = winreg.QueryValueEx(key, "InstallPath")[0]
                        launchers['Steam'] = {
                            'path': install_path,
                            'games_path': Path(install_path) / "steamapps" / "common"
                        }
                        break
                except:
                    continue
            
            # Battle.net detection
            battlenet_paths = [
                r"SOFTWARE\Blizzard Entertainment\Battle.net",
                r"SOFTWARE\WOW6432Node\Blizzard Entertainment\Battle.net"
            ]
            
            for path in battlenet_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path) as key:
                        launchers['Battle.net'] = {'detected': True}
                        break
                except:
                    continue
            
            # Epic Games detection
            epic_path = Path.home() / "AppData" / "Local" / "EpicGamesLauncher"
            if epic_path.exists():
                launchers['Epic Games'] = {'path': str(epic_path)}
            
            # Origin detection
            origin_paths = [
                "C:/Program Files (x86)/Origin",
                "C:/Program Files/Origin"
            ]
            for path in origin_paths:
                if Path(path).exists():
                    launchers['Origin'] = {'path': path}
                    break
            
            # Ubisoft Connect detection
            ubisoft_paths = [
                "C:/Program Files (x86)/Ubisoft/Ubisoft Game Launcher",
                "C:/Program Files/Ubisoft/Ubisoft Game Launcher"
            ]
            for path in ubisoft_paths:
                if Path(path).exists():
                    launchers['Ubisoft Connect'] = {'path': path}
                    break
            
            self.system_profile['game_launchers'] = launchers
            
            if launchers:
                self.profile_results.append(f"🎮 Found {len(launchers)} game launchers:")
                for launcher, info in launchers.items():
                    self.profile_results.append(f"   - {launcher}")
                    if 'games_path' in info:
                        self.customizations_needed.append(f"Scan {launcher} games directory: {info['games_path']}")
            else:
                self.profile_results.append("⚠️ No game launchers detected")
            
            return True
            
        except Exception as e:
            self.profile_results.append(f"❌ Game launcher detection error: {str(e)}")
            return False
    
    def detect_hardware_specs(self):
        """Detect hardware specifications"""
        try:
            self.profile_results.append("💻 Detecting hardware specifications...")
            
            # CPU info
            cpu_info = {
                'name': platform.processor(),
                'cores': psutil.cpu_count(logical=False),
                'threads': psutil.cpu_count(logical=True),
                'frequency': psutil.cpu_freq().max if psutil.cpu_freq() else 'Unknown'
            }
            
            # Memory info
            memory = psutil.virtual_memory()
            memory_info = {
                'total_gb': round(memory.total / (1024**3), 2),
                'available_gb': round(memory.available / (1024**3), 2),
                'percent_used': memory.percent
            }
            
            # Disk info
            disk_info = []
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_info.append({
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'fstype': partition.fstype,
                        'total_gb': round(usage.total / (1024**3), 2),
                        'free_gb': round(usage.free / (1024**3), 2)
                    })
                except:
                    continue
            
            # GPU info (basic)
            try:
                c = wmi.WMI()
                gpu_info = []
                for gpu in c.Win32_VideoController():
                    if gpu.Name:
                        gpu_info.append({
                            'name': gpu.Name,
                            'driver_version': gpu.DriverVersion,
                            'memory': gpu.AdapterRAM
                        })
            except:
                gpu_info = [{'name': 'Detection failed'}]
            
            self.system_profile['hardware'] = {
                'cpu': cpu_info,
                'memory': memory_info,
                'disks': disk_info,
                'gpu': gpu_info
            }
            
            self.profile_results.append(f"   CPU: {cpu_info['name']} ({cpu_info['cores']} cores)")
            self.profile_results.append(f"   RAM: {memory_info['total_gb']} GB")
            self.profile_results.append(f"   Storage: {len(disk_info)} drives detected")
            
            # Check for performance concerns
            if memory_info['total_gb'] < 8:
                self.compatibility_issues.append("⚠️ Low RAM (< 8GB) may affect performance")
            
            if any(disk['free_gb'] < 5 for disk in disk_info):
                self.compatibility_issues.append("⚠️ Low disk space (< 5GB) on some drives")
            
            return True
            
        except Exception as e:
            self.profile_results.append(f"❌ Hardware detection error: {str(e)}")
            return False
    
    def detect_system_language_region(self):
        """Detect system language and region settings"""
        try:
            self.profile_results.append("🌍 Detecting language and region...")
            
            # Get system locale
            import locale
            system_locale = locale.getdefaultlocale()
            
            # Get Windows display language
            try:
                result = subprocess.run(
                    'powershell.exe -Command "Get-WinUILanguageOverride"',
                    shell=True, capture_output=True, text=True
                )
                display_language = result.stdout.strip() if result.returncode == 0 else 'Unknown'
            except:
                display_language = 'Unknown'
            
            self.system_profile['locale'] = {
                'system_locale': system_locale,
                'display_language': display_language,
                'encoding': sys.getdefaultencoding()
            }
            
            self.profile_results.append(f"   System Locale: {system_locale[0] if system_locale[0] else 'Unknown'}")
            self.profile_results.append(f"   Display Language: {display_language}")
            
            # Check for non-English systems
            if system_locale[0] and not system_locale[0].startswith('en'):
                self.customizations_needed.append(f"Localization needed for {system_locale[0]}")
                self.customizations_needed.append("Registry paths may differ in non-English Windows")
            
            return True
            
        except Exception as e:
            self.profile_results.append(f"❌ Language detection error: {str(e)}")
            return False
    
    def detect_user_privileges(self):
        """Detect user privileges and UAC settings"""
        try:
            self.profile_results.append("🔐 Checking user privileges...")
            
            # Check if running as admin
            is_admin = os.name == 'nt' and ctypes.windll.shell32.IsUserAnAdmin()
            
            # Check UAC status
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                  r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System") as key:
                    uac_enabled = winreg.QueryValueEx(key, "EnableLUA")[0]
            except:
                uac_enabled = 1  # Default to enabled
            
            self.system_profile['privileges'] = {
                'is_admin': is_admin,
                'uac_enabled': bool(uac_enabled),
                'username': os.getenv('USERNAME'),
                'user_domain': os.getenv('USERDOMAIN')
            }
            
            self.profile_results.append(f"   Administrator: {'✅ Yes' if is_admin else '❌ No'}")
            self.profile_results.append(f"   UAC Enabled: {'✅ Yes' if uac_enabled else '❌ No'}")
            
            if not is_admin:
                self.compatibility_issues.append("❌ Administrator privileges required")
            
            return True
            
        except Exception as e:
            self.profile_results.append(f"❌ Privilege detection error: {str(e)}")
            return False
    
    def detect_network_configuration(self):
        """Detect network configuration that might affect downloads"""
        try:
            self.profile_results.append("🌐 Checking network configuration...")
            
            # Check internet connectivity
            import urllib.request
            try:
                urllib.request.urlopen('http://www.google.com', timeout=5)
                internet_available = True
            except:
                internet_available = False
            
            # Get network interfaces
            network_interfaces = []
            for interface, addrs in psutil.net_if_addrs().items():
                for addr in addrs:
                    if addr.family == 2:  # IPv4
                        network_interfaces.append({
                            'interface': interface,
                            'ip': addr.address,
                            'netmask': addr.netmask
                        })
            
            self.system_profile['network'] = {
                'internet_available': internet_available,
                'interfaces': network_interfaces
            }
            
            self.profile_results.append(f"   Internet: {'✅ Available' if internet_available else '❌ Not available'}")
            self.profile_results.append(f"   Network Interfaces: {len(network_interfaces)}")
            
            if not internet_available:
                self.compatibility_issues.append("❌ Internet connection required for downloads")
            
            return True
            
        except Exception as e:
            self.profile_results.append(f"❌ Network detection error: {str(e)}")
            return False
    
    def generate_custom_configuration(self):
        """Generate custom configuration based on system profile"""
        try:
            config = {
                'system_id': f"{self.system_profile.get('privileges', {}).get('username', 'unknown')}_{platform.node()}",
                'windows_version': self.system_profile.get('windows', {}).get('release', '10'),
                'customizations': {
                    'antivirus_removal': [],
                    'game_paths': [],
                    'registry_paths': [],
                    'service_names': [],
                    'localization': {}
                }
            }
            
            # Customize antivirus removal
            for av in self.system_profile.get('antivirus', []):
                config['customizations']['antivirus_removal'].append({
                    'name': av['name'],
                    'vendor': av['vendor'],
                    'removal_method': self.get_removal_method(av['name'])
                })
            
            # Customize game paths
            for launcher, info in self.system_profile.get('game_launchers', {}).items():
                if 'games_path' in info:
                    config['customizations']['game_paths'].append({
                        'launcher': launcher,
                        'path': str(info['games_path'])
                    })
            
            # Customize for Windows version
            windows_version = self.system_profile.get('windows', {}).get('release', '10')
            if windows_version == '11':
                config['customizations']['registry_paths'] = self.get_windows11_registry_paths()
            else:
                config['customizations']['registry_paths'] = self.get_windows10_registry_paths()
            
            # Save configuration
            config_file = Path("custom_system_config.json")
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            self.profile_results.append(f"✅ Custom configuration saved to {config_file}")
            return config
            
        except Exception as e:
            self.profile_results.append(f"❌ Configuration generation error: {str(e)}")
            return None
    
    def get_removal_method(self, av_name):
        """Get specific removal method for antivirus"""
        removal_methods = {
            'Windows Defender': 'registry_disable',
            'Norton': 'uninstall_tool',
            'McAfee': 'removal_tool',
            'Kaspersky': 'kavremover',
            'Avast': 'avastclear',
            'AVG': 'avg_remover',
            'Bitdefender': 'uninstall_tool'
        }
        return removal_methods.get(av_name, 'standard_uninstall')
    
    def get_windows11_registry_paths(self):
        """Get Windows 11 specific registry paths"""
        return [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer",
            r"SOFTWARE\Microsoft\Windows Security Health\State",
            r"SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate"
        ]
    
    def get_windows10_registry_paths(self):
        """Get Windows 10 specific registry paths"""
        return [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer",
            r"SOFTWARE\Microsoft\Windows Defender",
            r"SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate"
        ]
    
    def run_full_profile(self):
        """Run complete system profiling"""
        self.profile_results = []
        self.compatibility_issues = []
        self.customizations_needed = []
        
        self.profile_results.append("🔍 Starting comprehensive system profiling...")
        
        # Run all detection methods
        detection_methods = [
            (self.detect_windows_version, "Windows Version"),
            (self.detect_user_privileges, "User Privileges"),
            (self.detect_antivirus_software, "Antivirus Software"),
            (self.detect_game_launchers, "Game Launchers"),
            (self.detect_hardware_specs, "Hardware Specifications"),
            (self.detect_system_language_region, "Language & Region"),
            (self.detect_network_configuration, "Network Configuration")
        ]
        
        success_count = 0
        for method, name in detection_methods:
            try:
                if method():
                    success_count += 1
                    self.profile_results.append(f"✅ {name} detection completed")
                else:
                    self.profile_results.append(f"⚠️ {name} detection failed")
            except Exception as e:
                self.profile_results.append(f"❌ {name} error: {str(e)}")
        
        # Generate custom configuration
        custom_config = self.generate_custom_configuration()
        
        # Summary
        self.profile_results.append(f"\n📊 Profiling Summary:")
        self.profile_results.append(f"   Successful detections: {success_count}/{len(detection_methods)}")
        self.profile_results.append(f"   Compatibility issues: {len(self.compatibility_issues)}")
        self.profile_results.append(f"   Customizations needed: {len(self.customizations_needed)}")
        
        if self.compatibility_issues:
            self.profile_results.append(f"\n⚠️ Compatibility Issues:")
            for issue in self.compatibility_issues:
                self.profile_results.append(f"   {issue}")
        
        if self.customizations_needed:
            self.profile_results.append(f"\n🔧 Customizations Needed:")
            for customization in self.customizations_needed:
                self.profile_results.append(f"   {customization}")
        
        return {
            'profile': self.system_profile,
            'config': custom_config,
            'compatibility_issues': self.compatibility_issues,
            'customizations_needed': self.customizations_needed
        }
    
    def get_profile_results(self):
        """Get profiling results"""
        return self.profile_results
