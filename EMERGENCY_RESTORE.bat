@echo off
title EMERGENCY RESTORE - Fix Xbox and Microsoft Store Issues
color 0A

echo.
echo  ==========================================
echo   🚨 EMERGENCY RESTORE SYSTEM
echo   Fix Xbox and Microsoft Store Issues
echo  ==========================================
echo.
echo  This will restore Microsoft Store and Xbox
echo  components that were broken during repairs.
echo.

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.

    echo  [1/6] Restoring Windows Defender...
    powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $false" >nul 2>&1
    reg delete "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender" /v DisableAntiSpyware /f >nul 2>&1
    sc config WinDefend start= auto >nul 2>&1
    net start WinDefend >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Windows Defender: RESTORED) else (echo  ❌ Windows Defender: FAILED)

    echo.
    echo  [2/6] Restoring Windows Firewall...
    netsh advfirewall set allprofiles state on >nul 2>&1
    sc config MpsSvc start= auto >nul 2>&1
    net start MpsSvc >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Windows Firewall: RESTORED) else (echo  ❌ Windows Firewall: FAILED)

    echo.
    echo  [3/6] Restoring Windows Update...
    sc config wuauserv start= auto >nul 2>&1
    sc config UsoSvc start= auto >nul 2>&1
    sc config bits start= auto >nul 2>&1
    net start wuauserv >nul 2>&1
    net start UsoSvc >nul 2>&1
    net start bits >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Windows Update: RESTORED) else (echo  ❌ Windows Update: FAILED)

    echo.
    echo  [4/6] Restoring Microsoft Store...
    powershell.exe -Command "wsreset.exe" >nul 2>&1
    powershell.exe -Command "Get-AppxPackage Microsoft.WindowsStore | Remove-AppxPackage; Get-AppxPackage -AllUsers Microsoft.WindowsStore | Foreach {Add-AppxPackage -Register ($_.InstallLocation + '\AppXManifest.xml') -DisableDevelopmentMode}" >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Microsoft Store: RESTORED) else (echo  ❌ Microsoft Store: FAILED)

    echo.
    echo  [5/6] Restoring Xbox Components...
    powershell.exe -Command "Get-AppxPackage -AllUsers *Xbox* | Foreach {Add-AppxPackage -Register ($_.InstallLocation + '\AppXManifest.xml') -DisableDevelopmentMode}" >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Xbox Components: RESTORED) else (echo  ❌ Xbox Components: FAILED)

    echo.
    echo  [6/6] Cleaning up broken files...
    del /q fix_xbox_components.bat >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Cleanup: COMPLETE) else (echo  ❌ Cleanup: FAILED)

    echo.
    echo  ==========================================
    echo   ✅ EMERGENCY RESTORE COMPLETE!
    echo  ==========================================
    echo.
    echo  Microsoft Store and Xbox should now work.
    echo  System security has been restored.
    echo.

) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  Right-click this file and select "Run as administrator"
    echo.
)

echo.
echo  Press any key to exit...
pause >nul
