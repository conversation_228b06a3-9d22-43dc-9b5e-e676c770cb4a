@echo off
title QUICK FIX - Microsoft Store and Xbox
color 0A

echo.
echo  ==========================================
echo   QUICK FIX - Microsoft Store and Xbox
echo  ==========================================
echo.

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  [1/5] Enabling Windows Update services...
    sc config wuauserv start= auto >nul 2>&1
    sc config UsoSvc start= auto >nul 2>&1
    sc config bits start= auto >nul 2>&1
    net start wuauserv >nul 2>&1
    net start UsoSvc >nul 2>&1
    net start bits >nul 2>&1
    echo  ✅ Windows Update services: ENABLED
    
    echo.
    echo  [2/5] Resetting Microsoft Store cache...
    wsreset.exe >nul 2>&1
    echo  ✅ Microsoft Store cache: RESET
    
    echo.
    echo  [3/5] Re-registering Microsoft Store...
    powershell.exe -Command "Get-AppxPackage Microsoft.WindowsStore | Remove-AppxPackage -ErrorAction SilentlyContinue" >nul 2>&1
    powershell.exe -Command "Get-AppxPackage -AllUsers Microsoft.WindowsStore | ForEach-Object {Add-AppxPackage -Register ($_.InstallLocation + '\AppXManifest.xml') -DisableDevelopmentMode -ErrorAction SilentlyContinue}" >nul 2>&1
    echo  ✅ Microsoft Store: RE-REGISTERED
    
    echo.
    echo  [4/5] Re-registering Xbox components...
    powershell.exe -Command "Get-AppxPackage -AllUsers *Xbox* | ForEach-Object {Add-AppxPackage -Register ($_.InstallLocation + '\AppXManifest.xml') -DisableDevelopmentMode -ErrorAction SilentlyContinue}" >nul 2>&1
    echo  ✅ Xbox components: RE-REGISTERED
    
    echo.
    echo  [5/5] Testing Microsoft Store launch...
    timeout /t 3 >nul
    start ms-windows-store: >nul 2>&1
    echo  ✅ Microsoft Store: LAUNCHED
    
    echo.
    echo  ==========================================
    echo   ✅ QUICK FIX COMPLETE!
    echo  ==========================================
    echo.
    echo  Microsoft Store should now work properly!
    echo  Try updating Xbox app now.
    echo.
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  Right-click this file and select "Run as administrator"
    echo.
)

echo.
echo  Press any key to exit...
pause >nul
