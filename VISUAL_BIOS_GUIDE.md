# 🖥️ VISUAL BIOS INTERFACE GUIDE
## Step-by-Step BIOS Changes with Screenshots Guide

---

## 🚀 **STEP 1: COMPLETE SOFTWARE FIXES FIRST**

### **Run This Command:**
```
Right-click SIMPLE_FIX.bat → Run as administrator
```

### **Then Run:**
```
Right-click AUTO_BIOS_ENTRY.bat → Run as administrator
```
**This will automatically boot to BIOS in 60 seconds!**

---

## 🔧 **STEP 2: BIOS INTERFACE NAVIGATION**

### **🎯 WHAT YOU'LL SEE WHEN BIOS OPENS:**

```
┌─────────────────────────────────────────────────────────────┐
│                    BIOS SETUP UTILITY                      │
├─────────────────────────────────────────────────────────────┤
│  Main    Advanced    Security    Boot    Save & Exit       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Use ← → ↑ ↓ keys to navigate                              │
│  Press ENTER to select                                      │
│  Press ESC to go back                                       │
│  Press F10 to Save and Exit                                │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔒 **CHANGE #1: DISABLE SECURE BOOT**

### **Navigation Path:**
1. **Use → arrow key** to move to **"Security"** tab
2. **Press ENTER** to enter Security menu
3. **Look for "Secure Boot"** option
4. **Press ENTER** on Secure Boot
5. **Change from "Enabled" to "Disabled"**
6. **Press ENTER** to confirm

### **Visual Example:**
```
┌─────────────────────────────────────────────────────────────┐
│                    SECURITY MENU                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  → Secure Boot                           [Enabled]         │
│    TPM Configuration                     [Enabled]         │
│    Password Settings                     [Not Set]         │
│    Intel TXT                            [Disabled]        │
│                                                             │
│  ↑↓ Select Item    ENTER: Select    ESC: Exit             │
└─────────────────────────────────────────────────────────────┘
```

### **After Selecting Secure Boot:**
```
┌─────────────────────────────────────────────────────────────┐
│                  SECURE BOOT MENU                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Secure Boot                            [Enabled]          │
│                                                             │
│  → Change to: [Disabled] ← SELECT THIS                     │
│                                                             │
│  ENTER: Change    ESC: Cancel                              │
└─────────────────────────────────────────────────────────────┘
```

---

## 🖥️ **CHANGE #2: ENABLE VIRTUALIZATION**

### **Navigation Path:**
1. **Use → arrow key** to move to **"Advanced"** tab
2. **Press ENTER** to enter Advanced menu
3. **Look for "CPU Configuration"** or "Processor"**
4. **Press ENTER** on CPU Configuration
5. **Find "Intel VT-x" or "AMD-V" or "Virtualization Technology"**
6. **Change from "Disabled" to "Enabled"**
7. **Press ENTER** to confirm

### **Visual Example:**
```
┌─────────────────────────────────────────────────────────────┐
│                   ADVANCED MENU                            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  → CPU Configuration                                        │
│    Chipset Configuration                                    │
│    Storage Configuration                                    │
│    USB Configuration                                        │
│    Network Stack Configuration                             │
│                                                             │
│  ↑↓ Select Item    ENTER: Select    ESC: Exit             │
└─────────────────────────────────────────────────────────────┘
```

### **Inside CPU Configuration:**
```
┌─────────────────────────────────────────────────────────────┐
│                 CPU CONFIGURATION                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Intel(R) Virtualization Technology    [Disabled]         │
│  → Change to: [Enabled] ← SELECT THIS                      │
│                                                             │
│  Intel VT-d                            [Disabled]         │
│  Hyper-Threading                       [Enabled]          │
│                                                             │
│  ENTER: Change    ESC: Back                                │
└─────────────────────────────────────────────────────────────┘
```

---

## 💾 **STEP 3: SAVE AND EXIT**

### **Navigation Path:**
1. **Press F10** (Save and Exit shortcut)
2. **OR** use → arrow to go to **"Save & Exit"** tab
3. **Select "Save Changes and Exit"**
4. **Press ENTER** to confirm
5. **Select "Yes"** when asked to save

### **Visual Example:**
```
┌─────────────────────────────────────────────────────────────┐
│                  SAVE & EXIT MENU                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  → Save Changes and Exit                                    │
│    Discard Changes and Exit                                │
│    Save Changes                                            │
│    Discard Changes                                         │
│    Load Setup Defaults                                     │
│                                                             │
│  ENTER: Select    ESC: Cancel                              │
└─────────────────────────────────────────────────────────────┘
```

### **Confirmation Dialog:**
```
┌─────────────────────────────────────────────────────────────┐
│                    SAVE CONFIRMATION                       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  Save configuration changes and exit setup?                │
│                                                             │
│  → [Yes]    [No]                                           │
│                                                             │
│  ENTER: Select    ESC: Cancel                              │
└─────────────────────────────────────────────────────────────┘
```

---

## 🏭 **MANUFACTURER-SPECIFIC LOCATIONS**

### **ASUS BIOS:**
```
Secure Boot: Boot → Secure Boot → OS Type → "Other OS"
Virtualization: Advanced → CPU Configuration → "Intel Virtualization Technology"
```

### **MSI BIOS:**
```
Secure Boot: Settings → Security → Secure Boot → "Secure Boot Mode"
Virtualization: OC → CPU Features → "Intel Virtualization Tech"
```

### **GIGABYTE BIOS:**
```
Secure Boot: BIOS → Windows 8/10 Features → "Secure Boot"
Virtualization: M.I.T. → Advanced Frequency Settings → Advanced CPU Settings
```

### **DELL BIOS:**
```
Secure Boot: Secure Boot → "Secure Boot Enable"
Virtualization: Virtualization Support → "Virtualization"
```

### **HP BIOS:**
```
Secure Boot: System Configuration → "Secure Boot Configuration"
Virtualization: System Configuration → "Virtualization Technology"
```

---

## 🎯 **QUICK REFERENCE CARD**

### **🔍 WHAT TO LOOK FOR:**
- **Secure Boot**: Usually in Security or Boot section
- **Virtualization**: Usually in Advanced → CPU Configuration

### **🎮 WHAT TO CHANGE:**
- **Secure Boot**: `Enabled` → `Disabled`
- **Virtualization**: `Disabled` → `Enabled`

### **💾 HOW TO SAVE:**
- **Press F10** (universal save shortcut)
- **OR** go to Save & Exit → Save Changes and Exit

### **🔑 NAVIGATION KEYS:**
- **Arrow Keys**: Navigate menus
- **ENTER**: Select/Change option
- **ESC**: Go back/Cancel
- **F10**: Save and Exit

---

## ✅ **VERIFICATION AFTER BIOS CHANGES**

### **After Windows Boots:**
1. **Run Support Tools.exe**
2. **All checks should now show:**
   - ✅ Secure Boot: Disabled
   - ✅ Virtualization: Enabled
   - ✅ Firewall: Working
   - ✅ Real-time Protection: Working

### **If Still Issues:**
- **Double-check** BIOS settings were saved
- **Restart** and verify changes in BIOS again
- **Run** software fixes again if needed

---

**🎯 Follow this visual guide and all Support Tools.exe issues will be resolved!**

*The key is making these 2 simple changes in BIOS after the automated entry!*
