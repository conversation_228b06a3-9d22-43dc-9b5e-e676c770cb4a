@echo off
title AUTO BIOS ENTRY - No Key Pressing Required
color 0A

echo.
echo  ==========================================
echo   🔧 AUTO BIOS ENTRY - AUTOMATED SOLUTION
echo  ==========================================
echo.
echo  This will automatically boot your computer
echo  directly to BIOS - NO key pressing required!
echo.
echo  ⚠️ WHAT THIS DOES:
echo  • Restarts computer in 60 seconds
echo  • Boots DIRECTLY to BIOS/UEFI setup
echo  • No need to press F2, F12, DEL, or ESC
echo  • You can make BIOS changes manually
echo.

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  📋 BIOS CHANGES NEEDED:
    echo  ======================
    echo.
    echo  1. DISABLE SECURE BOOT:
    echo     • Navigate to Security or Boot section
    echo     • Find "Secure Boot" option
    echo     • Set to "Disabled"
    echo.
    echo  2. ENABLE VIRTUALIZATION:
    echo     • Go to Advanced or CPU Configuration
    echo     • Find "Intel VT-x" or "AMD-V" or "Virtualization Technology"
    echo     • Set to "Enabled"
    echo.
    echo  3. SAVE AND EXIT:
    echo     • Press F10 or find "Save and Exit"
    echo     • Confirm changes
    echo.
    
    choice /c YN /m "Ready to boot to BIOS automatically? (Y/N)"
    if errorlevel 2 goto :cancel
    if errorlevel 1 goto :auto_bios
    
    :auto_bios
    echo.
    echo  🚀 INITIATING AUTO BIOS BOOT...
    echo.
    echo  ⏰ System will restart in 60 seconds
    echo  🔧 Will boot DIRECTLY to BIOS setup
    echo  📝 Make the required changes listed above
    echo  💾 Save and exit when done
    echo.
    echo  ⚠️ IMPORTANT: Don't forget to:
    echo  1. Disable Secure Boot
    echo  2. Enable Virtualization
    echo  3. Save and Exit
    echo.
    
    REM Use firmware restart to boot directly to BIOS
    shutdown /r /fw /t 60
    
    if %errorLevel% == 0 (
        echo  ✅ AUTO BIOS BOOT SCHEDULED!
        echo.
        echo  The system will automatically boot to BIOS
        echo  in 60 seconds. No key pressing required!
        echo.
        echo  After making BIOS changes:
        echo  • Save and Exit BIOS
        echo  • Run Support Tools.exe to verify
        echo  • All checks should pass
        echo.
    ) else (
        echo  ❌ Auto BIOS boot failed
        echo.
        echo  MANUAL ALTERNATIVE:
        echo  1. Restart computer manually
        echo  2. Press F2, F12, DEL, or ESC during boot
        echo  3. Make the BIOS changes listed above
        echo.
    )
    
    goto :end
    
    :cancel
    echo.
    echo  🚫 Auto BIOS boot cancelled
    echo.
    echo  MANUAL BIOS ACCESS:
    echo  ==================
    echo  1. Restart computer
    echo  2. Press F2, F12, DEL, or ESC during boot
    echo  3. Make the required BIOS changes
    echo.
    echo  OR run this script again when ready
    echo.
    goto :end
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  Auto BIOS boot requires administrator privileges.
    echo.
    echo  HOW TO RUN AS ADMINISTRATOR:
    echo  ===========================
    echo  1. Right-click this file (AUTO_BIOS_ENTRY.bat)
    echo  2. Select "Run as administrator"
    echo  3. Click "Yes" when prompted by UAC
    echo.
    
    choice /c YN /m "Would you like to restart with administrator privileges? (Y/N)"
    if errorlevel 2 goto :end
    if errorlevel 1 goto :restart_admin
    
    :restart_admin
    echo.
    echo  🔄 Restarting with administrator privileges...
    
    REM Try to restart with admin privileges
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    goto :end
)

:end
echo.
echo  ==========================================
echo   Auto BIOS Entry Complete
echo  ==========================================
echo.
pause
exit /b
