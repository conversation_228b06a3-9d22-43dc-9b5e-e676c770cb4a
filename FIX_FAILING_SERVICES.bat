@echo off
title FIX FAILING SERVICES - Enhanced Recovery
color 0A

echo.
echo  ==========================================
echo   🔧 FIX FAILING SERVICES
echo   Enhanced Service Recovery
echo  ==========================================
echo.
echo  This will fix the services that are failing:
echo  • Update Orchestrator Service
echo  • BITS Service  
echo  • Delivery Optimization Service
echo  • Windows Firewall Service
echo.

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  🔧 STEP 1: Fixing service dependencies...
    
    REM Fix core dependencies first
    sc config RpcSs start= auto >nul 2>&1
    sc start RpcSs >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ RPC Service) else (echo  ❌ RPC Service)
    
    sc config DcomLaunch start= auto >nul 2>&1
    sc start DcomLaunch >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ DCOM Launch) else (echo  ❌ DCOM Launch)
    
    sc config EventLog start= auto >nul 2>&1
    sc start EventLog >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Event Log) else (echo  ❌ Event Log)
    
    echo.
    echo  🔧 STEP 2: Fixing firewall dependencies...
    
    sc config BFE start= auto >nul 2>&1
    sc start BFE >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Base Filtering Engine) else (echo  ❌ Base Filtering Engine)
    
    sc config PolicyAgent start= auto >nul 2>&1
    sc start PolicyAgent >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Policy Agent) else (echo  ❌ Policy Agent)
    
    echo.
    echo  🔧 STEP 3: Reconfiguring failing services...
    
    REM Update Orchestrator with dependencies
    sc config UsoSvc start= auto depend= RpcSs >nul 2>&1
    sc stop UsoSvc >nul 2>&1
    timeout /t 2 >nul
    sc start UsoSvc >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Update Orchestrator Service) else (echo  ❌ Update Orchestrator Service)
    
    REM BITS with dependencies
    sc config BITS start= auto depend= RpcSs/EventLog >nul 2>&1
    sc stop BITS >nul 2>&1
    timeout /t 2 >nul
    sc start BITS >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ BITS Service) else (echo  ❌ BITS Service)
    
    REM Delivery Optimization
    sc config DoSvc start= auto depend= RpcSs >nul 2>&1
    sc stop DoSvc >nul 2>&1
    timeout /t 2 >nul
    sc start DoSvc >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Delivery Optimization) else (echo  ❌ Delivery Optimization)
    
    REM Windows Firewall
    sc config MpsSvc start= auto depend= BFE/RpcSs >nul 2>&1
    sc stop MpsSvc >nul 2>&1
    timeout /t 2 >nul
    sc start MpsSvc >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Windows Firewall Service) else (echo  ❌ Windows Firewall Service)
    
    echo.
    echo  🔧 STEP 4: PowerShell service recovery...
    
    powershell.exe -Command "Get-Service UsoSvc | Set-Service -StartupType Automatic -PassThru | Start-Service -ErrorAction SilentlyContinue" >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ PS Update Orchestrator) else (echo  ❌ PS Update Orchestrator)
    
    powershell.exe -Command "Get-Service BITS | Set-Service -StartupType Automatic -PassThru | Start-Service -ErrorAction SilentlyContinue" >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ PS BITS Service) else (echo  ❌ PS BITS Service)
    
    powershell.exe -Command "Get-Service DoSvc | Set-Service -StartupType Automatic -PassThru | Start-Service -ErrorAction SilentlyContinue" >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ PS Delivery Optimization) else (echo  ❌ PS Delivery Optimization)
    
    powershell.exe -Command "Get-Service MpsSvc | Set-Service -StartupType Automatic -PassThru | Start-Service -ErrorAction SilentlyContinue" >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ PS Windows Firewall) else (echo  ❌ PS Windows Firewall)
    
    echo.
    echo  🔧 STEP 5: Registry permission fixes...
    
    REM Fix registry permissions for Windows Update
    reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\WindowsUpdate\Auto Update" /v AUOptions /t REG_DWORD /d 4 /f >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Auto Update Registry) else (echo  ❌ Auto Update Registry)
    
    REM Remove blocking policies
    reg delete "HKLM\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" /v NoAutoUpdate /f >nul 2>&1
    reg delete "HKLM\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate" /v DisableWindowsUpdateAccess /f >nul 2>&1
    reg delete "HKLM\SOFTWARE\Policies\Microsoft\Windows\WindowsUpdate\AU" /v NoAutoUpdate /f >nul 2>&1
    echo  ✅ Removed blocking policies
    
    echo.
    echo  🔧 STEP 6: Firewall profile fixes...
    
    netsh advfirewall set allprofiles state on >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ All Firewall Profiles) else (echo  ❌ All Firewall Profiles)
    
    powershell.exe -Command "Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled True" >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ PS Firewall Profiles) else (echo  ❌ PS Firewall Profiles)
    
    echo.
    echo  ==========================================
    echo   📊 SERVICE FIX COMPLETED
    echo  ==========================================
    echo.
    echo  ✅ Dependencies fixed
    echo  ✅ Services reconfigured  
    echo  ✅ PowerShell recovery applied
    echo  ✅ Registry permissions fixed
    echo  ✅ Firewall profiles enabled
    echo.
    echo  💡 NEXT STEPS:
    echo  1. Test the services in GameBoost Pro
    echo  2. Try Safe Mode or Gaming Mode again
    echo  3. Services should now start properly
    echo.
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator to fix
    echo  system services and registry permissions.
    echo.
    echo  HOW TO RUN AS ADMINISTRATOR:
    echo  ===========================
    echo  1. Right-click this file (FIX_FAILING_SERVICES.bat)
    echo  2. Select "Run as administrator"
    echo  3. Click "Yes" when prompted by UAC
    echo.
    
    choice /c YN /m "Would you like to restart with administrator privileges? (Y/N)"
    if errorlevel 2 goto :end
    if errorlevel 1 goto :restart_admin
    
    :restart_admin
    echo.
    echo  🔄 Restarting with administrator privileges...
    
    REM Try to restart with admin privileges
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    goto :end
)

:end
echo.
echo  ==========================================
echo   Service Fix Complete
echo  ==========================================
echo.
pause
exit /b
