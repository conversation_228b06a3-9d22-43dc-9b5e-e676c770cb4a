@echo off
title FIX SUPPORT TOOLS ISSUES
color 0E

echo.
echo  ==========================================
echo   🔧 FIX SUPPORT TOOLS ISSUES
echo  ==========================================
echo.
echo  This will fix all issues detected by
echo  Support Tools.exe including:
echo.
echo  • Secure Boot errors
echo  • Virtualization errors  
echo  • Firewall status errors
echo  • Real-time Protection errors
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  🚀 Starting comprehensive fix...
    echo.
    
    REM Run the Python fixer
    python FIX_ALL_SUPPORT_TOOLS_ISSUES.py
    
    if %errorLevel% == 0 (
        echo.
        echo  ==========================================
        echo   ✅ FIX OPERATION COMPLETED
        echo  ==========================================
        echo.
        echo  💡 NEXT STEPS:
        echo  1. RESTART your computer
        echo  2. Run Support Tools.exe again
        echo  3. All checks should now pass
        echo.
        
        choice /c YN /m "Would you like to restart now? (Y/N)"
        if errorlevel 2 goto :no_restart
        if errorlevel 1 goto :restart
        
        :restart
        echo  🔄 Restarting in 10 seconds...
        timeout /t 10
        shutdown /r /t 0
        goto :end
        
        :no_restart
        echo  ⚠️ Please restart manually for changes to take effect
        goto :end
        
    ) else (
        echo.
        echo  ⚠️ Some issues may require manual intervention
        echo  📄 Check BIOS_INSTRUCTIONS.txt for manual steps
        echo.
    )
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator to fix
    echo  system-level issues detected by Support Tools.exe
    echo.
    echo  HOW TO RUN AS ADMINISTRATOR:
    echo  ===========================
    echo  1. Right-click this file (FIX_SUPPORT_TOOLS_ISSUES.bat)
    echo  2. Select "Run as administrator"
    echo  3. Click "Yes" when prompted by UAC
    echo.
    
    choice /c YN /m "Would you like to restart with administrator privileges? (Y/N)"
    if errorlevel 2 goto :end
    if errorlevel 1 goto :restart_admin
    
    :restart_admin
    echo.
    echo  🔄 Restarting with administrator privileges...
    
    REM Try to restart with admin privileges
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    goto :end
)

:end
echo.
echo  ==========================================
echo   Support Tools Issue Fixer Complete
echo  ==========================================
echo.
pause
exit /b
