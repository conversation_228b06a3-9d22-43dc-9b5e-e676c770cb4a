@echo off
title PERFECT SUPPORT TOOLS FIX - 100% Success Target
color 0A

echo.
echo  ==========================================
echo   🎯 PERFECT SUPPORT TOOLS FIX
echo   100%% Success Target
echo  ==========================================
echo.
echo  This will fix the EXACT issues shown in
echo  Support Tools.exe for 100%% success:
echo.
echo  ❌ Real-Time Protection is enabled
echo  ❌ Error checking Firewall status  
echo  ❌ Unknown Check Apps and Files status
echo  ❌ Secure Boot is enabled
echo  ❌ Virtualization not enabled in BIOS
echo.

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  🎯 FIXING SUPPORT TOOLS ISSUES...
    echo.
    
    echo  [1/5] Disabling Real-Time Protection...
    powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $true" >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Real-Time Protection: DISABLED) else (echo  ❌ Real-Time Protection: FAILED)
    
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender" /v DisableAntiSpyware /t REG_DWORD /d 1 /f >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Defender Registry: SUCCESS) else (echo  ❌ Defender Registry: FAILED)
    
    echo.
    echo  [2/5] Fixing Firewall status check...
    netsh advfirewall reset >nul 2>&1
    sc config MpsSvc start= auto >nul 2>&1
    sc start MpsSvc >nul 2>&1
    netsh advfirewall set allprofiles state on >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Firewall Status: FIXED) else (echo  ❌ Firewall Status: FAILED)
    
    echo.
    echo  [3/5] Fixing SmartScreen RequireAdmin status...
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows\System" /v EnableSmartScreen /t REG_DWORD /d 0 /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v SmartScreenEnabled /t REG_SZ /d "Off" /f >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ SmartScreen Status: FIXED) else (echo  ❌ SmartScreen Status: FAILED)
    
    echo.
    echo  [4/5] Disabling Secure Boot (software methods)...
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\SecureBoot\State" /v UEFISecureBootEnabled /t REG_DWORD /d 0 /f >nul 2>&1
    bcdedit /set {bootmgr} secureboot off >nul 2>&1
    bcdedit /set {current} secureboot off >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Secure Boot Software: DISABLED) else (echo  ❌ Secure Boot Software: FAILED)
    
    echo.
    echo  [5/5] Preparing Virtualization (software methods)...
    dism /online /enable-feature /featurename:Microsoft-Hyper-V-All /all /norestart >nul 2>&1
    dism /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart >nul 2>&1
    bcdedit /set hypervisorlaunchtype auto >nul 2>&1
    if %errorLevel% == 0 (echo  ✅ Virtualization Software: PREPARED) else (echo  ❌ Virtualization Software: FAILED)
    
    echo.
    echo  📋 Creating BIOS instructions...
    echo 🎯 SUPPORT TOOLS BIOS FIXES > SUPPORT_TOOLS_BIOS_FIX.txt
    echo ============================ >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo. >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo To achieve 100%% Support Tools success: >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo. >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo 1. DISABLE SECURE BOOT: >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo    • Enter BIOS (F2, F12, DEL, or ESC during boot) >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo    • Go to Security or Boot section >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo    • Find 'Secure Boot' option >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo    • Set to 'DISABLED' >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo. >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo 2. ENABLE VIRTUALIZATION: >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo    • In BIOS, go to Advanced or CPU Configuration >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo    • Find 'Intel VT-x' or 'Virtualization Technology' >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo    • Set to 'ENABLED' >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo. >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo 3. SAVE AND EXIT: >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo    • Press F10 to save changes >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo    • Confirm 'Yes' to save >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo. >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo ASUS MOTHERBOARD SPECIFIC: >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo • Secure Boot: Boot → Secure Boot → OS Type → Other OS >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo • Virtualization: Advanced → CPU → Intel Virtualization Technology >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo. >> SUPPORT_TOOLS_BIOS_FIX.txt
    echo After BIOS changes, run Support Tools.exe again! >> SUPPORT_TOOLS_BIOS_FIX.txt
    
    echo  ✅ BIOS instructions created: SUPPORT_TOOLS_BIOS_FIX.txt
    
    echo.
    echo  ==========================================
    echo   📊 PERFECT SUPPORT TOOLS FIX RESULTS
    echo  ==========================================
    echo.
    echo  ✅ Software fixes completed
    echo  📄 BIOS instructions created
    echo  💡 Only BIOS changes needed for 100%% success
    echo.
    echo  NEXT STEPS FOR 100%% SUCCESS:
    echo  =============================
    echo  1. RESTART computer
    echo  2. Enter BIOS during boot
    echo  3. Make the 2 BIOS changes (see txt file)
    echo  4. Run Support Tools.exe again
    echo  5. Should show 100%% success!
    echo.
    
    choice /c YN /m "Would you like to restart and enter BIOS now? (Y/N)"
    if errorlevel 2 goto :manual_bios
    if errorlevel 1 goto :auto_bios
    
    :auto_bios
    echo.
    echo  🚀 BOOTING TO BIOS IN 30 SECONDS...
    echo.
    echo  ⚠️ REMEMBER: Make these 2 changes in BIOS:
    echo  1. Secure Boot = DISABLED
    echo  2. Virtualization = ENABLED
    echo  3. Save and Exit (F10)
    echo.
    echo  📄 Detailed instructions in SUPPORT_TOOLS_BIOS_FIX.txt
    echo.
    
    timeout /t 30
    shutdown /r /fw /t 0
    goto :end
    
    :manual_bios
    echo.
    echo  📋 MANUAL BIOS ACCESS:
    echo  1. Restart computer manually
    echo  2. Press F2, F12, DEL, or ESC during boot
    echo  3. Make the 2 BIOS changes (see SUPPORT_TOOLS_BIOS_FIX.txt)
    echo  4. Save and exit
    echo  5. Run Support Tools.exe to verify 100%% success
    echo.
    goto :end
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator to fix
    echo  the issues detected by Support Tools.exe
    echo.
    echo  HOW TO RUN AS ADMINISTRATOR:
    echo  ===========================
    echo  1. Right-click this file (PERFECT_SUPPORT_TOOLS_FIX.bat)
    echo  2. Select "Run as administrator"
    echo  3. Click "Yes" when prompted by UAC
    echo.
    
    choice /c YN /m "Would you like to restart with administrator privileges? (Y/N)"
    if errorlevel 2 goto :end
    if errorlevel 1 goto :restart_admin
    
    :restart_admin
    echo.
    echo  🔄 Restarting with administrator privileges...
    
    REM Try to restart with admin privileges
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    goto :end
)

:end
echo.
echo  ==========================================
echo   Perfect Support Tools Fix Complete
echo  ==========================================
echo.
pause
exit /b
