
BIOS CONFIGURATION INSTRUCTIONS
===============================

SECURE BOOT DISABLE:
1. Restart computer and enter BIOS (F2, F12, DEL, or ESC during boot)
2. Navigate to Security or Boot section
3. Find "Secure Boot" option
4. Set to "Disabled"
5. Save and exit BIOS

VIRTUALIZATION ENABLE:
1. In BIOS, go to Advanced or CPU Configuration
2. Find "Intel VT-x" or "AMD-V" or "Virtualization Technology"
3. Set to "Enabled"
4. Save and exit BIOS

MANUFACTURER LOCATIONS:
- ASUS: Advanced > CPU Configuration
- MSI: OC > CPU Features
- Gigabyte: M.I.T. > Advanced CPU Settings
- Dell: Virtualization Support
- HP: System Configuration

AFTER BIOS CHANGES:
1. Save settings and restart
2. Run Support Tools.exe again to verify
3. All checks should now pass
        