@echo off
title EMERGENCY RESTORE - Gaming Tool Safety Recovery
echo.
echo ========================================
echo  EMERGENCY RESTORE - Gaming Tool Safety
echo ========================================
echo.
echo This script will restore your system to safe defaults
echo if the gaming tool caused any issues.
echo.
pause

echo [1/5] Enabling Windows Defender Real-Time Protection...
powershell -Command "Set-MpPreference -DisableRealtimeMonitoring $false"

echo [2/5] Enabling Windows Firewall...
netsh advfirewall set allprofiles state on

echo [3/5] Restoring SmartScreen...
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer" /v SmartScreenEnabled /t REG_SZ /d RequireAdmin /f

echo [4/5] Starting security services...
net start MpsSvc
net start WinDefend
net start WdNisSvc

echo [5/5] Enabling Windows Update...
sc config wuauserv start= auto
sc config UsoSvc start= auto
net start wuauserv
net start UsoSvc

echo.
echo ========================================
echo  EMERGENCY RESTORE COMPLETE!
echo ========================================
echo.
echo Your system security has been restored to safe defaults.
echo All protection features are now enabled.
echo.
pause
