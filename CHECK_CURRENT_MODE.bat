@echo off
title SECURITY MODE CHECKER
color 0B

echo.
echo  ==========================================
echo   🔍 SECURITY MODE CHECKER
echo  ==========================================
echo.
echo  Checking current security mode...
echo.

REM Change to script directory
cd /d "%~dp0"

REM Run the status checker
python -c "from security_toggle import SecurityToggle; t = SecurityToggle(); state = t.get_current_security_state(); security_active = any(state.values()); print('📊 CURRENT MODE:', '🛡️ SAFE MODE (Security Enabled)' if security_active else '🎮 GAMING MODE (Security Disabled)'); print(); [print(f'  {k.replace(\"_\", \" \").title()}: {\"✅ ENABLED\" if v else \"❌ DISABLED\"}') for k, v in state.items()]"

echo.
echo  ==========================================
echo   🔄 TOGGLE OPTIONS
echo  ==========================================
echo.
echo  Available actions:
echo  • Run GAMING_MODE.bat to disable security
echo  • Run SAFE_MODE.bat to enable security
echo  • Use the main interface toggle buttons
echo.

pause
exit /b
