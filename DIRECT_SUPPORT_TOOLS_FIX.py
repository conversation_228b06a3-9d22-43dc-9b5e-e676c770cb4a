#!/usr/bin/env python3
"""
Direct Support Tools Fix
Simple, direct fix for all Support Tools.exe issues
"""

import subprocess
import os
import sys

def run_command(command, description):
    """Run command with simple output"""
    print(f"Running: {description}")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"  SUCCESS: {description}")
            return True
        else:
            print(f"  FAILED: {description}")
            return False
    except Exception as e:
        print(f"  ERROR: {description} - {str(e)}")
        return False

def is_admin():
    """Check admin privileges"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def main():
    print("DIRECT SUPPORT TOOLS FIX")
    print("=" * 40)
    
    if not is_admin():
        print("ERROR: Administrator privileges required!")
        print("Right-click and 'Run as administrator'")
        input("Press Enter to exit...")
        return
    
    print("Administrator privileges confirmed")
    print()
    
    print("FIXING SUPPORT TOOLS ISSUES...")
    print("-" * 30)
    
    # Fix 1: Secure Boot disable
    print("\n1. FIXING SECURE BOOT...")
    commands = [
        ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\SecureBoot\\State" /v UEFISecureBootEnabled /t REG_DWORD /d 0 /f', "Secure Boot Registry 1"),
        ('reg add "HKLM\\SYSTEM\\CurrentControlSet\\Control\\SecureBoot" /v SecureBootEnabled /t REG_DWORD /d 0 /f', "Secure Boot Registry 2"),
        ('bcdedit /set {bootmgr} secureboot off', "Boot Manager Secure Boot"),
        ('bcdedit /set {current} secureboot off', "Current Boot Secure Boot"),
    ]
    
    secure_boot_fixes = 0
    for cmd, desc in commands:
        if run_command(cmd, desc):
            secure_boot_fixes += 1
    
    # Fix 2: Virtualization enable
    print("\n2. FIXING VIRTUALIZATION...")
    commands = [
        ('dism /online /enable-feature /featurename:Microsoft-Hyper-V-All /all /norestart', "Hyper-V Features"),
        ('dism /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart', "VM Platform"),
        ('dism /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart', "WSL Feature"),
        ('bcdedit /set hypervisorlaunchtype auto', "Hypervisor Launch"),
    ]
    
    virtualization_fixes = 0
    for cmd, desc in commands:
        if run_command(cmd, desc):
            virtualization_fixes += 1
    
    # Fix 3: Firewall reset
    print("\n3. FIXING FIREWALL...")
    commands = [
        ('netsh advfirewall reset', "Firewall Reset"),
        ('sc stop MpsSvc', "Stop Firewall Service"),
        ('sc start MpsSvc', "Start Firewall Service"),
        ('sc config MpsSvc start= auto', "Firewall Auto Start"),
        ('netsh advfirewall set allprofiles state on', "Enable All Profiles"),
    ]
    
    firewall_fixes = 0
    for cmd, desc in commands:
        if run_command(cmd, desc):
            firewall_fixes += 1
    
    # Fix 4: Real-time Protection reset
    print("\n4. FIXING REAL-TIME PROTECTION...")
    commands = [
        ('sc stop WinDefend', "Stop Defender Service"),
        ('sc start WinDefend', "Start Defender Service"),
        ('sc config WinDefend start= auto', "Defender Auto Start"),
        ('powershell.exe -Command "Set-MpPreference -DisableRealtimeMonitoring $false"', "Enable Real-time"),
        ('powershell.exe -Command "Update-MpSignature"', "Update Signatures"),
    ]
    
    defender_fixes = 0
    for cmd, desc in commands:
        if run_command(cmd, desc):
            defender_fixes += 1
    
    # Create BIOS instructions
    print("\n5. CREATING BIOS INSTRUCTIONS...")
    bios_instructions = """
BIOS CONFIGURATION REQUIRED
===========================

SECURE BOOT DISABLE:
1. Restart computer and enter BIOS (F2, F12, DEL, or ESC during boot)
2. Navigate to Security or Boot section
3. Find "Secure Boot" option
4. Set to "Disabled"
5. Save and exit BIOS

VIRTUALIZATION ENABLE:
1. In BIOS, go to Advanced or CPU Configuration
2. Find "Intel VT-x" or "AMD-V" or "Virtualization Technology"
3. Set to "Enabled"
4. Save and exit BIOS

MANUFACTURER LOCATIONS:
- ASUS: Advanced > CPU Configuration
- MSI: OC > CPU Features
- Gigabyte: M.I.T. > Advanced CPU Settings
- Dell: Virtualization Support
- HP: System Configuration

AFTER BIOS CHANGES:
1. Save settings and restart
2. Run Support Tools.exe again to verify
3. All checks should now pass
    """
    
    try:
        with open("BIOS_INSTRUCTIONS.txt", "w") as f:
            f.write(bios_instructions)
        print("  SUCCESS: BIOS instructions created")
    except:
        print("  FAILED: Could not create BIOS instructions")
    
    # Summary
    print("\n" + "=" * 40)
    print("FIX SUMMARY")
    print("=" * 40)
    print(f"Secure Boot fixes: {secure_boot_fixes}/4")
    print(f"Virtualization fixes: {virtualization_fixes}/4")
    print(f"Firewall fixes: {firewall_fixes}/5")
    print(f"Defender fixes: {defender_fixes}/5")
    
    total_fixes = secure_boot_fixes + virtualization_fixes + firewall_fixes + defender_fixes
    total_possible = 18
    
    print(f"\nTotal fixes applied: {total_fixes}/{total_possible}")
    
    if total_fixes >= 12:
        print("\nSUCCESS: Most issues should be resolved!")
        print("NEXT STEPS:")
        print("1. RESTART your computer")
        print("2. Enter BIOS and make manual changes (see BIOS_INSTRUCTIONS.txt)")
        print("3. Run Support Tools.exe to verify")
        
        restart = input("\nWould you like to restart now? (y/n): ").lower().strip()
        if restart == 'y':
            print("Restarting in 10 seconds...")
            os.system("shutdown /r /t 10")
        else:
            print("Please restart manually and check BIOS settings")
    else:
        print("\nWARNING: Some fixes failed")
        print("Manual intervention may be required")
        print("Check BIOS_INSTRUCTIONS.txt for manual steps")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
