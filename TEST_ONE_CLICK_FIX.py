#!/usr/bin/env python3
"""
Test One-Click Fix
Quick test to verify the one-click fix works
"""

import subprocess
import os

def test_one_click_fix():
    print("🎯 TESTING ONE-CLICK FIX")
    print("=" * 40)
    
    # Test 1: Check if batch file exists
    if os.path.exists("ONE_CLICK_FIX_EVERYTHING.bat"):
        print("✅ ONE_CLICK_FIX_EVERYTHING.bat exists")
    else:
        print("❌ ONE_CLICK_FIX_EVERYTHING.bat missing")
    
    # Test 2: Check if interface file exists
    if os.path.exists("main.py"):
        print("✅ main.py interface exists")
    else:
        print("❌ main.py interface missing")
    
    # Test 3: Check if method exists in main.py
    try:
        with open("main.py", "r") as f:
            content = f.read()
            if "def one_click_fix_everything" in content:
                print("✅ one_click_fix_everything method exists")
            else:
                print("❌ one_click_fix_everything method missing")
    except:
        print("❌ Could not read main.py")
    
    # Test 4: Check if BIOS guide exists
    if os.path.exists("SIMPLE_BIOS_STEPS.txt"):
        print("✅ SIMPLE_BIOS_STEPS.txt exists")
    else:
        print("⚠️ SIMPLE_BIOS_STEPS.txt will be created when needed")
    
    print()
    print("🎯 ONE-CLICK FIX TEST COMPLETE")
    print()
    print("✅ READY TO USE:")
    print("1. Run: python main.py")
    print("2. Click: 🎯 ONE-CLICK FIX ALL ISSUES")
    print("3. Follow prompts for BIOS changes")
    print()

if __name__ == "__main__":
    test_one_click_fix()
    input("Press Enter to exit...")
