# DISABLE_DEFENDER_TAMPER.ps1
# Aggressive Windows Defender disable targeting Tamper Protection

Write-Host "🛡️ AGGRESSIVE WINDOWS DEFENDER DISABLE" -ForegroundColor Red
Write-Host "=" * 50 -ForegroundColor Red
Write-Host ""

# Check admin privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ ERROR: Administrator privileges required!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Administrator privileges confirmed" -ForegroundColor Green
Write-Host ""

Write-Host "🚨 This will aggressively disable Windows Defender!" -ForegroundColor Yellow
Write-Host "Including attempts to bypass Tamper Protection" -ForegroundColor Yellow
Write-Host ""

$confirm = Read-Host "Type 'DISABLE' to proceed"
if ($confirm -ne "DISABLE") {
    Write-Host "Operation cancelled." -ForegroundColor Yellow
    exit 0
}

Write-Host ""
Write-Host "🚀 Starting aggressive Defender disable..." -ForegroundColor Cyan
Write-Host ""

# Step 1: Try to disable Tamper Protection first
Write-Host "🔒 ATTEMPTING TO DISABLE TAMPER PROTECTION..." -ForegroundColor Cyan
try {
    # Method 1: Registry disable
    Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows Defender\Features" -Name "TamperProtection" -Value 0 -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Tamper Protection Registry: DISABLED" -ForegroundColor Green
} catch {
    Write-Host "❌ Tamper Protection Registry: FAILED" -ForegroundColor Red
}

try {
    # Method 2: Group Policy disable
    $regPath = "HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Features"
    if (!(Test-Path $regPath)) {
        New-Item -Path $regPath -Force | Out-Null
    }
    Set-ItemProperty -Path $regPath -Name "TamperProtection" -Value 0 -Force
    Write-Host "✅ Tamper Protection Policy: DISABLED" -ForegroundColor Green
} catch {
    Write-Host "❌ Tamper Protection Policy: FAILED" -ForegroundColor Red
}

# Step 2: Disable Defender Services FIRST
Write-Host ""
Write-Host "🛑 STOPPING DEFENDER SERVICES..." -ForegroundColor Cyan

$services = @("WinDefend", "WdNisSvc", "SecurityHealthService", "Sense", "WdFilter", "WdBoot")
foreach ($service in $services) {
    try {
        # Stop service forcefully
        Stop-Service -Name $service -Force -ErrorAction SilentlyContinue
        Set-Service -Name $service -StartupType Disabled -ErrorAction SilentlyContinue
        
        # Registry disable as backup
        $servicePath = "HKLM:\SYSTEM\CurrentControlSet\Services\$service"
        if (Test-Path $servicePath) {
            Set-ItemProperty -Path $servicePath -Name "Start" -Value 4 -Force -ErrorAction SilentlyContinue
        }
        
        Write-Host "✅ Service $service: STOPPED & DISABLED" -ForegroundColor Green
    } catch {
        Write-Host "❌ Service $service: FAILED" -ForegroundColor Red
    }
}

# Step 3: Registry Nuclear Option
Write-Host ""
Write-Host "📝 REGISTRY NUCLEAR DISABLE..." -ForegroundColor Cyan

$registryChanges = @(
    @{Path="HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender"; Name="DisableAntiSpyware"; Value=1},
    @{Path="HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender"; Name="DisableRealtimeMonitoring"; Value=1},
    @{Path="HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection"; Name="DisableRealtimeMonitoring"; Value=1},
    @{Path="HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection"; Name="DisableBehaviorMonitoring"; Value=1},
    @{Path="HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection"; Name="DisableOnAccessProtection"; Value=1},
    @{Path="HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection"; Name="DisableScanOnRealtimeEnable"; Value=1},
    @{Path="HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Spynet"; Name="DisableBlockAtFirstSeen"; Value=1},
    @{Path="HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Spynet"; Name="SpynetReporting"; Value=0},
    @{Path="HKLM:\SOFTWARE\Policies\Microsoft\Windows Defender\Spynet"; Name="SubmitSamplesConsent"; Value=2}
)

foreach ($change in $registryChanges) {
    try {
        if (!(Test-Path $change.Path)) {
            New-Item -Path $change.Path -Force | Out-Null
        }
        Set-ItemProperty -Path $change.Path -Name $change.Name -Value $change.Value -Force
        Write-Host "✅ Registry: $($change.Path)\$($change.Name)" -ForegroundColor Green
    } catch {
        Write-Host "❌ Registry: $($change.Path)\$($change.Name)" -ForegroundColor Red
    }
}

# Step 4: PowerShell Preferences (Multiple attempts)
Write-Host ""
Write-Host "⚙️ POWERSHELL PREFERENCES DISABLE..." -ForegroundColor Cyan

$defenderCommands = @(
    "Set-MpPreference -DisableRealtimeMonitoring `$true",
    "Set-MpPreference -MAPSReporting Disabled",
    "Set-MpPreference -DisableBehaviorMonitoring `$true",
    "Set-MpPreference -DisableBlockAtFirstSeen `$true",
    "Set-MpPreference -DisableIOAVProtection `$true",
    "Set-MpPreference -DisableScriptScanning `$true",
    "Set-MpPreference -DisableArchiveScanning `$true",
    "Set-MpPreference -DisableEmailScanning `$true",
    "Set-MpPreference -DisableRemovableDriveScanning `$true",
    "Set-MpPreference -SubmitSamplesConsent NeverSend"
)

foreach ($cmd in $defenderCommands) {
    try {
        Invoke-Expression $cmd -ErrorAction SilentlyContinue
        Write-Host "✅ Command: $cmd" -ForegroundColor Green
    } catch {
        Write-Host "❌ Command: $cmd" -ForegroundColor Red
    }
}

# Step 5: Kill Defender Processes
Write-Host ""
Write-Host "💀 KILLING DEFENDER PROCESSES..." -ForegroundColor Cyan

$processes = @("MsMpEng", "NisSrv", "SecurityHealthSystray", "SecurityHealthService")
foreach ($proc in $processes) {
    try {
        Get-Process -Name $proc -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
        Write-Host "✅ Process $proc: KILLED" -ForegroundColor Green
    } catch {
        Write-Host "❌ Process $proc: NOT FOUND" -ForegroundColor Yellow
    }
}

# Step 6: Verification
Write-Host ""
Write-Host "🔍 VERIFICATION..." -ForegroundColor Cyan

try {
    $rtpStatus = Get-MpPreference | Select-Object -ExpandProperty DisableRealtimeMonitoring
    if ($rtpStatus -eq $true) {
        Write-Host "✅ Real-time Protection: DISABLED" -ForegroundColor Green
    } else {
        Write-Host "❌ Real-time Protection: STILL ACTIVE" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠️ Cannot verify Defender status (may be disabled)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=" * 50 -ForegroundColor Green
Write-Host "🎉 AGGRESSIVE DEFENDER DISABLE COMPLETED!" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green
Write-Host ""

Write-Host "⚠️ IMPORTANT NOTES:" -ForegroundColor Yellow
Write-Host "• Some changes require a RESTART to take effect" -ForegroundColor Yellow
Write-Host "• Tamper Protection may need manual disable in Windows Security" -ForegroundColor Yellow
Write-Host "• If still active, try Safe Mode disable" -ForegroundColor Yellow
Write-Host ""

$restart = Read-Host "Would you like to restart now? (y/n)"
if ($restart -eq "y" -or $restart -eq "Y") {
    Write-Host "Restarting in 10 seconds..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    Restart-Computer -Force
} else {
    Write-Host "Please restart manually for changes to take effect." -ForegroundColor Yellow
}

Read-Host "Press Enter to exit"
